package com.teammanage.dto.response;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 登录响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class LoginResponse {

    /**
     * 用户Token
     */
    private String token;

    /**
     * Token过期时间（毫秒）
     */
    private Long expiresIn;

    /**
     * 用户信息
     */
    private UserInfo user;

    /**
     * 团队列表
     */
    private List<TeamInfo> teams;

    /**
     * 当前选择的团队信息（用于团队选择响应）
     */
    private TeamInfo team;

    /**
     * 团队选择成功标识（用于团队选择响应）
     */
    private Boolean teamSelectionSuccess;

    // 手动添加getter/setter方法
    public String getToken() { return token; }
    public void setToken(String token) { this.token = token; }

    public Long getExpiresIn() { return expiresIn; }
    public void setExpiresIn(Long expiresIn) { this.expiresIn = expiresIn; }

    public UserInfo getUser() { return user; }
    public void setUser(UserInfo user) { this.user = user; }

    public List<TeamInfo> getTeams() { return teams; }
    public void setTeams(List<TeamInfo> teams) { this.teams = teams; }

    public TeamInfo getTeam() { return team; }
    public void setTeam(TeamInfo team) { this.team = team; }

    public Boolean getTeamSelectionSuccess() { return teamSelectionSuccess; }
    public void setTeamSelectionSuccess(Boolean teamSelectionSuccess) { this.teamSelectionSuccess = teamSelectionSuccess; }

    /**
     * 用户信息
     */
    public static class UserInfo {
        private Long id;
        private String email;
        private String name;

        // 手动添加getter/setter方法
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }

    /**
     * 团队信息
     */
    public static class TeamInfo {
        private Long id;
        private String name;
        private Boolean isCreator;
        private Integer memberCount;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastAccessTime;

        // 手动添加getter/setter方法
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public Boolean getIsCreator() { return isCreator; }
        public void setIsCreator(Boolean isCreator) { this.isCreator = isCreator; }

        public Integer getMemberCount() { return memberCount; }
        public void setMemberCount(Integer memberCount) { this.memberCount = memberCount; }

        public LocalDateTime getLastAccessTime() { return lastAccessTime; }
        public void setLastAccessTime(LocalDateTime lastAccessTime) { this.lastAccessTime = lastAccessTime; }
    }

}
