package com.teammanage.dto.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;


import java.util.List;

/**
 * 邀请成员请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

public class InviteMembersRequest {

    /**
     * 邀请的邮箱列表
     */
    @NotEmpty(message = "邮箱列表不能为空")
    @Size(max = 10, message = "一次最多邀请10个成员")
    private List<@Email(message = "邮箱格式不正确") String> emails;

    // 手动添加getter/setter方法
    public List<String> getEmails() { return emails; }
    public void setEmails(List<String> emails) { this.emails = emails; }

}
