package com.teammanage.dto.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * 从好友列表邀请成员请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class InviteFriendsRequest {

    /**
     * 好友ID列表
     */
    @NotEmpty(message = "好友ID列表不能为空")
    @Size(max = 50, message = "一次最多邀请50个好友")
    private List<Long> friendIds;

    // 手动添加getter/setter方法
    public List<Long> getFriendIds() { return friendIds; }
    public void setFriendIds(List<Long> friendIds) { this.friendIds = friendIds; }
}
