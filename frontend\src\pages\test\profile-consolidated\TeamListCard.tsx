import React, { useState, useEffect } from "react";
import {
  Card,
  Typography,
  Tooltip,
  List,
  Flex,
  Spin,
  Alert,
  Row,
  Col,
  message
} from "antd";
import { TeamService } from "@/services/team";
import { AuthService } from "@/services";
import { useModel, history } from '@umijs/max';
import type { TeamDetailResponse } from "@/types/api";
import {
  CarOutlined,
  TeamOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CrownOutlined,
  RightOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined
} from "@ant-design/icons";

const { Text, Title } = Typography;

// 添加内联样式
const styles = `
  .team-item .ant-card-body {
    padding: 0 !important;
  }

  .team-name-hover:hover {
    color: #1890ff !important;
  }

  .team-switch-icon {
    transition: transform 0.2s ease;
  }

  .team-name-hover:hover .team-switch-icon {
    transform: translateX(2px);
  }

  @media (max-width: 768px) {
    .team-item {
      margin-bottom: 12px;
    }

    .team-stats-grid {
      grid-template-columns: repeat(2, 1fr) !important;
      gap: 8px !important;
    }
  }

  @media (max-width: 576px) {
    .team-stats-grid {
      grid-template-columns: 1fr !important;
    }
  }
`;

const TeamListCard: React.FC = () => {
  // 团队列表状态管理
  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [switchingTeamId, setSwitchingTeamId] = useState<number | null>(null);

  const { initialState, setInitialState } = useModel('@@initialState');
  const currentTeam = initialState?.currentTeam;

  // 获取团队列表数据
  useEffect(() => {
    const fetchTeams = async () => {
      try {
        setLoading(true);
        setError(null);
        const teamsData = await TeamService.getUserTeamsWithStats();
        setTeams(teamsData);
      } catch (error) {
        console.error('获取团队列表失败:', error);
        setError('获取团队列表失败');
      } finally {
        setLoading(false);
      }
    };

    fetchTeams();
  }, []);

  // 团队切换处理函数
  const handleTeamSwitch = async (teamId: number, teamName: string) => {
    if (teamId === currentTeam?.id) {
      message.info('您已经在当前团队中');
      return;
    }

    try {
      setSwitchingTeamId(teamId);
      const response = await AuthService.selectTeam({ teamId });

      // 检查后端返回的团队选择成功标识
      if (response.teamSelectionSuccess && response.team && response.team.id === teamId) {
        message.success(`已切换到团队：${teamName}`);

        // 同步更新 initialState，等待更新完成后再跳转
        if (initialState?.fetchTeamInfo && initialState?.fetchUserInfo && setInitialState) {
          try {
            const [currentUser, currentTeam] = await Promise.all([
              initialState.fetchUserInfo(),
              initialState.fetchTeamInfo()
            ]);

            // 确保团队信息已正确获取
            if (currentTeam && currentTeam.id === teamId) {
              await setInitialState({
                ...initialState,
                currentUser,
                currentTeam,
              });

              // 等待 initialState 更新完成后再跳转到仪表盘
              setTimeout(() => {
                history.push('/dashboard');
              }, 100);
            } else {
              console.error('获取的团队信息与选择的团队不匹配');
              message.error('团队切换失败，请重试');
            }
          } catch (error) {
            console.error('更新 initialState 失败:', error);
            message.error('团队切换失败，请重试');
          }
        } else {
          // 如果没有 initialState 相关方法，直接跳转
          history.push('/dashboard');
        }
      } else {
        console.error('团队切换响应异常，未返回正确的团队信息');
        message.error('团队切换失败，请重试');
      }
    } catch (error) {
      console.error('团队切换失败:', error);
      message.error('团队切换失败');
    } finally {
      setSwitchingTeamId(null);
    }
  };

  return (
    <>
      {/* 注入样式 */}
      <style dangerouslySetInnerHTML={{ __html: styles }} />

      <Card
        className="dashboard-card"
        style={{
          borderRadius: 16,
          boxShadow: "0 6px 20px rgba(0,0,0,0.08)",
          border: "none",
          background: "linear-gradient(145deg, #ffffff, #f8faff)",
        }}
        title={
          <Flex justify="space-between" align="center">
            <Title level={4} style={{
              margin: 0,
              background: 'linear-gradient(135deg, #1890ff, #722ed1)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: 600
            }}>
              团队列表
            </Title>
          </Flex>
        }
      >
      {error ? (
        <Alert
          message="团队列表加载失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      ) : (
        <Spin spinning={loading}>
          <List
            dataSource={teams}
            renderItem={(item) => (
              <List.Item>
                <Card
                  className="team-item"
                  style={{
                    background: currentTeam?.id === item.id
                      ? "linear-gradient(135deg, #f0f9ff, #e6f4ff)"
                      : "#fff",
                    borderRadius: 16,
                    boxShadow: currentTeam?.id === item.id
                      ? "0 4px 16px rgba(24, 144, 255, 0.12)"
                      : "0 2px 8px rgba(0,0,0,0.06)",
                    width: "100%",
                    borderLeft: `5px solid ${item.isCreator ? "#722ed1" : "#52c41a"}`,
                    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                    border: currentTeam?.id === item.id
                      ? "1px solid #91caff"
                      : "1px solid #f0f0f0",
                    padding: "24px 28px",
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                  hoverable
                  onMouseEnter={(e) => {
                    if (currentTeam?.id !== item.id) {
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 8px 24px rgba(0,0,0,0.12)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (currentTeam?.id !== item.id) {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';
                    }
                  }}
                >
                  {/* 当前团队的装饰性背景 */}
                  {currentTeam?.id === item.id && (
                    <div style={{
                      position: 'absolute',
                      top: 0,
                      right: 0,
                      width: '100px',
                      height: '100px',
                      background: 'linear-gradient(135deg, rgba(24, 144, 255, 0.05), rgba(64, 169, 255, 0.02))',
                      borderRadius: '0 16px 0 100px',
                      pointerEvents: 'none'
                    }} />
                  )}
                  {/* 优化的卡片布局 */}
                  <Row justify="space-between" align="top" gutter={[20, 0]}>
                    {/* 左侧：团队基本信息 */}
                    <Col flex="1">
                      <Flex vertical gap={12}>
                        {/* 团队名称和状态 */}
                        <Flex align="center" justify="space-between" style={{ width: '100%' }}>
                          <Flex align="center" gap={12}>
                            {/* 团队名称 - 增强交互设计 */}
                            <div
                              style={{
                                position: 'relative',
                                cursor: currentTeam?.id === item.id ? 'default' : 'pointer',
                                padding: '4px 0'
                              }}
                              onClick={() => handleTeamSwitch(item.id, item.name)}
                            >
                              <Text
                                strong
                                style={{
                                  fontSize: 20,
                                  color: currentTeam?.id === item.id ? '#1890ff' : '#262626',
                                  lineHeight: 1.2,
                                  transition: 'all 0.2s ease',
                                  position: 'relative'
                                }}
                                className={currentTeam?.id !== item.id ? 'team-name-hover' : ''}
                              >
                                {item.name}
                              </Text>
                              {currentTeam?.id !== item.id && (
                                <RightOutlined
                                  style={{
                                    fontSize: 12,
                                    color: '#8c8c8c',
                                    marginLeft: 8,
                                    transition: 'all 0.2s ease'
                                  }}
                                  className="team-switch-icon"
                                />
                              )}
                            </div>

                            {/* 当前团队标识 - 重新设计 */}
                            {currentTeam?.id === item.id && (
                              <div style={{
                                background: 'linear-gradient(135deg, #1890ff, #40a9ff)',
                                color: 'white',
                                padding: '4px 12px',
                                borderRadius: 20,
                                fontSize: 12,
                                fontWeight: 600,
                                display: 'flex',
                                alignItems: 'center',
                                gap: 4,
                                boxShadow: '0 2px 6px rgba(24, 144, 255, 0.3)'
                              }}>
                                <CheckCircleOutlined style={{ fontSize: 12 }} />
                                当前团队
                              </div>
                            )}

                            {/* 加载状态 */}
                            {switchingTeamId === item.id && (
                              <div style={{
                                background: '#f0f0f0',
                                padding: '6px 12px',
                                borderRadius: 20,
                                display: 'flex',
                                alignItems: 'center',
                                gap: 6
                              }}>
                                <Spin size="small" />
                                <Text style={{ fontSize: 12, color: '#666' }}>切换中...</Text>
                              </div>
                            )}
                          </Flex>

                          {/* 团队角色标识 - 重新设计 */}
                          <div style={{
                            background: item.isCreator
                              ? 'linear-gradient(135deg, #722ed1, #9254de)'
                              : 'linear-gradient(135deg, #52c41a, #73d13d)',
                            color: 'white',
                            padding: '6px 14px',
                            borderRadius: 16,
                            fontSize: 12,
                            fontWeight: 600,
                            display: 'flex',
                            alignItems: 'center',
                            gap: 6,
                            boxShadow: item.isCreator
                              ? '0 2px 8px rgba(114, 46, 209, 0.3)'
                              : '0 2px 8px rgba(82, 196, 26, 0.3)',
                            minWidth: 'fit-content'
                          }}>
                            {item.isCreator ? (
                              <>
                                <CrownOutlined style={{ fontSize: 14 }} />
                                管理员
                              </>
                            ) : (
                              <>
                                <UserOutlined style={{ fontSize: 14 }} />
                                成员
                              </>
                            )}
                          </div>
                        </Flex>
                        
                        {/* 团队基本信息行 - 优化设计 */}
                        <Flex align="center" gap={32} style={{ marginTop: 8 }}>
                          <Tooltip title={`创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`}>
                            <Flex align="center" gap={8}>
                              <div style={{
                                background: 'linear-gradient(135deg, #f0f9ff, #e0f2fe)',
                                padding: '4px',
                                borderRadius: '6px',
                                display: 'flex',
                                alignItems: 'center'
                              }}>
                                <ClockCircleOutlined style={{ color: "#0ea5e9", fontSize: 14 }} />
                              </div>
                              <Text style={{ fontSize: 13, color: '#595959', fontWeight: 500 }}>
                                创建于 {new Date(item.createdAt).toLocaleDateString('zh-CN')}
                              </Text>
                            </Flex>
                          </Tooltip>

                          <Tooltip title={`团队成员: ${item.memberCount}人`}>
                            <Flex align="center" gap={8}>
                              <div style={{
                                background: 'linear-gradient(135deg, #f0f9ff, #e0f2fe)',
                                padding: '4px',
                                borderRadius: '6px',
                                display: 'flex',
                                alignItems: 'center'
                              }}>
                                <TeamOutlined style={{ color: "#0ea5e9", fontSize: 14 }} />
                              </div>
                              <Text style={{ fontSize: 13, color: '#595959', fontWeight: 500 }}>
                                {item.memberCount} 名成员
                              </Text>
                            </Flex>
                          </Tooltip>
                        </Flex>
                      </Flex>
                    </Col>

                    {/* 右侧：核心指标卡片 - 重新设计 */}
                    <Col flex="none">
                      <div style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(2, 1fr)',
                        gap: '12px',
                        minWidth: '200px'
                      }}>
                        {/* 车辆资源卡片 */}
                        <div style={{
                          background: 'linear-gradient(135deg, #e6f4ff, #f0f9ff)',
                          border: '1px solid #91caff',
                          borderRadius: 12,
                          padding: '12px',
                          textAlign: 'center',
                          transition: 'all 0.3s ease',
                          cursor: 'pointer',
                          position: 'relative',
                          overflow: 'hidden'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'translateY(-2px)';
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(24, 144, 255, 0.15)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = 'none';
                        }}>
                          <div style={{
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            width: '30px',
                            height: '30px',
                            background: 'linear-gradient(135deg, #1890ff, #40a9ff)',
                            borderRadius: '0 12px 0 20px',
                            opacity: 0.1
                          }} />
                          <Flex vertical align="center" gap={6}>
                            <div style={{
                              background: 'linear-gradient(135deg, #1890ff, #40a9ff)',
                              borderRadius: '50%',
                              width: '32px',
                              height: '32px',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              marginBottom: '4px'
                            }}>
                              <CarOutlined style={{ color: "white", fontSize: 16 }} />
                            </div>
                            <Text strong style={{ fontSize: 18, color: '#1890ff', lineHeight: 1 }}>
                              {item.stats?.vehicles || 0}
                            </Text>
                            <Text style={{ fontSize: 11, color: '#666', fontWeight: 500 }}>车辆资源</Text>
                          </Flex>
                        </div>

                        {/* 人员资源卡片 */}
                        <div style={{
                          background: 'linear-gradient(135deg, #f6ffed, #f0f9ff)',
                          border: '1px solid #95de64',
                          borderRadius: 12,
                          padding: '12px',
                          textAlign: 'center',
                          transition: 'all 0.3s ease',
                          cursor: 'pointer',
                          position: 'relative',
                          overflow: 'hidden'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'translateY(-2px)';
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(82, 196, 26, 0.15)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = 'none';
                        }}>
                          <div style={{
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            width: '30px',
                            height: '30px',
                            background: 'linear-gradient(135deg, #52c41a, #73d13d)',
                            borderRadius: '0 12px 0 20px',
                            opacity: 0.1
                          }} />
                          <Flex vertical align="center" gap={6}>
                            <div style={{
                              background: 'linear-gradient(135deg, #52c41a, #73d13d)',
                              borderRadius: '50%',
                              width: '32px',
                              height: '32px',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              marginBottom: '4px'
                            }}>
                              <UserOutlined style={{ color: "white", fontSize: 16 }} />
                            </div>
                            <Text strong style={{ fontSize: 18, color: '#52c41a', lineHeight: 1 }}>
                              {item.stats?.personnel || 0}
                            </Text>
                            <Text style={{ fontSize: 11, color: '#666', fontWeight: 500 }}>人员资源</Text>
                          </Flex>
                        </div>

                        {/* 临期事项卡片 */}
                        <div style={{
                          background: 'linear-gradient(135deg, #fff7e6, #fffbe6)',
                          border: '1px solid #ffd666',
                          borderRadius: 12,
                          padding: '12px',
                          textAlign: 'center',
                          transition: 'all 0.3s ease',
                          cursor: 'pointer',
                          position: 'relative',
                          overflow: 'hidden'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'translateY(-2px)';
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(250, 173, 20, 0.15)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = 'none';
                        }}>
                          <div style={{
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            width: '30px',
                            height: '30px',
                            background: 'linear-gradient(135deg, #faad14, #ffc53d)',
                            borderRadius: '0 12px 0 20px',
                            opacity: 0.1
                          }} />
                          <Flex vertical align="center" gap={6}>
                            <div style={{
                              background: 'linear-gradient(135deg, #faad14, #ffc53d)',
                              borderRadius: '50%',
                              width: '32px',
                              height: '32px',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              marginBottom: '4px'
                            }}>
                              <ExclamationCircleOutlined style={{ color: "white", fontSize: 16 }} />
                            </div>
                            <Text strong style={{ fontSize: 18, color: '#faad14', lineHeight: 1 }}>
                              {item.stats?.expiring || 0}
                            </Text>
                            <Text style={{ fontSize: 11, color: '#666', fontWeight: 500 }}>临期事项</Text>
                          </Flex>
                        </div>

                        {/* 逾期事项卡片 */}
                        <div style={{
                          background: 'linear-gradient(135deg, #fff1f0, #fff2f0)',
                          border: '1px solid #ffadd2',
                          borderRadius: 12,
                          padding: '12px',
                          textAlign: 'center',
                          transition: 'all 0.3s ease',
                          cursor: 'pointer',
                          position: 'relative',
                          overflow: 'hidden'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'translateY(-2px)';
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(255, 77, 79, 0.15)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = 'none';
                        }}>
                          <div style={{
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            width: '30px',
                            height: '30px',
                            background: 'linear-gradient(135deg, #ff4d4f, #ff7875)',
                            borderRadius: '0 12px 0 20px',
                            opacity: 0.1
                          }} />
                          <Flex vertical align="center" gap={6}>
                            <div style={{
                              background: 'linear-gradient(135deg, #ff4d4f, #ff7875)',
                              borderRadius: '50%',
                              width: '32px',
                              height: '32px',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              marginBottom: '4px'
                            }}>
                              <ExclamationCircleOutlined style={{ color: "white", fontSize: 16 }} />
                            </div>
                            <Text strong style={{ fontSize: 18, color: '#ff4d4f', lineHeight: 1 }}>
                              {item.stats?.overdue || 0}
                            </Text>
                            <Text style={{ fontSize: 11, color: '#666', fontWeight: 500 }}>逾期事项</Text>
                          </Flex>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </Card>
              </List.Item>
            )}
          />
        </Spin>
      )}
    </Card>
    </>
  );
};

export default TeamListCard;