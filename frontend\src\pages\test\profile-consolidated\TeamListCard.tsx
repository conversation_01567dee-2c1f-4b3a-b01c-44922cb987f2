import React, { useState, useEffect } from "react";
import {
  Card,
  Typography,
  Tooltip,
  List,
  Flex,
  Spin,
  Alert,
  Tag,
  Row,
  Col,
  message
} from "antd";
import { TeamService } from "@/services/team";
import { AuthService } from "@/services";
import { useModel, history } from '@umijs/max';
import type { TeamDetailResponse } from "@/types/api";
import {
  CalendarOutlined,
  CarOutlined,
  TeamOutlined,
  UserOutlined,
  WarningOutlined,
  ClockCircleOutlined,
  UserSwitchOutlined
} from "@ant-design/icons";

const { Text, Title } = Typography;

const TeamListCard: React.FC = () => {
  // 团队列表状态管理
  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [switchingTeamId, setSwitchingTeamId] = useState<number | null>(null);

  const { initialState, setInitialState } = useModel('@@initialState');
  const currentTeam = initialState?.currentTeam;

  // 获取团队列表数据
  useEffect(() => {
    const fetchTeams = async () => {
      try {
        setLoading(true);
        setError(null);
        const teamsData = await TeamService.getUserTeamsWithStats();
        setTeams(teamsData);
      } catch (error) {
        console.error('获取团队列表失败:', error);
        setError('获取团队列表失败');
      } finally {
        setLoading(false);
      }
    };

    fetchTeams();
  }, []);

  // 团队切换处理函数
  const handleTeamSwitch = async (teamId: number, teamName: string) => {
    if (teamId === currentTeam?.id) {
      message.info('您已经在当前团队中');
      return;
    }

    try {
      setSwitchingTeamId(teamId);
      const response = await AuthService.selectTeam({ teamId });

      // 检查后端返回的团队选择成功标识
      if (response.teamSelectionSuccess && response.team && response.team.id === teamId) {
        message.success(`已切换到团队：${teamName}`);

        // 同步更新 initialState，等待更新完成后再跳转
        if (initialState?.fetchTeamInfo && initialState?.fetchUserInfo && setInitialState) {
          try {
            const [currentUser, currentTeam] = await Promise.all([
              initialState.fetchUserInfo(),
              initialState.fetchTeamInfo()
            ]);

            // 确保团队信息已正确获取
            if (currentTeam && currentTeam.id === teamId) {
              await setInitialState({
                ...initialState,
                currentUser,
                currentTeam,
              });

              // 等待 initialState 更新完成后再跳转到仪表盘
              setTimeout(() => {
                history.push('/dashboard');
              }, 100);
            } else {
              console.error('获取的团队信息与选择的团队不匹配');
              message.error('团队切换失败，请重试');
            }
          } catch (error) {
            console.error('更新 initialState 失败:', error);
            message.error('团队切换失败，请重试');
          }
        } else {
          // 如果没有 initialState 相关方法，直接跳转
          history.push('/dashboard');
        }
      } else {
        console.error('团队切换响应异常，未返回正确的团队信息');
        message.error('团队切换失败，请重试');
      }
    } catch (error) {
      console.error('团队切换失败:', error);
      message.error('团队切换失败');
    } finally {
      setSwitchingTeamId(null);
    }
  };

  return (
    <Card
      className="dashboard-card"
      style={{
        borderRadius: 12,
        boxShadow: "0 4px 12px rgba(0,0,0,0.05)",
        border: "none",
        background: "linear-gradient(145deg, #ffffff, #f5f8ff)",
      }}
      title={
        <Flex justify="space-between" align="center">
          <Title level={5} style={{ margin: 0 }}>团队列表</Title>
        </Flex>
      }
    >
      {error ? (
        <Alert
          message="团队列表加载失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      ) : (
        <Spin spinning={loading}>
          <List
            dataSource={teams}
            renderItem={(item) => (
              <List.Item>
                <Card
                  className="team-item"
                  style={{
                    background: "#fff",
                    borderRadius: 8,
                    boxShadow: "0 1px 3px rgba(0,0,0,0.03)",
                    width: "100%",
                    borderLeft: `3px solid ${item.isCreator ? "#1890ff" : "#52c41a"}`,
                  }}
                  bodyStyle={{ padding: 16 }}
                >
                  {/* 使用Row和Col布局 */}
                  <Row justify="space-between" align="middle" gutter={[16, 0]}>
                    {/* 第一列：团队基本信息 */}
                    <Col>
                      <Flex vertical>
                        {/* 团队名称 - 可点击切换团队 */}
                        <Text
                          strong
                          style={{
                            fontSize: 16,
                            marginBottom: 8,
                            cursor: 'pointer',
                            color: currentTeam?.id === item.id ? '#1890ff' : '#000',
                            textDecoration: currentTeam?.id === item.id ? 'none' : 'underline'
                          }}
                          onClick={() => handleTeamSwitch(item.id, item.name)}
                        >
                          {item.name}
                          {currentTeam?.id === item.id && (
                            <Tag color="blue" style={{ marginLeft: 8, fontSize: 12 }}>
                              当前团队
                            </Tag>
                          )}
                          {switchingTeamId === item.id && (
                            <Spin size="small" style={{ marginLeft: 8 }} />
                          )}
                        </Text>
                        
                        {/* 创建时间和团队人数 */}
                        <Flex align="center" gap={16} style={{ marginBottom: 8 }}>
                          <Tooltip title={`创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`}>
                            <Flex align="center" gap={4}>
                              <ClockCircleOutlined style={{ color: "#666", fontSize: 14 }} />
                              <Text type="secondary" style={{ fontSize: 12 }}>
                                {new Date(item.createdAt).toLocaleDateString('zh-CN')}
                              </Text>
                            </Flex>
                          </Tooltip>
                          
                          <Tooltip title={`团队成员: ${item.memberCount}人`}>
                            <Flex align="center" gap={4}>
                              <TeamOutlined style={{ color: "#666", fontSize: 14 }} />
                              <Text type="secondary" style={{ fontSize: 12 }}>
                                {item.memberCount}人
                              </Text>
                            </Flex>
                          </Tooltip>
                        </Flex>
                        
                        {/* 身份和最后登录时间 */}
                        <Flex align="center" gap={16}>
                          <Tag 
                            color={item.isCreator ? "blue" : "green"}
                            icon={<UserSwitchOutlined />}
                            style={{ 
                              fontSize: 12,
                              margin: 0,
                              display: 'inline-flex',
                              alignItems: 'center',
                              height: 22
                            }}
                          >
                            {item.isCreator ? "管理员" : "成员"}
                          </Tag>
                          
                          <Tooltip title={`最后登录时间: ${new Date().toLocaleString('zh-CN')}`}>
                            <Flex align="center" gap={4}>
                              <CalendarOutlined style={{ color: "#666", fontSize: 14 }} />
                              <Text type="secondary" style={{ fontSize: 12 }}>
                                最后登录: {new Date().toLocaleString('zh-CN')}
                              </Text>
                            </Flex>
                          </Tooltip>
                        </Flex>
                      </Flex>
                    </Col>
                    
                    {/* 第二列：核心指标卡片 */}
                    <Col flex="auto">
                      <Row 
                        gutter={[8, 0]} 
                        style={{ width: '100%' }}
                        justify="space-between"
                      >
                        {/* 车辆资源卡片 */}
                        <Col>
                          <Card
                            size="small"
                            style={{ 
                              background: '#f0f7ff',
                              borderColor: '#d9e8ff',
                              borderRadius: 6,
                              minWidth: 80
                            }}
                            bodyStyle={{ padding: '6px 12px' }}
                          >
                            <Flex align="center" gap={6}>
                              <CarOutlined style={{ color: "#1890ff", fontSize: 16 }} />
                              <Text strong style={{ fontSize: 14 }}>{item.stats?.vehicles || 0}</Text>
                            </Flex>
                            <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>车辆资源</div>
                          </Card>
                        </Col>
                        
                        {/* 人员资源卡片 */}
                        <Col>
                          <Card
                            size="small"
                            style={{ 
                              background: '#f6ffed',
                              borderColor: '#d1f0be',
                              borderRadius: 6,
                              minWidth: 80
                            }}
                            bodyStyle={{ padding: '6px 12px' }}
                          >
                            <Flex align="center" gap={6}>
                              <UserOutlined style={{ color: "#52c41a", fontSize: 16 }} />
                              <Text strong style={{ fontSize: 14 }}>{item.stats?.personnel || 0}</Text>
                            </Flex>
                            <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>人员资源</div>
                          </Card>
                        </Col>
                        
                        {/* 临期事项卡片 */}
                        <Col>
                          <Card
                            size="small"
                            style={{ 
                              background: '#fff7e6',
                              borderColor: '#ffdfa6',
                              borderRadius: 6,
                              minWidth: 80
                            }}
                            bodyStyle={{ padding: '6px 12px' }}
                          >
                            <Flex align="center" gap={6}>
                              <WarningOutlined style={{ color: "#faad14", fontSize: 16 }} />
                              <Text strong style={{ fontSize: 14 }}>{item.stats?.expiring || 0}</Text>
                            </Flex>
                            <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>临期事项</div>
                          </Card>
                        </Col>
                        
                        {/* 逾期事项卡片 */}
                        <Col>
                          <Card
                            size="small"
                            style={{ 
                              background: '#fff1f0',
                              borderColor: '#ffccc7',
                              borderRadius: 6,
                              minWidth: 80
                            }}
                            bodyStyle={{ padding: '6px 12px' }}
                          >
                            <Flex align="center" gap={6}>
                              <WarningOutlined style={{ color: "#ff4d4f", fontSize: 16 }} />
                              <Text strong style={{ fontSize: 14 }}>{item.stats?.overdue || 0}</Text>
                            </Flex>
                            <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>逾期事项</div>
                          </Card>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                </Card>
              </List.Item>
            )}
          />
        </Spin>
      )}
    </Card>
  );
};

export default TeamListCard;