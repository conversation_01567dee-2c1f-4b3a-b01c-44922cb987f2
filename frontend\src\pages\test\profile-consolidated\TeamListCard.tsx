import React, { useState, useEffect } from "react";
import {
  Card,
  Typography,
  Tooltip,
  List,
  Flex,
  Spin,
  Alert,
  Tag,
  Row,
  Col,
  message
} from "antd";
import { TeamService } from "@/services/team";
import { AuthService } from "@/services";
import { useModel, history } from '@umijs/max';
import type { TeamDetailResponse } from "@/types/api";
import {
  CarOutlined,
  TeamOutlined,
  UserOutlined,
  WarningOutlined,
  ClockCircleOutlined,
  UserSwitchOutlined,
  CrownOutlined,
  StarFilled,
  RightOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined
} from "@ant-design/icons";

const { Text, Title } = Typography;

const TeamListCard: React.FC = () => {
  // 团队列表状态管理
  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [switchingTeamId, setSwitchingTeamId] = useState<number | null>(null);

  const { initialState, setInitialState } = useModel('@@initialState');
  const currentTeam = initialState?.currentTeam;

  // 获取团队列表数据
  useEffect(() => {
    const fetchTeams = async () => {
      try {
        setLoading(true);
        setError(null);
        const teamsData = await TeamService.getUserTeamsWithStats();
        setTeams(teamsData);
      } catch (error) {
        console.error('获取团队列表失败:', error);
        setError('获取团队列表失败');
      } finally {
        setLoading(false);
      }
    };

    fetchTeams();
  }, []);

  // 团队切换处理函数
  const handleTeamSwitch = async (teamId: number, teamName: string) => {
    if (teamId === currentTeam?.id) {
      message.info('您已经在当前团队中');
      return;
    }

    try {
      setSwitchingTeamId(teamId);
      const response = await AuthService.selectTeam({ teamId });

      // 检查后端返回的团队选择成功标识
      if (response.teamSelectionSuccess && response.team && response.team.id === teamId) {
        message.success(`已切换到团队：${teamName}`);

        // 同步更新 initialState，等待更新完成后再跳转
        if (initialState?.fetchTeamInfo && initialState?.fetchUserInfo && setInitialState) {
          try {
            const [currentUser, currentTeam] = await Promise.all([
              initialState.fetchUserInfo(),
              initialState.fetchTeamInfo()
            ]);

            // 确保团队信息已正确获取
            if (currentTeam && currentTeam.id === teamId) {
              await setInitialState({
                ...initialState,
                currentUser,
                currentTeam,
              });

              // 等待 initialState 更新完成后再跳转到仪表盘
              setTimeout(() => {
                history.push('/dashboard');
              }, 100);
            } else {
              console.error('获取的团队信息与选择的团队不匹配');
              message.error('团队切换失败，请重试');
            }
          } catch (error) {
            console.error('更新 initialState 失败:', error);
            message.error('团队切换失败，请重试');
          }
        } else {
          // 如果没有 initialState 相关方法，直接跳转
          history.push('/dashboard');
        }
      } else {
        console.error('团队切换响应异常，未返回正确的团队信息');
        message.error('团队切换失败，请重试');
      }
    } catch (error) {
      console.error('团队切换失败:', error);
      message.error('团队切换失败');
    } finally {
      setSwitchingTeamId(null);
    }
  };

  return (
    <Card
      className="dashboard-card"
      style={{
        borderRadius: 12,
        boxShadow: "0 4px 12px rgba(0,0,0,0.05)",
        border: "none",
        background: "linear-gradient(145deg, #ffffff, #f5f8ff)",
      }}
      title={
        <Flex justify="space-between" align="center">
          <Title level={5} style={{ margin: 0 }}>团队列表</Title>
        </Flex>
      }
    >
      {error ? (
        <Alert
          message="团队列表加载失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      ) : (
        <Spin spinning={loading}>
          <List
            dataSource={teams}
            renderItem={(item) => (
              <List.Item>
                <Card
                  className="team-item"
                  style={{
                    background: "#fff",
                    borderRadius: 12,
                    boxShadow: "0 2px 8px rgba(0,0,0,0.06)",
                    width: "100%",
                    borderLeft: `4px solid ${item.isCreator ? "#1890ff" : "#52c41a"}`,
                    transition: "all 0.3s ease",
                    border: "1px solid #f0f0f0",
                    padding: "20px 24px"
                  }}
                  hoverable
                >
                  {/* 优化的卡片布局 */}
                  <Row justify="space-between" align="top" gutter={[20, 0]}>
                    {/* 左侧：团队基本信息 */}
                    <Col flex="1">
                      <Flex vertical gap={12}>
                        {/* 团队名称和状态 */}
                        <Flex align="center" justify="space-between" style={{ width: '100%' }}>
                          <Flex align="center" gap={12}>
                            {/* 团队名称 - 增强交互设计 */}
                            <div
                              style={{
                                position: 'relative',
                                cursor: currentTeam?.id === item.id ? 'default' : 'pointer',
                                padding: '4px 0'
                              }}
                              onClick={() => handleTeamSwitch(item.id, item.name)}
                            >
                              <Text
                                strong
                                style={{
                                  fontSize: 20,
                                  color: currentTeam?.id === item.id ? '#1890ff' : '#262626',
                                  lineHeight: 1.2,
                                  transition: 'all 0.2s ease',
                                  position: 'relative'
                                }}
                                className={currentTeam?.id !== item.id ? 'team-name-hover' : ''}
                              >
                                {item.name}
                              </Text>
                              {currentTeam?.id !== item.id && (
                                <RightOutlined
                                  style={{
                                    fontSize: 12,
                                    color: '#8c8c8c',
                                    marginLeft: 8,
                                    transition: 'all 0.2s ease'
                                  }}
                                  className="team-switch-icon"
                                />
                              )}
                            </div>

                            {/* 当前团队标识 - 重新设计 */}
                            {currentTeam?.id === item.id && (
                              <div style={{
                                background: 'linear-gradient(135deg, #1890ff, #40a9ff)',
                                color: 'white',
                                padding: '4px 12px',
                                borderRadius: 20,
                                fontSize: 12,
                                fontWeight: 600,
                                display: 'flex',
                                alignItems: 'center',
                                gap: 4,
                                boxShadow: '0 2px 6px rgba(24, 144, 255, 0.3)'
                              }}>
                                <CheckCircleOutlined style={{ fontSize: 12 }} />
                                当前团队
                              </div>
                            )}

                            {/* 加载状态 */}
                            {switchingTeamId === item.id && (
                              <div style={{
                                background: '#f0f0f0',
                                padding: '6px 12px',
                                borderRadius: 20,
                                display: 'flex',
                                alignItems: 'center',
                                gap: 6
                              }}>
                                <Spin size="small" />
                                <Text style={{ fontSize: 12, color: '#666' }}>切换中...</Text>
                              </div>
                            )}
                          </Flex>

                          {/* 团队角色标识 - 重新设计 */}
                          <div style={{
                            background: item.isCreator
                              ? 'linear-gradient(135deg, #722ed1, #9254de)'
                              : 'linear-gradient(135deg, #52c41a, #73d13d)',
                            color: 'white',
                            padding: '6px 14px',
                            borderRadius: 16,
                            fontSize: 12,
                            fontWeight: 600,
                            display: 'flex',
                            alignItems: 'center',
                            gap: 6,
                            boxShadow: item.isCreator
                              ? '0 2px 8px rgba(114, 46, 209, 0.3)'
                              : '0 2px 8px rgba(82, 196, 26, 0.3)',
                            minWidth: 'fit-content'
                          }}>
                            {item.isCreator ? (
                              <>
                                <CrownOutlined style={{ fontSize: 14 }} />
                                管理员
                              </>
                            ) : (
                              <>
                                <UserOutlined style={{ fontSize: 14 }} />
                                成员
                              </>
                            )}
                          </div>
                        </Flex>
                        
                        {/* 团队基本信息行 */}
                        <Flex align="center" gap={24}>
                          <Tooltip title={`创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`}>
                            <Flex align="center" gap={6}>
                              <ClockCircleOutlined style={{ color: "#8c8c8c", fontSize: 14 }} />
                              <Text type="secondary" style={{ fontSize: 13 }}>
                                创建于 {new Date(item.createdAt).toLocaleDateString('zh-CN')}
                              </Text>
                            </Flex>
                          </Tooltip>

                          <Tooltip title={`团队成员: ${item.memberCount}人`}>
                            <Flex align="center" gap={6}>
                              <TeamOutlined style={{ color: "#8c8c8c", fontSize: 14 }} />
                              <Text type="secondary" style={{ fontSize: 13 }}>
                                {item.memberCount} 名成员
                              </Text>
                            </Flex>
                          </Tooltip>
                        </Flex>

                        {/* 用户身份标签 */}
                        <div>
                          <Tag
                            color={item.isCreator ? "blue" : "green"}
                            icon={<UserSwitchOutlined />}
                            style={{
                              fontSize: 12,
                              fontWeight: 500,
                              display: 'inline-flex',
                              alignItems: 'center',
                              height: 26,
                              paddingLeft: 10,
                              paddingRight: 10,
                              borderRadius: 6
                            }}
                          >
                            {item.isCreator ? "团队管理员" : "团队成员"}
                          </Tag>
                        </div>
                      </Flex>
                    </Col>

                    {/* 右侧：核心指标卡片 */}
                    <Col flex="none">
                      <Flex gap={12} wrap="wrap" justify="end">
                        {/* 车辆资源卡片 */}
                        <Card
                          size="small"
                          style={{
                            background: '#f0f7ff',
                            borderColor: '#d9e8ff',
                            borderRadius: 8,
                            minWidth: 90,
                            boxShadow: '0 1px 4px rgba(0,0,0,0.06)',
                            padding: '8px 12px',
                            textAlign: 'center'
                          }}
                        >
                          <Flex vertical align="center" gap={4}>
                            <Flex align="center" gap={6}>
                              <CarOutlined style={{ color: "#1890ff", fontSize: 18 }} />
                              <Text strong style={{ fontSize: 16, color: '#1890ff' }}>{item.stats?.vehicles || 0}</Text>
                            </Flex>
                            <Text style={{ fontSize: 12, color: '#666', lineHeight: 1 }}>车辆资源</Text>
                          </Flex>
                        </Card>

                        {/* 人员资源卡片 */}
                        <Card
                          size="small"
                          style={{
                            background: '#f6ffed',
                            borderColor: '#d1f0be',
                            borderRadius: 8,
                            minWidth: 90,
                            boxShadow: '0 1px 4px rgba(0,0,0,0.06)',
                            padding: '8px 12px',
                            textAlign: 'center'
                          }}
                        >
                          <Flex vertical align="center" gap={4}>
                            <Flex align="center" gap={6}>
                              <UserOutlined style={{ color: "#52c41a", fontSize: 18 }} />
                              <Text strong style={{ fontSize: 16, color: '#52c41a' }}>{item.stats?.personnel || 0}</Text>
                            </Flex>
                            <Text style={{ fontSize: 12, color: '#666', lineHeight: 1 }}>人员资源</Text>
                          </Flex>
                        </Card>

                        {/* 临期事项卡片 */}
                        <Card
                          size="small"
                          style={{
                            background: '#fff7e6',
                            borderColor: '#ffdfa6',
                            borderRadius: 8,
                            minWidth: 90,
                            boxShadow: '0 1px 4px rgba(0,0,0,0.06)',
                            padding: '8px 12px',
                            textAlign: 'center'
                          }}
                        >
                          <Flex vertical align="center" gap={4}>
                            <Flex align="center" gap={6}>
                              <WarningOutlined style={{ color: "#faad14", fontSize: 18 }} />
                              <Text strong style={{ fontSize: 16, color: '#faad14' }}>{item.stats?.expiring || 0}</Text>
                            </Flex>
                            <Text style={{ fontSize: 12, color: '#666', lineHeight: 1 }}>临期事项</Text>
                          </Flex>
                        </Card>

                        {/* 逾期事项卡片 */}
                        <Card
                          size="small"
                          style={{
                            background: '#fff1f0',
                            borderColor: '#ffccc7',
                            borderRadius: 8,
                            minWidth: 90,
                            boxShadow: '0 1px 4px rgba(0,0,0,0.06)',
                            padding: '8px 12px',
                            textAlign: 'center'
                          }}
                        >
                          <Flex vertical align="center" gap={4}>
                            <Flex align="center" gap={6}>
                              <WarningOutlined style={{ color: "#ff4d4f", fontSize: 18 }} />
                              <Text strong style={{ fontSize: 16, color: '#ff4d4f' }}>{item.stats?.overdue || 0}</Text>
                            </Flex>
                            <Text style={{ fontSize: 12, color: '#666', lineHeight: 1 }}>逾期事项</Text>
                          </Flex>
                        </Card>
                      </Flex>
                    </Col>
                  </Row>
                </Card>
              </List.Item>
            )}
          />
        </Spin>
      )}
    </Card>
  );
};

export default TeamListCard;