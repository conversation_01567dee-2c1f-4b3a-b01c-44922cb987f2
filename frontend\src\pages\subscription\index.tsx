/**
 * 订阅管理页面 - 统一的订阅管理界面
 */

import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import { PageContainer } from '@ant-design/pro-components';
import { SubscriptionService } from '@/services';
import type { SubscriptionResponse } from '@/types/api';

// 导入统一的订阅管理组件
import UnifiedSubscriptionContent from './components/UnifiedSubscriptionContent';

const SubscriptionPage: React.FC = () => {
  const [currentSubscription, setCurrentSubscription] = useState<SubscriptionResponse | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCurrentSubscription();
  }, []);

  const fetchCurrentSubscription = async () => {
    try {
      setLoading(true);
      const subscription = await SubscriptionService.getCurrentSubscription();
      setCurrentSubscription(subscription);
    } catch (error) {
      console.error('获取当前订阅失败:', error);
      message.error('获取订阅信息失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <PageContainer title="订阅管理">
      <UnifiedSubscriptionContent
        currentSubscription={currentSubscription}
        loading={loading}
        onRefresh={fetchCurrentSubscription}
      />
    </PageContainer>
  );
};

export default SubscriptionPage;
