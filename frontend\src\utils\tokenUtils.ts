/**
 * Token 解析工具
 * 用于直接从 JWT Token 中解析信息，避免依赖异步状态更新
 */

/**
 * 解析 JWT Token 的 payload
 * @param token JWT Token
 * @returns 解析后的 payload 对象
 */
function parseJwtPayload(token: string): any {
  try {
    // JWT Token 格式：header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format');
    }

    // 解码 payload (base64url)
    const payload = parts[1];
    // 处理 base64url 编码（替换 - 和 _ 字符，添加必要的 padding）
    const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');
    const paddedBase64 = base64 + '='.repeat((4 - base64.length % 4) % 4);
    
    // 解码并解析 JSON
    const decodedPayload = atob(paddedBase64);
    return JSON.parse(decodedPayload);
  } catch (error) {
    console.error('Failed to parse JWT payload:', error);
    return null;
  }
}

/**
 * 从当前 Token 中获取团队 ID
 * @returns 团队 ID，如果没有团队信息则返回 null
 */
export function getTeamIdFromCurrentToken(): number | null {
  try {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      return null;
    }

    const payload = parseJwtPayload(token);
    if (!payload) {
      return null;
    }

    return payload.teamId || null;
  } catch (error) {
    console.error('Failed to get team ID from token:', error);
    return null;
  }
}

/**
 * 从当前 Token 中获取用户 ID
 * @returns 用户 ID，如果没有用户信息则返回 null
 */
export function getUserIdFromCurrentToken(): number | null {
  try {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      return null;
    }

    const payload = parseJwtPayload(token);
    if (!payload) {
      return null;
    }

    return payload.userId || null;
  } catch (error) {
    console.error('Failed to get user ID from token:', error);
    return null;
  }
}

/**
 * 检查当前 Token 是否包含团队信息
 * @returns 是否包含团队信息
 */
export function hasTeamInCurrentToken(): boolean {
  const teamId = getTeamIdFromCurrentToken();
  return teamId !== null && teamId !== undefined;
}

/**
 * 从当前 Token 中获取是否为创建者
 * @returns 是否为创建者，如果没有团队信息则返回 null
 */
export function getIsCreatorFromCurrentToken(): boolean | null {
  try {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      return null;
    }

    const payload = parseJwtPayload(token);
    if (!payload) {
      return null;
    }

    return payload.isCreator || null;
  } catch (error) {
    console.error('Failed to get isCreator from token:', error);
    return null;
  }
}

/**
 * 获取当前 Token 的完整信息
 * @returns Token 中的所有信息
 */
export function getCurrentTokenInfo(): {
  userId: number | null;
  teamId: number | null;
  isCreator: boolean | null;
  email: string | null;
  name: string | null;
} {
  try {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      return {
        userId: null,
        teamId: null,
        isCreator: null,
        email: null,
        name: null,
      };
    }

    const payload = parseJwtPayload(token);
    if (!payload) {
      return {
        userId: null,
        teamId: null,
        isCreator: null,
        email: null,
        name: null,
      };
    }

    return {
      userId: payload.userId || null,
      teamId: payload.teamId || null,
      isCreator: payload.isCreator || null,
      email: payload.email || null,
      name: payload.name || null,
    };
  } catch (error) {
    console.error('Failed to get token info:', error);
    return {
      userId: null,
      teamId: null,
      isCreator: null,
      email: null,
      name: null,
    };
  }
}
