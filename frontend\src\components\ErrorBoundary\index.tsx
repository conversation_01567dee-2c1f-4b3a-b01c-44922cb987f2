/**
 * 错误边界组件
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Result, Button, Typography, Card } from 'antd';
import { BugOutlined, ReloadOutlined, HomeOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';

const { Paragraph, Text } = Typography;

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 你同样可以将错误日志上报给服务器
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });

    // 这里可以添加错误上报逻辑
    this.reportError(error, errorInfo);
  }

  reportError = (error: Error, errorInfo: ErrorInfo) => {
    // 上报错误到监控系统
    try {
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
      };

      // 这里可以调用错误上报API
      console.log('Error Report:', errorReport);
      
      // 示例：发送到错误监控服务
      // fetch('/api/error-report', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorReport),
      // });
    } catch (reportError) {
      console.error('Failed to report error:', reportError);
    }
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    history.push('/');
  };

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义的 fallback UI，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认的错误 UI
      return (
        <div style={{ padding: '50px', minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
          <Card style={{ maxWidth: 800, margin: '0 auto' }}>
            <Result
              status="error"
              icon={<BugOutlined />}
              title="页面出现了错误"
              subTitle="抱歉，页面遇到了意外错误。我们已经记录了这个问题，请尝试刷新页面或返回首页。"
              extra={[
                <Button type="primary" icon={<ReloadOutlined />} onClick={this.handleReload} key="reload">
                  刷新页面
                </Button>,
                <Button icon={<HomeOutlined />} onClick={this.handleGoHome} key="home">
                  返回首页
                </Button>,
                <Button onClick={this.handleReset} key="reset">
                  重试
                </Button>,
              ]}
            >
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <div style={{ textAlign: 'left', marginTop: 24 }}>
                  <Typography>
                    <Paragraph>
                      <Text strong>错误详情（开发环境）：</Text>
                    </Paragraph>
                    <Paragraph>
                      <Text code>{this.state.error.message}</Text>
                    </Paragraph>
                    {this.state.error.stack && (
                      <Paragraph>
                        <Text strong>错误堆栈：</Text>
                        <pre style={{ 
                          background: '#f6f8fa', 
                          padding: 16, 
                          borderRadius: 6,
                          overflow: 'auto',
                          fontSize: 12,
                          lineHeight: 1.4
                        }}>
                          {this.state.error.stack}
                        </pre>
                      </Paragraph>
                    )}
                    {this.state.errorInfo?.componentStack && (
                      <Paragraph>
                        <Text strong>组件堆栈：</Text>
                        <pre style={{ 
                          background: '#f6f8fa', 
                          padding: 16, 
                          borderRadius: 6,
                          overflow: 'auto',
                          fontSize: 12,
                          lineHeight: 1.4
                        }}>
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </Paragraph>
                    )}
                  </Typography>
                </div>
              )}
            </Result>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
