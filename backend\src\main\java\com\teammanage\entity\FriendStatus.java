package com.teammanage.entity;

/**
 * 好友关系状态枚举
 * 
 * <AUTHOR>
 * @since 1.1.0
 */
public enum FriendStatus {
    /**
     * 待处理 - 好友请求已发送，等待对方同意
     */
    PENDING(1, "待处理"),

    /**
     * 已接受 - 好友请求已被接受，建立好友关系
     */
    ACCEPTED(2, "已接受"),

    /**
     * 已拒绝 - 好友请求被拒绝
     */
    REJECTED(3, "已拒绝");

    private final Integer code;
    private final String description;

    FriendStatus(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举值
     *
     * @param code 状态代码
     * @return 对应的枚举值
     */
    public static FriendStatus fromCode(Integer code) {
        for (FriendStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown friend status code: " + code);
    }

    @Override
    public String toString() {
        return String.valueOf(code);
    }
}
