{"version": 3, "sources": ["src/pages/subscription/index.tsx"], "sourcesContent": ["/**\n * 订阅管理页面 - 统一的订阅管理界面\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { message } from 'antd';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { SubscriptionService } from '@/services';\nimport type { SubscriptionResponse } from '@/types/api';\n\n// 导入统一的订阅管理组件\nimport UnifiedSubscriptionContent from './components/UnifiedSubscriptionContent';\n\nconst SubscriptionPage: React.FC = () => {\n  const [currentSubscription, setCurrentSubscription] = useState<SubscriptionResponse | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchCurrentSubscription();\n  }, []);\n\n  const fetchCurrentSubscription = async () => {\n    try {\n      setLoading(true);\n      const subscription = await SubscriptionService.getCurrentSubscription();\n      setCurrentSubscription(subscription);\n    } catch (error) {\n      console.error('获取当前订阅失败:', error);\n      message.error('获取订阅信息失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <PageContainer title=\"订阅管理\">\n      <UnifiedSubscriptionContent\n        currentSubscription={currentSubscription}\n        loading={loading}\n        onRefresh={fetchCurrentSubscription}\n      />\n    </PageContainer>\n  );\n};\n\nexport default SubscriptionPage;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BA2CD;;;eAAA;;;;;;;wEAzC2C;6BACnB;sCACM;iCACM;4FAIG;;;;;;;;;;AAEvC,MAAM,mBAA6B;;IACjC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,IAAA,eAAQ,EAA8B;IAC5F,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IAEvC,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,2BAA2B;QAC/B,IAAI;YACF,WAAW;YACX,MAAM,eAAe,MAAM,6BAAmB,CAAC,sBAAsB;YACrE,uBAAuB;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,2BAAC,4BAAa;QAAC,OAAM;kBACnB,cAAA,2BAAC,mCAA0B;YACzB,qBAAqB;YACrB,SAAS;YACT,WAAW;;;;;;;;;;;AAInB;GA9BM;KAAA;IAgCN,WAAe"}