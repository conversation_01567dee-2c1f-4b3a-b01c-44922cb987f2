-- 创建TODO表
CREATE TABLE todos (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'TODO ID',
    title VARCHAR(255) NOT NULL COMMENT '任务标题',
    description TEXT COMMENT '任务描述',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '完成状态：0-未完成，1-已完成',
    priority TINYINT NOT NULL DEFAULT 2 COMMENT '优先级：1-低，2-中，3-高',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='TODO表';

-- 插入一些示例数据
INSERT INTO todos (title, description, status, priority, user_id) VALUES
('完善个人资料信息', '更新个人基本信息和联系方式', 1, 1, 1),
('驾驶证信息完善', '上传驾驶证照片并填写相关信息', 1, 2, 1),
('设置提醒收藏', '配置重要事项的提醒功能', 1, 1, 1),
('车辆保养记录更新', '更新所有车辆的保养记录和下次保养时间', 0, 2, 1),
('团队会议准备', '准备下周团队会议的议程和材料', 0, 3, 1),
('车辆安全检查报告', '完成本月车辆安全检查并提交报告', 0, 3, 1);
