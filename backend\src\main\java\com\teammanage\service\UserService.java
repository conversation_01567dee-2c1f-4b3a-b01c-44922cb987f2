package com.teammanage.service;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.teammanage.dto.request.UpdateUserProfileRequest;
import com.teammanage.dto.response.UserProfileResponse;
import com.teammanage.entity.Account;
import com.teammanage.exception.BusinessException;
import com.teammanage.exception.ResourceNotFoundException;
import com.teammanage.mapper.AccountMapper;
import com.teammanage.util.PasswordUtil;

/**
 * 用户管理服务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class UserService {

    private static final Logger log = LoggerFactory.getLogger(UserService.class);

    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private PasswordUtil passwordUtil;

    /**
     * 根据ID获取用户信息
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    public Account getUserById(Long userId) {
        Account account = accountMapper.selectById(userId);
        if (account == null) {
            throw new ResourceNotFoundException("用户不存在");
        }
        return account;
    }

    /**
     * 根据邮箱获取用户信息
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    public Account getUserByEmail(String email) {
        Account account = accountMapper.findByEmail(email);
        if (account == null) {
            throw new ResourceNotFoundException("用户不存在");
        }
        return account;
    }

    /**
     * 获取用户个人资料
     * 
     * @param userId 用户ID
     * @return 用户个人资料
     */
    public UserProfileResponse getUserProfile(Long userId) {
        Account account = getUserById(userId);
        
        UserProfileResponse response = new UserProfileResponse();
        response.setId(account.getId());
        response.setEmail(account.getEmail());
        response.setName(account.getName());
        response.setDefaultSubscriptionPlanId(account.getDefaultSubscriptionPlanId());
        response.setCreatedAt(account.getCreatedAt());
        response.setUpdatedAt(account.getUpdatedAt());
        
        return response;
    }

    /**
     * 更新用户个人资料
     * 
     * @param userId 用户ID
     * @param request 更新请求
     * @return 更新后的用户资料
     */
    @Transactional
    public UserProfileResponse updateUserProfile(Long userId, UpdateUserProfileRequest request) {
        Account account = getUserById(userId);
        
        // 更新用户名
        if (StringUtils.hasText(request.getName())) {
            account.setName(request.getName());
        }
        
        // 更新密码
        if (StringUtils.hasText(request.getNewPassword())) {
            // 验证旧密码
            if (!StringUtils.hasText(request.getCurrentPassword())) {
                throw new BusinessException("修改密码时必须提供当前密码");
            }
            
            if (!passwordUtil.matches(request.getCurrentPassword(), account.getPasswordHash())) {
                throw new BusinessException("当前密码不正确");
            }
            
            // 设置新密码
            account.setPasswordHash(passwordUtil.encode(request.getNewPassword()));
        }
        
        // 更新数据库
        accountMapper.updateById(account);
        
        log.info("用户资料更新成功: userId={}, name={}", userId, account.getName());
        
        return getUserProfile(userId);
    }

    /**
     * 检查邮箱是否已存在
     * 
     * @param email 邮箱
     * @return 是否存在
     */
    public boolean isEmailExists(String email) {
        return accountMapper.existsByEmail(email);
    }

    /**
     * 验证用户密码
     *
     * @param userId 用户ID
     * @param password 密码
     * @return 是否正确
     */
    public boolean validatePassword(Long userId, String password) {
        Account account = getUserById(userId);
        return passwordUtil.matches(password, account.getPasswordHash());
    }

    /**
     * 根据邮箱搜索用户（用于添加好友）
     *
     * @param email 邮箱关键词
     * @param currentUserId 当前用户ID（排除自己）
     * @return 用户列表
     */
    public List<Account> searchUsersByEmail(String email, Long currentUserId) {
        if (!StringUtils.hasText(email)) {
            return List.of(); // 返回空列表
        }

        // 搜索用户，排除当前用户
        List<Account> users = accountMapper.searchByEmail(email.trim(), currentUserId);

        log.debug("邮箱搜索用户: email={}, currentUserId={}, resultCount={}",
                email, currentUserId, users.size());

        return users;
    }

    /**
     * 修改用户密码
     * 
     * @param userId 用户ID
     * @param currentPassword 当前密码
     * @param newPassword 新密码
     */
    @Transactional
    public void changePassword(Long userId, String currentPassword, String newPassword) {
        Account account = getUserById(userId);
        
        // 验证当前密码
        if (!passwordUtil.matches(currentPassword, account.getPasswordHash())) {
            throw new BusinessException("当前密码不正确");
        }
        
        // 设置新密码
        account.setPasswordHash(passwordUtil.encode(newPassword));
        accountMapper.updateById(account);
        
        log.info("用户密码修改成功: userId={}", userId);
    }

    /**
     * 更新用户默认订阅套餐
     * 
     * @param userId 用户ID
     * @param planId 套餐ID
     */
    @Transactional
    public void updateDefaultSubscriptionPlan(Long userId, Long planId) {
        Account account = getUserById(userId);
        account.setDefaultSubscriptionPlanId(planId);
        accountMapper.updateById(account);
        
        log.info("用户默认套餐更新成功: userId={}, planId={}", userId, planId);
    }

}
