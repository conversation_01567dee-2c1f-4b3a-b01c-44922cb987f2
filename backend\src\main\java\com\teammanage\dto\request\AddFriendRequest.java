package com.teammanage.dto.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;

/**
 * 添加好友请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class AddFriendRequest {

    /**
     * 好友邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    // 手动添加getter/setter方法
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
}
