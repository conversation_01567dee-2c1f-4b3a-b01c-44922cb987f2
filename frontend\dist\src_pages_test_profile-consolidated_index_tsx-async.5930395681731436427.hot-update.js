globalThis.makoModuleHotUpdate('src/pages/test/profile-consolidated/index.tsx', {
    modules: {
        "src/pages/test/profile-consolidated/TodoManagement.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _todo = __mako_require__("src/services/todo.ts");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text } = _antd.Typography;
            const { TabPane } = _antd.Tabs;
            const TodoManagement = (props)=>{
                _s();
                // TODO数据状态管理
                const [personalTasks, setPersonalTasks] = (0, _react.useState)([]);
                const [todoStats, setTodoStats] = (0, _react.useState)({
                    highPriorityCount: 0,
                    mediumPriorityCount: 0,
                    lowPriorityCount: 0,
                    totalCount: 0,
                    completedCount: 0,
                    completionPercentage: 0
                });
                const [loading, setLoading] = (0, _react.useState)(true);
                const [error, setError] = (0, _react.useState)(null);
                // 待办事项状态管理
                const [todoModalVisible, setTodoModalVisible] = (0, _react.useState)(false);
                const [todoForm] = _antd.Form.useForm();
                const [editingTodoId, setEditingTodoId] = (0, _react.useState)(null);
                // 过滤器状态
                const [activeTab, setActiveTab] = (0, _react.useState)("pending");
                const [searchText, setSearchText] = (0, _react.useState)("");
                // 获取TODO数据
                (0, _react.useEffect)(()=>{
                    const fetchTodoData = async ()=>{
                        try {
                            setLoading(true);
                            setError(null);
                            console.log('TodoManagement: 开始获取TODO数据');
                            // 分别获取TODO列表和统计数据，避免一个失败影响另一个
                            const todosPromise = _todo.TodoService.getUserTodos().catch((error)=>{
                                console.error('获取TODO列表失败:', error);
                                return [];
                            });
                            const statsPromise = _todo.TodoService.getTodoStats().catch((error)=>{
                                console.error('获取TODO统计失败:', error);
                                return {
                                    highPriorityCount: 0,
                                    mediumPriorityCount: 0,
                                    lowPriorityCount: 0,
                                    totalCount: 0,
                                    completedCount: 0,
                                    completionPercentage: 0
                                };
                            });
                            const [todos, stats] = await Promise.all([
                                todosPromise,
                                statsPromise
                            ]);
                            console.log('TodoManagement: 获取到TODO列表:', todos);
                            console.log('TodoManagement: 获取到统计数据:', stats);
                            setPersonalTasks(todos);
                            setTodoStats(stats);
                        } catch (error) {
                            console.error('获取TODO数据时发生未知错误:', error);
                            setError('获取TODO数据失败，请刷新页面重试');
                        } finally{
                            setLoading(false);
                        }
                    };
                    fetchTodoData();
                }, []);
                // 根据激活的标签和搜索文本过滤任务
                const filteredPersonalTasks = personalTasks.filter((task)=>{
                    // 根据标签过滤
                    if (activeTab === "pending" && task.status === 1) return false;
                    if (activeTab === "completed" && task.status === 0) return false;
                    // 根据搜索文本过滤
                    if (searchText && !task.title.toLowerCase().includes(searchText.toLowerCase())) return false;
                    return true;
                });
                // 处理待办事项操作
                const handleToggleTodoStatus = async (id)=>{
                    try {
                        const task = personalTasks.find((t)=>t.id === id);
                        if (!task) {
                            _antd.message.error('任务不存在');
                            return;
                        }
                        const newStatus = task.status === 0 ? 1 : 0;
                        console.log(`TodoManagement: 更新任务状态 ${id} -> ${newStatus}`);
                        await _todo.TodoService.updateTodo(id, {
                            status: newStatus
                        });
                        // 更新本地状态
                        setPersonalTasks(personalTasks.map((task)=>task.id === id ? {
                                ...task,
                                status: newStatus
                            } : task));
                        // 刷新统计数据
                        try {
                            const stats = await _todo.TodoService.getTodoStats();
                            setTodoStats(stats);
                        } catch (statsError) {
                            console.error('刷新统计数据失败:', statsError);
                        // 统计数据刷新失败不影响主要操作
                        }
                        _antd.message.success(newStatus === 1 ? '任务已完成' : '任务已标记为未完成');
                    } catch (error) {
                        console.error('更新任务状态失败:', error);
                        _antd.message.error('更新任务状态失败，请稍后重试');
                    }
                };
                const handleAddOrUpdateTodo = async (values)=>{
                    try {
                        console.log('TodoManagement: 保存任务', {
                            editingTodoId,
                            values
                        });
                        if (editingTodoId) {
                            // 更新现有待办事项
                            const updatedTodo = await _todo.TodoService.updateTodo(editingTodoId, {
                                title: values.name,
                                priority: values.priority
                            });
                            setPersonalTasks(personalTasks.map((task)=>task.id === editingTodoId ? updatedTodo : task));
                            _antd.message.success('任务更新成功');
                        } else {
                            // 添加新待办事项
                            const newTodo = await _todo.TodoService.createTodo({
                                title: values.name,
                                priority: values.priority
                            });
                            setPersonalTasks([
                                newTodo,
                                ...personalTasks
                            ]);
                            _antd.message.success('任务创建成功');
                        }
                        // 刷新统计数据
                        try {
                            const stats = await _todo.TodoService.getTodoStats();
                            setTodoStats(stats);
                        } catch (statsError) {
                            console.error('刷新统计数据失败:', statsError);
                        // 统计数据刷新失败不影响主要操作
                        }
                        // 重置表单并关闭模态框
                        setTodoModalVisible(false);
                        setEditingTodoId(null);
                        todoForm.resetFields();
                    } catch (error) {
                        console.error('保存任务失败:', error);
                        const action = editingTodoId ? '更新' : '创建';
                        _antd.message.error(`${action}任务失败，请检查网络连接后重试`);
                    }
                };
                const handleDeleteTodo = async (id)=>{
                    try {
                        console.log('TodoManagement: 删除任务', id);
                        await _todo.TodoService.deleteTodo(id);
                        setPersonalTasks(personalTasks.filter((task)=>task.id !== id));
                        // 刷新统计数据
                        try {
                            const stats = await _todo.TodoService.getTodoStats();
                            setTodoStats(stats);
                        } catch (statsError) {
                            console.error('刷新统计数据失败:', statsError);
                        // 统计数据刷新失败不影响主要操作
                        }
                        _antd.message.success('任务删除成功');
                    } catch (error) {
                        console.error('删除任务失败:', error);
                        _antd.message.error('删除任务失败，请稍后重试');
                    }
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    className: "dashboard-card",
                    style: {
                        borderRadius: 12,
                        boxShadow: "0 4px 12px rgba(0,0,0,0.05)",
                        border: "none",
                        background: "linear-gradient(145deg, #ffffff, #f5f8ff)"
                    },
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                        justify: "space-between",
                        align: "center",
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            strong: true,
                            children: "待办事项"
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                            lineNumber: 251,
                            columnNumber: 11
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                        lineNumber: 250,
                        columnNumber: 9
                    }, void 0),
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                            justify: "space-between",
                            align: "center",
                            style: {
                                marginBottom: 16,
                                gap: 12,
                                flexWrap: "wrap",
                                padding: "12px 16px",
                                background: "#fafbfc",
                                borderRadius: 8,
                                border: "1px solid #f0f0f0"
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                    align: "center",
                                    gap: 12,
                                    style: {
                                        flex: 1,
                                        minWidth: 280
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.Search, {
                                            placeholder: "搜索任务...",
                                            allowClear: true,
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                lineNumber: 274,
                                                columnNumber: 21
                                            }, void 0),
                                            value: searchText,
                                            onChange: (e)=>setSearchText(e.target.value),
                                            style: {
                                                flex: 1,
                                                maxWidth: 300
                                            },
                                            size: "middle"
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 271,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                lineNumber: 286,
                                                columnNumber: 19
                                            }, void 0),
                                            onClick: ()=>{
                                                setEditingTodoId(null);
                                                todoForm.resetFields();
                                                setTodoModalVisible(true);
                                            },
                                            style: {
                                                background: "#1890ff",
                                                borderColor: "#1890ff",
                                                boxShadow: "0 2px 4px rgba(24, 144, 255, 0.3)",
                                                fontWeight: 500,
                                                minWidth: 80
                                            },
                                            size: "middle",
                                            children: "新增"
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 284,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 270,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                    align: "center",
                                    gap: 16,
                                    style: {
                                        flexShrink: 0
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            size: 12,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                    title: `高优先级任务: ${todoStats.highPriorityCount}个`,
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        align: "center",
                                                        gap: 6,
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    width: 8,
                                                                    height: 8,
                                                                    borderRadius: "50%",
                                                                    background: "#ff4d4f"
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 311,
                                                                columnNumber: 17
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    fontSize: 13,
                                                                    fontWeight: 500,
                                                                    color: "#262626"
                                                                },
                                                                children: [
                                                                    "高: ",
                                                                    todoStats.highPriorityCount
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 319,
                                                                columnNumber: 17
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 310,
                                                        columnNumber: 15
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 309,
                                                    columnNumber: 13
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                    title: `中优先级任务: ${todoStats.mediumPriorityCount}个`,
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        align: "center",
                                                        gap: 6,
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    width: 8,
                                                                    height: 8,
                                                                    borderRadius: "50%",
                                                                    background: "#faad14"
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 327,
                                                                columnNumber: 17
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    fontSize: 13,
                                                                    fontWeight: 500,
                                                                    color: "#262626"
                                                                },
                                                                children: [
                                                                    "中: ",
                                                                    todoStats.mediumPriorityCount
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 335,
                                                                columnNumber: 17
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 326,
                                                        columnNumber: 15
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 325,
                                                    columnNumber: 13
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                    title: `低优先级任务: ${todoStats.lowPriorityCount}个`,
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        align: "center",
                                                        gap: 6,
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    width: 8,
                                                                    height: 8,
                                                                    borderRadius: "50%",
                                                                    background: "#52c41a"
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 343,
                                                                columnNumber: 17
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    fontSize: 13,
                                                                    fontWeight: 500,
                                                                    color: "#262626"
                                                                },
                                                                children: [
                                                                    "低: ",
                                                                    todoStats.lowPriorityCount
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 351,
                                                                columnNumber: 17
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 342,
                                                        columnNumber: 15
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 341,
                                                    columnNumber: 13
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 308,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                            type: "vertical",
                                            style: {
                                                height: 20,
                                                backgroundColor: "#d9d9d9"
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 358,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                            title: `完成率: ${todoStats.completionPercentage}% (${todoStats.completedCount}/${todoStats.totalCount})`,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                align: "center",
                                                gap: 8,
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 13,
                                                            fontWeight: 500,
                                                            color: "#595959"
                                                        },
                                                        children: "完成率:"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 363,
                                                        columnNumber: 15
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                                                        percent: todoStats.completionPercentage,
                                                        size: "small",
                                                        style: {
                                                            width: 100
                                                        },
                                                        strokeColor: "#52c41a",
                                                        showInfo: false
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 366,
                                                        columnNumber: 15
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 13,
                                                            fontWeight: 600,
                                                            color: "#262626"
                                                        },
                                                        children: [
                                                            todoStats.completionPercentage,
                                                            "%"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 373,
                                                        columnNumber: 15
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                lineNumber: 362,
                                                columnNumber: 13
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 361,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 306,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                            lineNumber: 256,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                            activeKey: activeTab,
                            onChange: (key)=>setActiveTab(key),
                            size: "middle",
                            style: {
                                marginBottom: 8
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                                    tab: "全部"
                                }, "all", false, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 390,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                                    tab: "待处理"
                                }, "pending", false, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 391,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                                    tab: "已完成"
                                }, "completed", false, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 392,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                            lineNumber: 382,
                            columnNumber: 7
                        }, this),
                        error ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                            message: "TODO数据加载失败",
                            description: error,
                            type: "error",
                            showIcon: true,
                            style: {
                                marginBottom: 16
                            }
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                            lineNumber: 397,
                            columnNumber: 9
                        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            spinning: loading,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                                    dataSource: filteredPersonalTasks,
                                    renderItem: (item)=>{
                                        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                            className: "todo-item",
                                            style: {
                                                padding: "10px 16px",
                                                marginBottom: 12,
                                                borderRadius: 8,
                                                background: "#fff",
                                                opacity: item.status === 1 ? 0.7 : 1,
                                                borderLeft: `3px solid ${item.status === 1 ? "#52c41a" : item.priority === 3 ? "#ff4d4f" : item.priority === 2 ? "#faad14" : "#8c8c8c"}`,
                                                boxShadow: "0 1px 4px rgba(0,0,0,0.05)"
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                align: "center",
                                                gap: 12,
                                                style: {
                                                    width: "100%"
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        vertical: true,
                                                        align: "center",
                                                        children: [
                                                            item.status === 1 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                align: "center",
                                                                justify: "center",
                                                                style: {
                                                                    width: 22,
                                                                    height: 22,
                                                                    borderRadius: "50%",
                                                                    background: "#52c41a"
                                                                },
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                                    style: {
                                                                        color: "#fff",
                                                                        fontSize: 12
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                    lineNumber: 444,
                                                                    columnNumber: 23
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 434,
                                                                columnNumber: 21
                                                            }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    width: 18,
                                                                    height: 18,
                                                                    borderRadius: "50%",
                                                                    border: `2px solid ${item.priority === 3 ? "#ff4d4f" : item.priority === 2 ? "#faad14" : "#8c8c8c"}`
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 449,
                                                                columnNumber: 21
                                                            }, void 0),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    width: 2,
                                                                    height: 24,
                                                                    background: "#f0f0f0",
                                                                    marginTop: 4
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 465,
                                                                columnNumber: 19
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 432,
                                                        columnNumber: 17
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        vertical: true,
                                                        style: {
                                                            flex: 1
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    fontSize: 14,
                                                                    fontWeight: item.priority === 3 ? 500 : "normal",
                                                                    textDecoration: item.status === 1 ? "line-through" : "none",
                                                                    color: item.status === 1 ? "#8c8c8c" : "#262626"
                                                                },
                                                                children: item.title
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 477,
                                                                columnNumber: 19
                                                            }, void 0),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                align: "center",
                                                                size: 6,
                                                                style: {
                                                                    marginTop: 4
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                                                        style: {
                                                                            fontSize: 12,
                                                                            color: "#8c8c8c"
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                        lineNumber: 493,
                                                                        columnNumber: 21
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        type: "secondary",
                                                                        style: {
                                                                            fontSize: 12
                                                                        },
                                                                        children: [
                                                                            "创建于: ",
                                                                            new Date(item.createdAt).toLocaleDateString('zh-CN')
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                        lineNumber: 499,
                                                                        columnNumber: 21
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 492,
                                                                columnNumber: 19
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 476,
                                                        columnNumber: 17
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                                                        trigger: [
                                                            'click'
                                                        ],
                                                        menu: {
                                                            items: [
                                                                {
                                                                    key: 'complete',
                                                                    label: item.status === 1 ? '标记未完成' : '标记完成',
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                                        style: {
                                                                            color: item.status === 1 ? '#8c8c8c' : '#52c41a',
                                                                            fontSize: 14
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                        lineNumber: 514,
                                                                        columnNumber: 27
                                                                    }, void 0)
                                                                },
                                                                {
                                                                    key: 'edit',
                                                                    label: '编辑任务',
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {
                                                                        style: {
                                                                            color: '#8c8c8c'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                        lineNumber: 525,
                                                                        columnNumber: 31
                                                                    }, void 0)
                                                                },
                                                                {
                                                                    key: 'delete',
                                                                    label: '删除任务',
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {
                                                                        style: {
                                                                            color: '#ff4d4f'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                        lineNumber: 530,
                                                                        columnNumber: 31
                                                                    }, void 0),
                                                                    danger: true
                                                                }
                                                            ],
                                                            onClick: ({ key })=>{
                                                                if (key === "complete") handleToggleTodoStatus(item.id);
                                                                else if (key === "edit") {
                                                                    setEditingTodoId(item.id);
                                                                    todoForm.setFieldsValue({
                                                                        name: item.title,
                                                                        priority: item.priority
                                                                    });
                                                                    setTodoModalVisible(true);
                                                                } else if (key === "delete") handleDeleteTodo(item.id);
                                                            }
                                                        },
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                            type: "text",
                                                            size: "small",
                                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MoreOutlined, {}, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 553,
                                                                columnNumber: 27
                                                            }, void 0),
                                                            style: {
                                                                width: 32,
                                                                height: 32
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                            lineNumber: 550,
                                                            columnNumber: 19
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 506,
                                                        columnNumber: 17
                                                    }, void 0)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                lineNumber: 430,
                                                columnNumber: 15
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 410,
                                            columnNumber: 13
                                        }, void 0);
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 406,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                                    title: editingTodoId ? "编辑待办事项" : "新增待办事项",
                                    open: todoModalVisible,
                                    onCancel: ()=>{
                                        setTodoModalVisible(false);
                                        todoForm.resetFields();
                                    },
                                    onOk: ()=>{
                                        todoForm.submit();
                                    },
                                    centered: true,
                                    destroyOnClose: true,
                                    footer: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            onClick: ()=>setTodoModalVisible(false),
                                            children: "取消"
                                        }, "cancel", false, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 577,
                                            columnNumber: 11
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            onClick: ()=>{
                                                todoForm.submit();
                                            },
                                            style: {
                                                background: "#1890ff",
                                                borderColor: "#1890ff",
                                                boxShadow: "0 2px 4px rgba(24, 144, 255, 0.3)"
                                            },
                                            children: editingTodoId ? "更新任务" : "创建任务"
                                        }, "submit", false, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 580,
                                            columnNumber: 11
                                        }, void 0)
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                        form: todoForm,
                                        layout: "vertical",
                                        onFinish: handleAddOrUpdateTodo,
                                        autoComplete: "off",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                name: "name",
                                                label: "任务名称",
                                                rules: [
                                                    {
                                                        required: true,
                                                        message: "请输入任务名称"
                                                    }
                                                ],
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                    placeholder: "请输入任务名称",
                                                    size: "large",
                                                    style: {
                                                        borderRadius: 6
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 607,
                                                    columnNumber: 13
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                lineNumber: 602,
                                                columnNumber: 11
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                name: "priority",
                                                label: "优先级",
                                                initialValue: 2,
                                                rules: [
                                                    {
                                                        required: true,
                                                        message: "请选择优先级"
                                                    }
                                                ],
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                                    size: "large",
                                                    options: [
                                                        {
                                                            value: 3,
                                                            label: "高优先级"
                                                        },
                                                        {
                                                            value: 2,
                                                            label: "中优先级"
                                                        },
                                                        {
                                                            value: 1,
                                                            label: "低优先级"
                                                        }
                                                    ],
                                                    style: {
                                                        borderRadius: 6
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 620,
                                                    columnNumber: 13
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                lineNumber: 614,
                                                columnNumber: 11
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                        lineNumber: 596,
                                        columnNumber: 9
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 564,
                                    columnNumber: 7
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                            lineNumber: 405,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                    lineNumber: 241,
                    columnNumber: 5
                }, this);
            };
            _s(TodoManagement, "HHPhC8rlUAXUyFFDx+yADUZ6q1M=", false, function() {
                return [
                    _antd.Form.useForm
                ];
            });
            _c = TodoManagement;
            var _default = TodoManagement;
            var _c;
            $RefreshReg$(_c, "TodoManagement");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '8167965954817876332';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "common",
            "p__subscription__index"
        ],
        "src/pages/team/detail/index.tsx": [
            "common",
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "p__team__index"
        ],
        "src/pages/test/profile-consolidated/index.tsx": [
            "src/pages/test/profile-consolidated/index.tsx"
        ],
        "src/pages/user/index.tsx": [
            "common",
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_test_profile-consolidated_index_tsx-async.5930395681731436427.hot-update.js.map