/**
 * 用户资料页面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  Typography,
  message,
  Avatar,
  Upload
} from 'antd';
import {
  UserOutlined,
  EditOutlined,
  MailOutlined,
  SaveOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { UserService } from '@/services';
import type { UserProfileResponse, UpdateUserProfileRequest } from '@/types/api';

const { Title, Text } = Typography;

const UserProfilePage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfileResponse | null>(null);
  const [editMode, setEditMode] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchUserProfile();
  }, []);

  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      const profile = await UserService.getUserProfile();
      setUserProfile(profile);
      form.setFieldsValue({
        name: profile.name,
        email: profile.email,
      });
    } catch (error) {
      console.error('获取用户资料失败:', error);
      message.error('获取用户资料失败');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateProfile = async (values: { name: string }) => {
    try {
      setUpdating(true);
      const request: UpdateUserProfileRequest = {
        name: values.name,
      };
      
      const updatedProfile = await UserService.updateUserProfile(request);
      setUserProfile(updatedProfile);
      setEditMode(false);
      message.success('用户资料更新成功');
    } catch (error) {
      console.error('更新用户资料失败:', error);
    } finally {
      setUpdating(false);
    }
  };



  const handleCancelEdit = () => {
    setEditMode(false);
    if (userProfile) {
      form.setFieldsValue({
        name: userProfile.name,
        email: userProfile.email,
      });
    }
  };

  if (loading || !userProfile) {
    return (
      <PageContainer>
        <Card loading={loading} />
      </PageContainer>
    );
  }

  return (
    <PageContainer title="个人资料">
      <Card>
        <div style={{ display: 'flex', alignItems: 'flex-start', gap: 24 }}>
          {/* 头像部分 */}
          <div style={{ textAlign: 'center' }}>
            <Avatar size={120} icon={<UserOutlined />} />
            <div style={{ marginTop: 16 }}>
              <Upload
                showUploadList={false}
                beforeUpload={() => {
                  message.info('头像上传功能暂未实现');
                  return false;
                }}
              >
                <Button icon={<UploadOutlined />} size="small">
                  更换头像
                </Button>
              </Upload>
            </div>
          </div>

          {/* 资料表单 */}
          <div style={{ flex: 1 }}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleUpdateProfile}
              disabled={!editMode}
            >
              <Form.Item
                label="用户名"
                name="name"
                rules={[
                  { required: true, message: '请输入用户名！' },
                  { max: 100, message: '用户名长度不能超过100字符！' },
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="请输入用户名"
                  disabled={!editMode}
                />
              </Form.Item>

              <Form.Item
                label="邮箱"
                name="email"
              >
                <Input
                  prefix={<MailOutlined />}
                  disabled
                  placeholder="邮箱地址"
                />
              </Form.Item>

              <Form.Item label="注册时间">
                <Input
                  value={new Date(userProfile.createdAt).toLocaleString()}
                  disabled
                />
              </Form.Item>

              <Form.Item label="最后更新">
                <Input
                  value={new Date(userProfile.updatedAt).toLocaleString()}
                  disabled
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  {editMode ? (
                    <>
                      <Button
                        type="primary"
                        htmlType="submit"
                        loading={updating}
                        icon={<SaveOutlined />}
                      >
                        保存
                      </Button>
                      <Button onClick={handleCancelEdit}>
                        取消
                      </Button>
                    </>
                  ) : (
                    <Button
                      type="primary"
                      icon={<EditOutlined />}
                      onClick={() => setEditMode(true)}
                    >
                      编辑资料
                    </Button>
                  )}
                </Space>
              </Form.Item>
            </Form>


          </div>
        </div>
      </Card>


    </PageContainer>
  );
};

export default UserProfilePage;
