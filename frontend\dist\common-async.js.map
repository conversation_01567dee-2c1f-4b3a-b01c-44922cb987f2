{"version": 3, "sources": ["src/pages/subscription/components/UnifiedSubscriptionContent.tsx", "src/pages/team/detail/components/TeamDetailContent.tsx", "src/pages/team/detail/components/TeamMemberList.tsx", "src/pages/user/components/UserProfileContent.tsx"], "sourcesContent": ["/**\n * 统一订阅管理内容组件\n * 整合订阅详情和套餐选择功能\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  Card,\n  Descriptions, \n  Button, \n  Space, \n  Typography, \n  Tag, \n  Progress,\n  message,\n  Modal,\n  Table,\n  Alert,\n  Empty,\n  Row,\n  Col,\n  List,\n  InputNumber,\n  Divider\n} from 'antd';\nimport {\n  CrownOutlined,\n  UpOutlined,\n  ReloadOutlined,\n  StopOutlined,\n  HistoryOutlined,\n  CheckOutlined,\n  StarOutlined,\n  ShoppingCartOutlined\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { SubscriptionService } from '@/services';\nimport type { \n  SubscriptionResponse, \n  SubscriptionPlanResponse, \n  CreateSubscriptionRequest \n} from '@/types/api';\nimport { SubscriptionStatus } from '@/types/api';\n\nconst { Title, Text } = Typography;\n\ninterface UnifiedSubscriptionContentProps {\n  currentSubscription: SubscriptionResponse | null;\n  loading: boolean;\n  onRefresh: () => void;\n}\n\nconst UnifiedSubscriptionContent: React.FC<UnifiedSubscriptionContentProps> = ({\n  currentSubscription,\n  loading,\n  onRefresh\n}) => {\n  // 订阅详情相关状态\n  const [subscriptionHistory, setSubscriptionHistory] = useState<SubscriptionResponse[]>([]);\n  const [usageInfo, setUsageInfo] = useState<any>(null);\n  const [historyModalVisible, setHistoryModalVisible] = useState(false);\n\n  // 套餐选择相关状态\n  const [plans, setPlans] = useState<SubscriptionPlanResponse[]>([]);\n  const [plansLoading, setPlansLoading] = useState(true);\n  const [subscribing, setSubscribing] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlanResponse | null>(null);\n  const [subscribeModalVisible, setSubscribeModalVisible] = useState(false);\n  const [duration, setDuration] = useState(1);\n\n  useEffect(() => {\n    fetchPlans();\n    if (currentSubscription) {\n      fetchSubscriptionHistory();\n      fetchUsageInfo();\n    }\n  }, [currentSubscription]);\n\n  // 获取套餐列表\n  const fetchPlans = async () => {\n    try {\n      setPlansLoading(true);\n      const plansData = await SubscriptionService.getActivePlans();\n      setPlans(plansData);\n    } catch (error) {\n      console.error('获取套餐列表失败:', error);\n      message.error('获取套餐列表失败');\n    } finally {\n      setPlansLoading(false);\n    }\n  };\n\n  // 获取订阅历史\n  const fetchSubscriptionHistory = async () => {\n    try {\n      const history = await SubscriptionService.getSubscriptionHistory();\n      setSubscriptionHistory(history);\n    } catch (error) {\n      console.error('获取订阅历史失败:', error);\n    }\n  };\n\n  // 获取使用情况\n  const fetchUsageInfo = async () => {\n    try {\n      const usage = await SubscriptionService.getUsageInfo();\n      setUsageInfo(usage);\n    } catch (error) {\n      console.error('获取使用情况失败:', error);\n    }\n  };\n\n  // 处理订阅\n  const handleSubscribe = async () => {\n    if (!selectedPlan) return;\n\n    try {\n      setSubscribing(true);\n      const request: CreateSubscriptionRequest = {\n        planId: selectedPlan.id,\n        duration: duration\n      };\n      \n      await SubscriptionService.createSubscription(request);\n      message.success('订阅成功！');\n      setSubscribeModalVisible(false);\n      onRefresh();\n    } catch (error) {\n      console.error('订阅失败:', error);\n      message.error('订阅失败，请稍后重试');\n    } finally {\n      setSubscribing(false);\n    }\n  };\n\n  // 取消订阅\n  const handleCancelSubscription = async () => {\n    if (!currentSubscription) return;\n\n    Modal.confirm({\n      title: '确认取消订阅',\n      content: '取消订阅后，您将失去当前套餐的所有权益。确定要取消吗？',\n      okText: '确认取消',\n      cancelText: '保留订阅',\n      okType: 'danger',\n      onOk: async () => {\n        try {\n          await SubscriptionService.cancelSubscription(currentSubscription.id);\n          message.success('订阅已取消');\n          onRefresh();\n        } catch (error) {\n          console.error('取消订阅失败:', error);\n          message.error('取消订阅失败');\n        }\n      }\n    });\n  };\n\n  // 获取状态标签\n  const getStatusTag = (status: SubscriptionStatus) => {\n    const statusConfig = {\n      [SubscriptionStatus.ACTIVE]: { color: 'green', text: '有效' },\n      [SubscriptionStatus.EXPIRED]: { color: 'red', text: '已过期' },\n      [SubscriptionStatus.CANCELED]: { color: 'default', text: '已取消' },\n      [SubscriptionStatus.PENDING]: { color: 'orange', text: '待激活' }\n    };\n    \n    const config = statusConfig[status] || { color: 'default', text: '未知' };\n    return <Tag color={config.color}>{config.text}</Tag>;\n  };\n\n  // 获取套餐推荐标签\n  const getPlanRecommendation = (plan: SubscriptionPlanResponse) => {\n    if (plan.name === '标准版') {\n      return <Tag color=\"orange\" icon={<StarOutlined />}>推荐</Tag>;\n    }\n    if (plan.name === '企业版') {\n      return <Tag color=\"gold\" icon={<CrownOutlined />}>热门</Tag>;\n    }\n    return null;\n  };\n\n  // 套餐特性列表\n  const getPlanFeatures = (plan: SubscriptionPlanResponse) => {\n    const features = [\n      `可创建 ${plan.maxSize === 999999 ? '无限' : plan.maxSize} 个团队`,\n      '团队成员无限制',\n      '数据安全保障',\n      '7x24小时技术支持'\n    ];\n\n    if (plan.name !== '免费版') {\n      features.push('优先客服支持');\n    }\n    if (plan.name === '企业版') {\n      features.push('定制化服务');\n      features.push('专属客户经理');\n    }\n\n    return features;\n  };\n\n  // 历史记录表格列定义\n  const historyColumns: ColumnsType<SubscriptionResponse> = [\n    {\n      title: '套餐名称',\n      dataIndex: 'planName',\n      key: 'planName',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status: SubscriptionStatus) => getStatusTag(status),\n    },\n    {\n      title: '开始时间',\n      dataIndex: 'startDate',\n      key: 'startDate',\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '结束时间',\n      dataIndex: 'endDate',\n      key: 'endDate',\n      render: (date: string) => date ? new Date(date).toLocaleDateString() : '永久',\n    },\n    {\n      title: '价格',\n      dataIndex: 'price',\n      key: 'price',\n      render: (price: number) => `¥${price.toFixed(2)}`,\n    }\n  ];\n\n  return (\n    <div>\n      {/* 当前订阅状态 */}\n      <Card \n        title={\n          <Space>\n            <CrownOutlined />\n            当前订阅状态\n          </Space>\n        }\n        extra={\n          <Button \n            icon={<ReloadOutlined />} \n            onClick={onRefresh}\n            loading={loading}\n          >\n            刷新\n          </Button>\n        }\n        style={{ marginBottom: 24 }}\n      >\n        {currentSubscription ? (\n          <div>\n            <Descriptions column={2} bordered>\n              <Descriptions.Item label=\"套餐名称\">\n                <Space>\n                  {currentSubscription.planName}\n                  {getStatusTag(currentSubscription.status)}\n                </Space>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"团队限制\">\n                {currentSubscription.maxSize === 999999 ? '无限制' : `${currentSubscription.maxSize} 个`}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"开始时间\">\n                {new Date(currentSubscription.startDate).toLocaleDateString()}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"结束时间\">\n                {currentSubscription.endDate \n                  ? new Date(currentSubscription.endDate).toLocaleDateString() \n                  : '永久有效'\n                }\n              </Descriptions.Item>\n              <Descriptions.Item label=\"月费\">\n                ¥{currentSubscription.price.toFixed(2)}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"剩余天数\">\n                {usageInfo?.remainingDays !== undefined \n                  ? `${usageInfo.remainingDays} 天` \n                  : '计算中...'\n                }\n              </Descriptions.Item>\n            </Descriptions>\n\n            {usageInfo && (\n              <div style={{ marginTop: 16 }}>\n                <Text strong>团队使用情况：</Text>\n                <Progress\n                  percent={usageInfo.usagePercentage}\n                  format={() => `${usageInfo.currentUsage}/${usageInfo.maxUsage === 999999 ? '∞' : usageInfo.maxUsage}`}\n                  style={{ marginTop: 8 }}\n                />\n              </div>\n            )}\n\n            <div style={{ marginTop: 16 }}>\n              <Space>\n                <Button \n                  type=\"primary\" \n                  icon={<UpOutlined />}\n                  onClick={() => setSubscribeModalVisible(true)}\n                >\n                  升级套餐\n                </Button>\n                <Button \n                  icon={<HistoryOutlined />}\n                  onClick={() => setHistoryModalVisible(true)}\n                >\n                  查看历史\n                </Button>\n                {currentSubscription.status === SubscriptionStatus.ACTIVE && (\n                  <Button \n                    danger \n                    icon={<StopOutlined />}\n                    onClick={handleCancelSubscription}\n                  >\n                    取消订阅\n                  </Button>\n                )}\n              </Space>\n            </div>\n          </div>\n        ) : (\n          <Empty \n            description=\"暂无有效订阅\"\n            image={Empty.PRESENTED_IMAGE_SIMPLE}\n          >\n            <Button \n              type=\"primary\" \n              icon={<ShoppingCartOutlined />}\n              onClick={() => setSubscribeModalVisible(true)}\n            >\n              立即订阅\n            </Button>\n          </Empty>\n        )}\n      </Card>\n\n      {/* 套餐选择 */}\n      <Card \n        title={\n          <Space>\n            <ShoppingCartOutlined />\n            选择套餐\n          </Space>\n        }\n        loading={plansLoading}\n      >\n        <Row gutter={[16, 16]}>\n          {plans.map((plan) => (\n            <Col xs={24} sm={12} lg={6} key={plan.id}>\n              <Card\n                hoverable\n                className={`plan-card ${currentSubscription?.planId === plan.id ? 'current-plan' : ''}`}\n                actions={[\n                  <Button\n                    key=\"subscribe\"\n                    type={currentSubscription?.planId === plan.id ? 'default' : 'primary'}\n                    disabled={currentSubscription?.planId === plan.id}\n                    onClick={() => {\n                      setSelectedPlan(plan);\n                      setSubscribeModalVisible(true);\n                    }}\n                  >\n                    {currentSubscription?.planId === plan.id ? '当前套餐' : '选择此套餐'}\n                  </Button>\n                ]}\n              >\n                <div style={{ textAlign: 'center' }}>\n                  <Title level={4}>\n                    {plan.name}\n                    {getPlanRecommendation(plan)}\n                  </Title>\n                  <div style={{ fontSize: 32, fontWeight: 'bold', color: '#1890ff' }}>\n                    ¥{plan.price.toFixed(0)}\n                    <span style={{ fontSize: 14, color: '#666' }}>/月</span>\n                  </div>\n                  <Text type=\"secondary\">{plan.description}</Text>\n                </div>\n                \n                <Divider />\n                \n                <List\n                  size=\"small\"\n                  dataSource={getPlanFeatures(plan)}\n                  renderItem={(feature) => (\n                    <List.Item>\n                      <Space>\n                        <CheckOutlined style={{ color: '#52c41a' }} />\n                        {feature}\n                      </Space>\n                    </List.Item>\n                  )}\n                />\n              </Card>\n            </Col>\n          ))}\n        </Row>\n      </Card>\n\n      {/* 订阅确认弹窗 */}\n      <Modal\n        title=\"确认订阅\"\n        open={subscribeModalVisible}\n        onOk={handleSubscribe}\n        onCancel={() => setSubscribeModalVisible(false)}\n        confirmLoading={subscribing}\n        okText=\"确认订阅\"\n        cancelText=\"取消\"\n      >\n        {selectedPlan && (\n          <div>\n            <Alert\n              message={`您选择了 ${selectedPlan.name}`}\n              description={selectedPlan.description}\n              type=\"info\"\n              style={{ marginBottom: 16 }}\n            />\n            \n            <div style={{ marginBottom: 16 }}>\n              <Text strong>订阅时长：</Text>\n              <InputNumber\n                min={1}\n                max={12}\n                value={duration}\n                onChange={(value) => setDuration(value || 1)}\n                addonAfter=\"个月\"\n                style={{ marginLeft: 8 }}\n              />\n            </div>\n            \n            <div>\n              <Text strong>总费用：</Text>\n              <Text style={{ fontSize: 18, color: '#1890ff', marginLeft: 8 }}>\n                ¥{(selectedPlan.price * duration).toFixed(2)}\n              </Text>\n            </div>\n          </div>\n        )}\n      </Modal>\n\n      {/* 订阅历史弹窗 */}\n      <Modal\n        title=\"订阅历史\"\n        open={historyModalVisible}\n        onCancel={() => setHistoryModalVisible(false)}\n        footer={null}\n        width={800}\n      >\n        <Table\n          columns={historyColumns}\n          dataSource={subscriptionHistory}\n          rowKey=\"id\"\n          pagination={{ pageSize: 10 }}\n        />\n      </Modal>\n    </div>\n  );\n};\n\nexport default UnifiedSubscriptionContent;\n", "/**\n * 团队详情组件 - 增强模式显示\n */\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Statistic,\n  Button,\n  Space,\n  Typography,\n  Avatar,\n  Tag,\n  Progress,\n  Badge,\n  message,\n  Modal,\n  Form,\n  Input,\n  Dropdown,\n  Spin,\n  Empty\n} from 'antd';\nimport {\n  TeamOutlined,\n  UserOutlined,\n  CalendarOutlined,\n  EditOutlined,\n  CrownOutlined,\n  ClockCircleOutlined,\n  SettingOutlined,\n  DeleteOutlined,\n  ExclamationCircleOutlined,\n  ArrowLeftOutlined\n} from '@ant-design/icons';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';\nimport { history, useModel } from '@umijs/max';\nimport TeamMemberList from './TeamMemberList';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\n\ninterface TeamDetailContentProps {\n  teamDetail: TeamDetailResponse | null;\n  loading: boolean;\n  onRefresh: () => void;\n  /** 是否显示返回按钮 */\n  showBackButton?: boolean;\n  /** 返回按钮点击回调 */\n  onBack?: () => void;\n}\n\nconst TeamDetailContent: React.FC<TeamDetailContentProps> = ({\n  teamDetail,\n  loading,\n  onRefresh,\n  showBackButton = false,\n  onBack\n}) => {\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [updating, setUpdating] = useState(false);\n  const [deleting, setDeleting] = useState(false);\n  const [form] = Form.useForm();\n  const { setInitialState } = useModel('@@initialState');\n\n  // 辅助函数\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('zh-CN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const getTeamStatusColor = () => {\n    if (!teamDetail) return '#1890ff';\n    const memberCount = teamDetail.memberCount;\n    if (memberCount >= 10) return '#52c41a'; // 绿色 - 活跃\n    if (memberCount >= 5) return '#faad14'; // 橙色 - 正常\n    return '#1890ff'; // 蓝色 - 小团队\n  };\n\n  const getTeamStatusText = () => {\n    if (!teamDetail) return '小型团队';\n    const memberCount = teamDetail.memberCount;\n    if (memberCount >= 10) return '活跃团队';\n    if (memberCount >= 5) return '正常团队';\n    return '小型团队';\n  };\n\n  const handleGoBack = () => {\n    if (onBack) {\n      onBack();\n    } else {\n      history.push('/user/team-select');\n    }\n  };\n\n  /**\n   * 处理编辑团队信息操作\n   */\n  const handleEdit = () => {\n    if (!teamDetail) return;\n    form.setFieldsValue({\n      name: teamDetail.name,\n      description: teamDetail.description || '',\n    });\n    setEditModalVisible(true);\n  };\n\n  /**\n   * 处理团队信息更新操作\n   */\n  const handleUpdateTeam = async (values: UpdateTeamRequest) => {\n    if (!teamDetail) return;\n\n    try {\n      setUpdating(true);\n      await TeamService.updateCurrentTeam(values);\n      message.success('团队信息更新成功');\n      setEditModalVisible(false);\n      onRefresh();\n    } catch (error) {\n      console.error('更新团队失败:', error);\n      message.error('更新团队失败');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  /**\n   * 处理删除团队操作\n   */\n  const handleDeleteTeam = () => {\n    if (!teamDetail) return;\n\n    Modal.confirm({\n      title: '确认删除团队',\n      content: `确定要删除团队 \"${teamDetail.name}\" 吗？此操作不可恢复。`,\n      icon: <ExclamationCircleOutlined />,\n      okText: '确认删除',\n      cancelText: '取消',\n      okType: 'danger',\n      onOk: async () => {\n        try {\n          setDeleting(true);\n          await TeamService.deleteCurrentTeam();\n          message.success('团队删除成功');\n          // 更新全局状态，清除当前团队\n          setInitialState((s) => ({ ...s, currentTeam: undefined }));\n          history.push('/user/team-select');\n        } catch (error) {\n          console.error('删除团队失败:', error);\n          message.error('删除团队失败');\n        } finally {\n          setDeleting(false);\n        }\n      },\n    });\n  };\n\n  // 创建下拉菜单项（增强模式使用）\n  const createMenuItems = () => [\n    {\n      key: 'edit',\n      icon: <EditOutlined />,\n      label: '编辑团队',\n      onClick: handleEdit,\n    },\n    {\n      key: 'delete',\n      icon: <DeleteOutlined />,\n      label: '删除团队',\n      danger: true,\n      onClick: handleDeleteTeam,\n    },\n  ];\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px 0' }}>\n        <Spin size=\"large\" />\n      </div>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <Empty\n        image={Empty.PRESENTED_IMAGE_SIMPLE}\n        description=\"请先选择一个团队\"\n      />\n    );\n  }\n\n  // 增强模式渲染\n  return (\n    <div style={{ padding: '0 24px' }}>\n      {/* 团队头部信息卡片 */}\n      <Card\n        style={{\n          marginBottom: 24,\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          border: 'none',\n          borderRadius: 16\n        }}\n        styles={{ body: { padding: '32px' } }}\n      >\n        <Row align=\"middle\" justify=\"space-between\">\n          {/* 左列：返回按钮 */}\n          {showBackButton && (\n            <Col>\n              <Button\n                type=\"text\"\n                icon={<ArrowLeftOutlined />}\n                onClick={handleGoBack}\n                style={{\n                  color: 'rgba(255, 255, 255, 0.8)',\n                  fontSize: 16,\n                  padding: '4px 8px'\n                }}\n              >\n                返回\n              </Button>\n            </Col>\n          )}\n\n          {/* 中间列：团队名称显示 */}\n          <Col\n            flex=\"auto\"\n            style={{\n              display: 'flex',\n              justifyContent: 'center',\n              maxWidth: '60%'\n            }}\n          >\n              <Space size=\"large\" align=\"center\">\n                <Avatar\n                  size={65}\n                  icon={<TeamOutlined />}\n                  style={{\n                    backgroundColor: 'rgba(255, 255, 255, 0.2)',\n                    color: 'white',\n                    fontSize: 28\n                  }}\n                />\n                <div>\n                  <Space align=\"center\" style={{ marginBottom: 8 }}>\n                    <Title level={2} style={{ color: 'white', margin: 0 }}>\n                      {teamDetail.name}\n                    </Title>\n                    {teamDetail.isCreator && (\n                      <Tag\n                        icon={<CrownOutlined />}\n                        color=\"gold\"\n                        style={{ fontSize: 12 }}\n                      >\n                        管理员\n                      </Tag>\n                    )}\n                    <Badge\n                      color={getTeamStatusColor()}\n                      text={\n                        <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>\n                          {getTeamStatusText()}\n                        </Text>\n                      }\n                    />\n                  </Space>\n                  <Paragraph\n                    style={{\n                      color: 'rgba(255, 255, 255, 0.8)',\n                      margin: 0,\n                      textAlign: 'center'\n                    }}\n                    ellipsis={{ rows: 2 }}\n                  >\n                    {teamDetail.description || '这个团队还没有描述'}\n                  </Paragraph>\n                </div>\n              </Space>\n            </Col>\n\n            {/* 右列：团队操作菜单 */}\n            <Col>\n              {teamDetail.isCreator && (\n                <Dropdown\n                  menu={{ items: createMenuItems() }}\n                  trigger={['click']}\n                  placement=\"bottomRight\"\n                >\n                  <Button\n                    type=\"text\"\n                    icon={<SettingOutlined />}\n                    style={{\n                      color: 'rgba(255, 255, 255, 0.8)',\n                      fontSize: 20,\n                      width: 50,\n                      height: 50\n                    }}\n                  />\n                </Dropdown>\n              )}\n            </Col>\n          </Row>\n        </Card>\n\n        {/* 团队统计信息 */}\n        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n          <Col xs={24} sm={12} md={6}>\n            <Card>\n              <Statistic\n                title=\"团队成员\"\n                value={teamDetail.memberCount}\n                suffix=\"人\"\n                prefix={<UserOutlined style={{ color: '#1890ff' }} />}\n                valueStyle={{ color: '#1890ff' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Card>\n              <Statistic\n                title=\"创建时间\"\n                value={formatDate(teamDetail.createdAt)}\n                prefix={<CalendarOutlined style={{ color: '#52c41a' }} />}\n                valueStyle={{ color: '#52c41a', fontSize: 16 }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Card>\n              <Statistic\n                title=\"最后活动\"\n                value={formatDate(teamDetail.updatedAt)}\n                prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}\n                valueStyle={{ color: '#faad14', fontSize: 16 }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Card>\n              <div style={{ textAlign: 'center' }}>\n                <Text type=\"secondary\" style={{ fontSize: 14 }}>团队活跃度</Text>\n                <div style={{ marginTop: 8 }}>\n                  <Progress\n                    type=\"circle\"\n                    size={60}\n                    percent={Math.min(teamDetail.memberCount * 10, 100)}\n                    strokeColor={getTeamStatusColor()}\n                    format={() => (\n                      <Text style={{ fontSize: 12, color: getTeamStatusColor() }}>\n                        {teamDetail.memberCount >= 10 ? '高' :\n                         teamDetail.memberCount >= 5 ? '中' : '低'}\n                      </Text>\n                    )}\n                  />\n                </div>\n              </div>\n            </Card>\n          </Col>\n        </Row>\n\n        {/* 团队成员列表 */}\n        <TeamMemberList\n          teamId={teamDetail.id}\n          isCreator={teamDetail.isCreator}\n          onMemberChange={onRefresh}\n        />\n\n        {/* 编辑团队模态框 */}\n        <Modal\n          title=\"编辑团队信息\"\n          open={editModalVisible}\n          onCancel={() => setEditModalVisible(false)}\n          footer={null}\n          width={600}\n        >\n          <Form\n            form={form}\n            layout=\"vertical\"\n            onFinish={handleUpdateTeam}\n          >\n            <Form.Item\n              label=\"团队名称\"\n              name=\"name\"\n              rules={[\n                { required: true, message: '请输入团队名称' },\n                { max: 50, message: '团队名称不能超过50个字符' }\n              ]}\n            >\n              <Input placeholder=\"请输入团队名称\" />\n            </Form.Item>\n            <Form.Item\n              label=\"团队描述\"\n              name=\"description\"\n              rules={[\n                { max: 200, message: '团队描述不能超过200个字符' }\n              ]}\n            >\n              <TextArea\n                rows={4}\n                placeholder=\"请输入团队描述（可选）\"\n                showCount\n                maxLength={200}\n              />\n            </Form.Item>\n            <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n              <Space>\n                <Button onClick={() => setEditModalVisible(false)}>\n                  取消\n                </Button>\n                <Button type=\"primary\" htmlType=\"submit\" loading={updating}>\n                  保存\n                </Button>\n              </Space>\n            </Form.Item>\n          </Form>\n        </Modal>\n      </div>\n    );\n};\n\nexport default TeamDetailContent;\n", "/**\n * 团队成员列表组件\n *\n * 功能特性：\n * - 展示团队所有成员信息（头像、姓名、邮箱、角色、状态等）\n * - 支持成员搜索和筛选功能\n * - 提供成员管理操作（移除成员、角色变更等）\n * - 区分创建者和普通成员的权限显示\n * - 响应式表格设计，适配不同屏幕尺寸\n *\n * 权限控制：\n * - 只有团队创建者可以看到管理操作按钮\n * - 创建者不能移除自己\n * - 普通成员只能查看成员列表\n *\n * 交互设计：\n * - 支持批量操作（预留功能）\n * - 提供详细的操作确认对话框\n * - 实时更新成员状态和数量\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Avatar,\n  Tag,\n  Button,\n  Space,\n  message,\n  Modal,\n  Input,\n  Tooltip,\n  Dropdown,\n  Checkbox,\n  Typography,\n  Badge,\n  Divider,\n  Select\n} from 'antd';\nimport {\n  UserOutlined,\n  CrownOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  MoreOutlined,\n  UserSwitchOutlined,\n  StopOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  FilterOutlined,\n  HarmonyOSOutlined\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport type { MenuProps } from 'antd';\nimport { TeamService } from '@/services';\nimport type { TeamMemberResponse } from '@/types/api';\n\nconst { Text } = Typography;\nconst { Option } = Select;\n\n/**\n * 团队成员列表组件的Props接口\n */\ninterface TeamMemberListProps {\n  /** 团队ID，用于获取成员列表 */\n  teamId: number;\n  /** 当前用户是否为团队创建者，控制管理功能的显示 */\n  isCreator: boolean;\n  /** 成员变更时的回调函数，用于通知父组件刷新数据 */\n  onMemberChange?: () => void;\n}\n\nconst TeamMemberList: React.FC<TeamMemberListProps> = ({\n  teamId,\n  isCreator,\n  onMemberChange,\n}) => {\n  const [loading, setLoading] = useState(true);\n  const [members, setMembers] = useState<TeamMemberResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [filteredMembers, setFilteredMembers] = useState<TeamMemberResponse[]>([]);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [statusFilter, setStatusFilter] = useState<string>('all');\n\n  useEffect(() => {\n    fetchMembers();\n  }, [teamId]);\n\n  /**\n   * 成员列表过滤效果\n   *\n   * 过滤条件：\n   * 1. 搜索文本：匹配成员姓名或邮箱（不区分大小写）\n   * 2. 状态筛选：全部/活跃/非活跃/创建者/普通成员\n   *\n   * 安全性：\n   * - 添加空值检查，防止数据异常导致的错误\n   * - 确保成员对象的必要属性存在\n   */\n  useEffect(() => {\n    // 过滤成员列表 - 添加空值检查\n    if (!members || !Array.isArray(members)) {\n      setFilteredMembers([]);\n      return;\n    }\n\n    const filtered = members.filter(member => {\n      // 确保member对象存在且有必要的属性\n      if (!member || !member.name || !member.email) {\n        return false;\n      }\n\n      // 搜索文本匹配（姓名或邮箱）\n      const matchesSearch = !searchText ||\n        member.name.toLowerCase().includes(searchText.toLowerCase()) ||\n        member.email.toLowerCase().includes(searchText.toLowerCase());\n\n      // 状态筛选匹配\n      const matchesStatus = statusFilter === 'all' ||\n        (statusFilter === 'active' && member.isActive) ||\n        (statusFilter === 'inactive' && !member.isActive) ||\n        (statusFilter === 'creator' && member.isCreator) ||\n        (statusFilter === 'member' && !member.isCreator);\n\n      return matchesSearch && matchesStatus;\n    });\n    setFilteredMembers(filtered);\n  }, [members, searchText, statusFilter]);\n\n  /**\n   * 获取团队成员列表\n   *\n   * 功能：\n   * - 调用API获取当前团队的所有成员\n   * - 设置加载状态，提供用户反馈\n   * - 处理错误情况，确保组件稳定性\n   *\n   * 数据处理：\n   * - 确保返回数据为数组格式，防止渲染错误\n   * - 错误时设置空数组，保持组件正常显示\n   */\n  const fetchMembers = async () => {\n    try {\n      setLoading(true);\n      const response = await TeamService.getTeamMembers({ current: 1, pageSize: 1000 });\n      // 确保返回的数据是数组格式，防止渲染错误\n      setMembers(response?.list || []);\n    } catch (error) {\n      console.error('获取团队成员失败:', error);\n      message.error('获取团队成员失败');\n      // 出错时设置为空数组，保持组件正常显示\n      setMembers([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRemoveMember = (member: TeamMemberResponse) => {\n    if (member.isCreator) {\n      message.warning('不能移除团队创建者');\n      return;\n    }\n\n    Modal.confirm({\n      title: '确认移除成员',\n      content: `确定要移除成员 \"${member.name}\" 吗？`,\n      okText: '确认',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await TeamService.removeMember(member.id);\n          message.success('成员移除成功');\n          fetchMembers();\n          onMemberChange?.();\n        } catch (error) {\n          console.error('移除成员失败:', error);\n        }\n      },\n    });\n  };\n\n  const handleBatchRemove = () => {\n    const selectedMembers = members.filter(member =>\n      selectedRowKeys.includes(member.id) && !member.isCreator\n    );\n\n    if (selectedMembers.length === 0) {\n      message.warning('请选择要移除的成员');\n      return;\n    }\n\n    Modal.confirm({\n      title: '批量移除成员',\n      content: `确定要移除选中的 ${selectedMembers.length} 名成员吗？`,\n      okText: '确认',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await Promise.all(\n            selectedMembers.map(member => TeamService.removeMember(member.id))\n          );\n          message.success(`成功移除 ${selectedMembers.length} 名成员`);\n          setSelectedRowKeys([]);\n          fetchMembers();\n          onMemberChange?.();\n        } catch (error) {\n          console.error('批量移除成员失败:', error);\n          message.error('批量移除失败');\n        }\n      },\n    });\n  };\n\n  const columns: ColumnsType<TeamMemberResponse> = [\n    {\n      title: '成员',\n      dataIndex: 'name',\n      key: 'name',\n      render: (name, record) => (\n        <Space>\n          <Avatar size=\"small\" icon={<UserOutlined />} />\n          <div>\n            <div>{name}</div>\n            <div style={{ fontSize: 12, color: '#999' }}>{record.email}</div>\n          </div>\n        </Space>\n      ),\n    },\n    {\n      title: '角色',\n      dataIndex: 'isCreator',\n      key: 'role',\n      width: 100,\n      render: (isCreator) => (\n        <Tag color={isCreator ? 'gold' : 'blue'} icon={isCreator ? <CrownOutlined /> : <UserOutlined />}>\n          {isCreator ? '创建者' : '成员'}\n        </Tag>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'isActive',\n      key: 'status',\n      width: 80,\n      render: (isActive) => (\n        <Tag color={isActive ? 'green' : 'red'}>\n          {isActive ? '活跃' : '停用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '加入时间',\n      dataIndex: 'assignedAt',\n      key: 'assignedAt',\n      width: 150,\n      render: (assignedAt) => new Date(assignedAt).toLocaleDateString(),\n    },\n    {\n      title: '最后访问',\n      dataIndex: 'lastAccessTime',\n      key: 'lastAccessTime',\n      width: 150,\n      render: (lastAccessTime) => {\n        const date = new Date(lastAccessTime);\n        const now = new Date();\n        const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));\n        \n        let color = 'green';\n        if (diffDays > 7) color = 'orange';\n        if (diffDays > 30) color = 'red';\n        \n        return (\n          <Tooltip title={date.toLocaleString()}>\n            <Tag color={color}>\n              {diffDays === 0 ? '今天' : `${diffDays}天前`}\n            </Tag>\n          </Tooltip>\n        );\n      },\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      render: (_, record) => {\n        if (!isCreator || record.isCreator) {\n          return <Text type=\"secondary\">-</Text>;\n        }\n\n        const menuItems: MenuProps['items'] = [\n          {\n            key: 'remove',\n            label: '移除成员',\n            icon: <DeleteOutlined />,\n            danger: true,\n            onClick: () => handleRemoveMember(record),\n          },\n        ];\n\n        return (\n          <Space size=\"small\">\n            <Button\n              type=\"text\"\n              danger\n              size=\"small\"\n              icon={<DeleteOutlined />}\n              onClick={() => handleRemoveMember(record)}\n            >\n              移除\n            </Button>\n            <Dropdown menu={{ items: menuItems }} trigger={['click']}>\n              <Button\n                type=\"text\"\n                size=\"small\"\n                icon={<HarmonyOSOutlined />}\n              />\n            </Dropdown>\n          </Space>\n        );\n      },\n    },\n  ];\n\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: (newSelectedRowKeys: React.Key[]) => {\n      setSelectedRowKeys(newSelectedRowKeys);\n    },\n    getCheckboxProps: (record: TeamMemberResponse) => ({\n      disabled: record.isCreator, // 创建者不能被选中\n    }),\n  };\n\n  return (\n    <Card\n      title={\n        <Space>\n          <Text strong>团队成员</Text>\n          <Badge count={filteredMembers.length} showZero />\n        </Space>\n      }\n      extra={\n        <Space>\n          <Select\n            value={statusFilter}\n            onChange={setStatusFilter}\n            style={{ width: 120 }}\n            size=\"small\"\n          >\n            <Option value=\"all\">全部</Option>\n            <Option value=\"active\">活跃</Option>\n            <Option value=\"inactive\">停用</Option>\n            <Option value=\"creator\">创建者</Option>\n            <Option value=\"member\">成员</Option>\n          </Select>\n          <Input\n            placeholder=\"搜索成员\"\n            prefix={<SearchOutlined />}\n            value={searchText}\n            onChange={(e) => setSearchText(e.target.value)}\n            style={{ width: 200 }}\n            size=\"small\"\n          />\n        </Space>\n      }\n    >\n      {selectedRowKeys.length > 0 && isCreator && (\n        <div style={{ marginBottom: 16, padding: 12, background: '#f5f5f5', borderRadius: 6 }}>\n          <Space>\n            <Text>已选择 {selectedRowKeys.length} 名成员</Text>\n            <Button\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={handleBatchRemove}\n            >\n              批量移除\n            </Button>\n            <Button\n              size=\"small\"\n              onClick={() => setSelectedRowKeys([])}\n            >\n              取消选择\n            </Button>\n          </Space>\n        </div>\n      )}\n\n      <Table\n        columns={columns}\n        dataSource={filteredMembers}\n        rowKey=\"id\"\n        loading={loading}\n        rowSelection={isCreator ? rowSelection : undefined}\n        pagination={{\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total) => `共 ${total} 名成员`,\n          pageSize: 10,\n        }}\n      />\n    </Card>\n  );\n};\n\nexport default TeamMemberList;\n", "/**\n * 用户资料内容组件\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Form,\n  Input,\n  Button,\n  Space,\n  Typography,\n  message,\n  Avatar,\n  Upload\n} from 'antd';\nimport {\n  UserOutlined,\n  EditOutlined,\n  MailOutlined,\n  SaveOutlined,\n  UploadOutlined\n} from '@ant-design/icons';\nimport { UserService } from '@/services';\nimport type { UserProfileResponse, UpdateUserProfileRequest } from '@/types/api';\n\nconst { Title, Text } = Typography;\n\nconst UserProfileContent: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [editing, setEditing] = useState(false);\n  const [userProfile, setUserProfile] = useState<UserProfileResponse | null>(null);\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    fetchUserProfile();\n  }, []);\n\n  const fetchUserProfile = async () => {\n    try {\n      setLoading(true);\n      const profile = await UserService.getUserProfile();\n      setUserProfile(profile);\n      form.setFieldsValue({\n        name: profile.name,\n        email: profile.email,\n      });\n    } catch (error) {\n      console.error('获取用户资料失败:', error);\n      message.error('获取用户资料失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSaveProfile = async (values: any) => {\n    try {\n      setSaving(true);\n      const updateData: UpdateUserProfileRequest = {\n        name: values.name,\n      };\n\n      const updatedProfile = await UserService.updateUserProfile(updateData);\n      setUserProfile(updatedProfile);\n      setEditing(false);\n      message.success('个人资料更新成功');\n    } catch (error) {\n      console.error('更新个人资料失败:', error);\n      message.error('更新个人资料失败');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleCancel = () => {\n    setEditing(false);\n    if (userProfile) {\n      form.setFieldsValue({\n        name: userProfile.name,\n        email: userProfile.email,\n      });\n    }\n  };\n\n  if (loading || !userProfile) {\n    return <div>加载中...</div>;\n  }\n\n  return (\n    <div>\n      <div style={{ display: 'flex', alignItems: 'flex-start', gap: 24 }}>\n        {/* 头像部分 */}\n        <div style={{ textAlign: 'center' }}>\n          <Avatar size={120} icon={<UserOutlined />} />\n          <div style={{ marginTop: 16 }}>\n            <Upload\n              showUploadList={false}\n              beforeUpload={() => {\n                message.info('头像上传功能暂未实现');\n                return false;\n              }}\n            >\n              <Button icon={<UploadOutlined />} size=\"small\">\n                更换头像\n              </Button>\n            </Upload>\n          </div>\n        </div>\n\n        {/* 表单部分 */}\n        <div style={{ flex: 1 }}>\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>\n            <Title level={4} style={{ margin: 0 }}>\n              <UserOutlined /> 基本信息\n            </Title>\n            {!editing && (\n              <Button\n                type=\"primary\"\n                icon={<EditOutlined />}\n                onClick={() => setEditing(true)}\n              >\n                编辑资料\n              </Button>\n            )}\n          </div>\n\n          <Form\n            form={form}\n            layout=\"vertical\"\n            onFinish={handleSaveProfile}\n            disabled={!editing}\n          >\n            <Form.Item\n              label=\"用户名\"\n              name=\"name\"\n              rules={[\n                { required: true, message: '请输入用户名' },\n                { max: 100, message: '用户名不能超过100个字符' }\n              ]}\n            >\n              <Input\n                prefix={<UserOutlined />}\n                placeholder=\"请输入用户名\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              label=\"邮箱地址\"\n              name=\"email\"\n            >\n              <Input\n                prefix={<MailOutlined />}\n                disabled\n                placeholder=\"邮箱地址不可修改\"\n              />\n            </Form.Item>\n\n            <Form.Item>\n              <Space>\n                {editing ? (\n                  <>\n                    <Button\n                      type=\"primary\"\n                      htmlType=\"submit\"\n                      loading={saving}\n                      icon={<SaveOutlined />}\n                    >\n                      保存修改\n                    </Button>\n                    <Button onClick={handleCancel}>\n                      取消\n                    </Button>\n                  </>\n                ) : (\n                  <Button\n                    type=\"primary\"\n                    icon={<EditOutlined />}\n                    onClick={() => setEditing(true)}\n                  >\n                    编辑资料\n                  </Button>\n                )}\n              </Space>\n            </Form.Item>\n          </Form>\n        </div>\n      </div>\n\n\n    </div>\n  );\n};\n\nexport default UserProfileContent;\n"], "names": [], "mappings": ";;;AAAA;;;CAGC;;;;4BA6cD;;;eAAA;;;;;;wEA3c2C;6BAmBpC;8BAUA;iCAE6B;4BAMD;;;;;;;;;;AAEnC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAQlC,MAAM,6BAAwE,CAAC,EAC7E,mBAAmB,EACnB,OAAO,EACP,SAAS,EACV;;IACC,WAAW;IACX,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,IAAA,eAAQ,EAAyB,EAAE;IACzF,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAM;IAChD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,IAAA,eAAQ,EAAC;IAE/D,WAAW;IACX,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAA6B,EAAE;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAC;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAkC;IAClF,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,IAAA,eAAQ,EAAC;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IAEzC,IAAA,gBAAS,EAAC;QACR;QACA,IAAI,qBAAqB;YACvB;YACA;QACF;IACF,GAAG;QAAC;KAAoB;IAExB,SAAS;IACT,MAAM,aAAa;QACjB,IAAI;YACF,gBAAgB;YAChB,MAAM,YAAY,MAAM,6BAAmB,CAAC,cAAc;YAC1D,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,SAAS;IACT,MAAM,2BAA2B;QAC/B,IAAI;YACF,MAAM,UAAU,MAAM,6BAAmB,CAAC,sBAAsB;YAChE,uBAAuB;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,SAAS;IACT,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,QAAQ,MAAM,6BAAmB,CAAC,YAAY;YACpD,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,OAAO;IACP,MAAM,kBAAkB;QACtB,IAAI,CAAC,cAAc;QAEnB,IAAI;YACF,eAAe;YACf,MAAM,UAAqC;gBACzC,QAAQ,aAAa,EAAE;gBACvB,UAAU;YACZ;YAEA,MAAM,6BAAmB,CAAC,kBAAkB,CAAC;YAC7C,aAAO,CAAC,OAAO,CAAC;YAChB,yBAAyB;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,eAAe;QACjB;IACF;IAEA,OAAO;IACP,MAAM,2BAA2B;QAC/B,IAAI,CAAC,qBAAqB;QAE1B,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,MAAM;gBACJ,IAAI;oBACF,MAAM,6BAAmB,CAAC,kBAAkB,CAAC,oBAAoB,EAAE;oBACnE,aAAO,CAAC,OAAO,CAAC;oBAChB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,WAAW;oBACzB,aAAO,CAAC,KAAK,CAAC;gBAChB;YACF;QACF;IACF;IAEA,SAAS;IACT,MAAM,eAAe,CAAC;QACpB,MAAM,eAAe;YACnB,CAAC,uBAAkB,CAAC,MAAM,CAAC,EAAE;gBAAE,OAAO;gBAAS,MAAM;YAAK;YAC1D,CAAC,uBAAkB,CAAC,OAAO,CAAC,EAAE;gBAAE,OAAO;gBAAO,MAAM;YAAM;YAC1D,CAAC,uBAAkB,CAAC,QAAQ,CAAC,EAAE;gBAAE,OAAO;gBAAW,MAAM;YAAM;YAC/D,CAAC,uBAAkB,CAAC,OAAO,CAAC,EAAE;gBAAE,OAAO;gBAAU,MAAM;YAAM;QAC/D;QAEA,MAAM,SAAS,YAAY,CAAC,OAAO,IAAI;YAAE,OAAO;YAAW,MAAM;QAAK;QACtE,qBAAO,2BAAC,SAAG;YAAC,OAAO,OAAO,KAAK;sBAAG,OAAO,IAAI;;;;;;IAC/C;IAEA,WAAW;IACX,MAAM,wBAAwB,CAAC;QAC7B,IAAI,KAAK,IAAI,KAAK,OAChB,qBAAO,2BAAC,SAAG;YAAC,OAAM;YAAS,oBAAM,2BAAC,mBAAY;;;;;sBAAK;;;;;;QAErD,IAAI,KAAK,IAAI,KAAK,OAChB,qBAAO,2BAAC,SAAG;YAAC,OAAM;YAAO,oBAAM,2BAAC,oBAAa;;;;;sBAAK;;;;;;QAEpD,OAAO;IACT;IAEA,SAAS;IACT,MAAM,kBAAkB,CAAC;QACvB,MAAM,WAAW;YACf,CAAC,IAAI,EAAE,KAAK,OAAO,KAAK,SAAS,OAAO,KAAK,OAAO,CAAC,IAAI,CAAC;YAC1D;YACA;YACA;SACD;QAED,IAAI,KAAK,IAAI,KAAK,OAChB,SAAS,IAAI,CAAC;QAEhB,IAAI,KAAK,IAAI,KAAK,OAAO;YACvB,SAAS,IAAI,CAAC;YACd,SAAS,IAAI,CAAC;QAChB;QAEA,OAAO;IACT;IAEA,YAAY;IACZ,MAAM,iBAAoD;QACxD;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,SAA+B,aAAa;QACvD;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;QAC7D;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,OAAiB,OAAO,IAAI,KAAK,MAAM,kBAAkB,KAAK;QACzE;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,QAAkB,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,GAAG,CAAC;QACnD;KACD;IAED,qBACE,2BAAC;;0BAEC,2BAAC,UAAI;gBACH,qBACE,2BAAC,WAAK;;sCACJ,2BAAC,oBAAa;;;;;wBAAG;;;;;;;gBAIrB,qBACE,2BAAC,YAAM;oBACL,oBAAM,2BAAC,qBAAc;;;;;oBACrB,SAAS;oBACT,SAAS;8BACV;;;;;;gBAIH,OAAO;oBAAE,cAAc;gBAAG;0BAEzB,oCACC,2BAAC;;sCACC,2BAAC,kBAAY;4BAAC,QAAQ;4BAAG,QAAQ;;8CAC/B,2BAAC,kBAAY,CAAC,IAAI;oCAAC,OAAM;8CACvB,cAAA,2BAAC,WAAK;;4CACH,oBAAoB,QAAQ;4CAC5B,aAAa,oBAAoB,MAAM;;;;;;;;;;;;8CAG5C,2BAAC,kBAAY,CAAC,IAAI;oCAAC,OAAM;8CACtB,oBAAoB,OAAO,KAAK,SAAS,QAAQ,CAAC,EAAE,oBAAoB,OAAO,CAAC,EAAE,CAAC;;;;;;8CAEtF,2BAAC,kBAAY,CAAC,IAAI;oCAAC,OAAM;8CACtB,IAAI,KAAK,oBAAoB,SAAS,EAAE,kBAAkB;;;;;;8CAE7D,2BAAC,kBAAY,CAAC,IAAI;oCAAC,OAAM;8CACtB,oBAAoB,OAAO,GACxB,IAAI,KAAK,oBAAoB,OAAO,EAAE,kBAAkB,KACxD;;;;;;8CAGN,2BAAC,kBAAY,CAAC,IAAI;oCAAC,OAAM;;wCAAK;wCAC1B,oBAAoB,KAAK,CAAC,OAAO,CAAC;;;;;;;8CAEtC,2BAAC,kBAAY,CAAC,IAAI;oCAAC,OAAM;8CACtB,CAAA,sBAAA,gCAAA,UAAW,aAAa,MAAK,YAC1B,CAAC,EAAE,UAAU,aAAa,CAAC,EAAE,CAAC,GAC9B;;;;;;;;;;;;wBAKP,2BACC,2BAAC;4BAAI,OAAO;gCAAE,WAAW;4BAAG;;8CAC1B,2BAAC;oCAAK,MAAM;8CAAC;;;;;;8CACb,2BAAC,cAAQ;oCACP,SAAS,UAAU,eAAe;oCAClC,QAAQ,IAAM,CAAC,EAAE,UAAU,YAAY,CAAC,CAAC,EAAE,UAAU,QAAQ,KAAK,SAAS,MAAM,UAAU,QAAQ,CAAC,CAAC;oCACrG,OAAO;wCAAE,WAAW;oCAAE;;;;;;;;;;;;sCAK5B,2BAAC;4BAAI,OAAO;gCAAE,WAAW;4BAAG;sCAC1B,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCACL,MAAK;wCACL,oBAAM,2BAAC,iBAAU;;;;;wCACjB,SAAS,IAAM,yBAAyB;kDACzC;;;;;;kDAGD,2BAAC,YAAM;wCACL,oBAAM,2BAAC,sBAAe;;;;;wCACtB,SAAS,IAAM,uBAAuB;kDACvC;;;;;;oCAGA,oBAAoB,MAAM,KAAK,uBAAkB,CAAC,MAAM,kBACvD,2BAAC,YAAM;wCACL,MAAM;wCACN,oBAAM,2BAAC,mBAAY;;;;;wCACnB,SAAS;kDACV;;;;;;;;;;;;;;;;;;;;;;yCAQT,2BAAC,WAAK;oBACJ,aAAY;oBACZ,OAAO,WAAK,CAAC,sBAAsB;8BAEnC,cAAA,2BAAC,YAAM;wBACL,MAAK;wBACL,oBAAM,2BAAC,2BAAoB;;;;;wBAC3B,SAAS,IAAM,yBAAyB;kCACzC;;;;;;;;;;;;;;;;0BAQP,2BAAC,UAAI;gBACH,qBACE,2BAAC,WAAK;;sCACJ,2BAAC,2BAAoB;;;;;wBAAG;;;;;;;gBAI5B,SAAS;0BAET,cAAA,2BAAC,SAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;8BAClB,MAAM,GAAG,CAAC,CAAC,qBACV,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,2BAAC,UAAI;gCACH,SAAS;gCACT,WAAW,CAAC,UAAU,EAAE,CAAA,gCAAA,0CAAA,oBAAqB,MAAM,MAAK,KAAK,EAAE,GAAG,iBAAiB,GAAG,CAAC;gCACvF,SAAS;kDACP,2BAAC,YAAM;wCAEL,MAAM,CAAA,gCAAA,0CAAA,oBAAqB,MAAM,MAAK,KAAK,EAAE,GAAG,YAAY;wCAC5D,UAAU,CAAA,gCAAA,0CAAA,oBAAqB,MAAM,MAAK,KAAK,EAAE;wCACjD,SAAS;4CACP,gBAAgB;4CAChB,yBAAyB;wCAC3B;kDAEC,CAAA,gCAAA,0CAAA,oBAAqB,MAAM,MAAK,KAAK,EAAE,GAAG,SAAS;uCARhD;;;;;iCAUP;;kDAED,2BAAC;wCAAI,OAAO;4CAAE,WAAW;wCAAS;;0DAChC,2BAAC;gDAAM,OAAO;;oDACX,KAAK,IAAI;oDACT,sBAAsB;;;;;;;0DAEzB,2BAAC;gDAAI,OAAO;oDAAE,UAAU;oDAAI,YAAY;oDAAQ,OAAO;gDAAU;;oDAAG;oDAChE,KAAK,KAAK,CAAC,OAAO,CAAC;kEACrB,2BAAC;wDAAK,OAAO;4DAAE,UAAU;4DAAI,OAAO;wDAAO;kEAAG;;;;;;;;;;;;0DAEhD,2BAAC;gDAAK,MAAK;0DAAa,KAAK,WAAW;;;;;;;;;;;;kDAG1C,2BAAC,aAAO;;;;;kDAER,2BAAC,UAAI;wCACH,MAAK;wCACL,YAAY,gBAAgB;wCAC5B,YAAY,CAAC,wBACX,2BAAC,UAAI,CAAC,IAAI;0DACR,cAAA,2BAAC,WAAK;;sEACJ,2BAAC,oBAAa;4DAAC,OAAO;gEAAE,OAAO;4DAAU;;;;;;wDACxC;;;;;;;;;;;;;;;;;;;;;;;2BAvCoB,KAAK,EAAE;;;;;;;;;;;;;;;0BAmD9C,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,MAAM;gBACN,UAAU,IAAM,yBAAyB;gBACzC,gBAAgB;gBAChB,QAAO;gBACP,YAAW;0BAEV,8BACC,2BAAC;;sCACC,2BAAC,WAAK;4BACJ,SAAS,CAAC,KAAK,EAAE,aAAa,IAAI,CAAC,CAAC;4BACpC,aAAa,aAAa,WAAW;4BACrC,MAAK;4BACL,OAAO;gCAAE,cAAc;4BAAG;;;;;;sCAG5B,2BAAC;4BAAI,OAAO;gCAAE,cAAc;4BAAG;;8CAC7B,2BAAC;oCAAK,MAAM;8CAAC;;;;;;8CACb,2BAAC,iBAAW;oCACV,KAAK;oCACL,KAAK;oCACL,OAAO;oCACP,UAAU,CAAC,QAAU,YAAY,SAAS;oCAC1C,YAAW;oCACX,OAAO;wCAAE,YAAY;oCAAE;;;;;;;;;;;;sCAI3B,2BAAC;;8CACC,2BAAC;oCAAK,MAAM;8CAAC;;;;;;8CACb,2BAAC;oCAAK,OAAO;wCAAE,UAAU;wCAAI,OAAO;wCAAW,YAAY;oCAAE;;wCAAG;wCAC3D,CAAA,aAAa,KAAK,GAAG,QAAO,EAAG,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0BAQpD,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,uBAAuB;gBACvC,QAAQ;gBACR,OAAO;0BAEP,cAAA,2BAAC,WAAK;oBACJ,SAAS;oBACT,YAAY;oBACZ,QAAO;oBACP,YAAY;wBAAE,UAAU;oBAAG;;;;;;;;;;;;;;;;;AAKrC;GA1ZM;KAAA;IA4ZN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AChdf;;CAEC;;;;4BAwaD;;;eAAA;;;;;;;wEAtagC;6BAoBzB;8BAYA;iCACqB;4BAEM;gFACP;;;;;;;;;;AAE3B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,gBAAU;AAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;AAY1B,MAAM,oBAAsD,CAAC,EAC3D,UAAU,EACV,OAAO,EACP,SAAS,EACT,iBAAiB,KAAK,EACtB,MAAM,EACP;;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAC3B,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IAErC,OAAO;IACP,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,YAAY,OAAO;QACxB,MAAM,cAAc,WAAW,WAAW;QAC1C,IAAI,eAAe,IAAI,OAAO,WAAW,UAAU;QACnD,IAAI,eAAe,GAAG,OAAO,WAAW,UAAU;QAClD,OAAO,WAAW,WAAW;IAC/B;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,YAAY,OAAO;QACxB,MAAM,cAAc,WAAW,WAAW;QAC1C,IAAI,eAAe,IAAI,OAAO;QAC9B,IAAI,eAAe,GAAG,OAAO;QAC7B,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,IAAI,QACF;aAEA,YAAO,CAAC,IAAI,CAAC;IAEjB;IAEA;;GAEC,GACD,MAAM,aAAa;QACjB,IAAI,CAAC,YAAY;QACjB,KAAK,cAAc,CAAC;YAClB,MAAM,WAAW,IAAI;YACrB,aAAa,WAAW,WAAW,IAAI;QACzC;QACA,oBAAoB;IACtB;IAEA;;GAEC,GACD,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,YAAY;QAEjB,IAAI;YACF,YAAY;YACZ,MAAM,qBAAW,CAAC,iBAAiB,CAAC;YACpC,aAAO,CAAC,OAAO,CAAC;YAChB,oBAAoB;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,YAAY;QACd;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB;QACvB,IAAI,CAAC,YAAY;QAEjB,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,SAAS,EAAE,WAAW,IAAI,CAAC,YAAY,CAAC;YAClD,oBAAM,2BAAC,gCAAyB;;;;;YAChC,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,MAAM;gBACJ,IAAI;oBACF,YAAY;oBACZ,MAAM,qBAAW,CAAC,iBAAiB;oBACnC,aAAO,CAAC,OAAO,CAAC;oBAChB,gBAAgB;oBAChB,gBAAgB,CAAC,IAAO,CAAA;4BAAE,GAAG,CAAC;4BAAE,aAAa;wBAAU,CAAA;oBACvD,YAAO,CAAC,IAAI,CAAC;gBACf,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,WAAW;oBACzB,aAAO,CAAC,KAAK,CAAC;gBAChB,SAAU;oBACR,YAAY;gBACd;YACF;QACF;IACF;IAEA,kBAAkB;IAClB,MAAM,kBAAkB,IAAM;YAC5B;gBACE,KAAK;gBACL,oBAAM,2BAAC,mBAAY;;;;;gBACnB,OAAO;gBACP,SAAS;YACX;YACA;gBACE,KAAK;gBACL,oBAAM,2BAAC,qBAAc;;;;;gBACrB,OAAO;gBACP,QAAQ;gBACR,SAAS;YACX;SACD;IAED,IAAI,SACF,qBACE,2BAAC;QAAI,OAAO;YAAE,WAAW;YAAU,SAAS;QAAS;kBACnD,cAAA,2BAAC,UAAI;YAAC,MAAK;;;;;;;;;;;IAKjB,IAAI,CAAC,YACH,qBACE,2BAAC,WAAK;QACJ,OAAO,WAAK,CAAC,sBAAsB;QACnC,aAAY;;;;;;IAKlB,SAAS;IACT,qBACE,2BAAC;QAAI,OAAO;YAAE,SAAS;QAAS;;0BAE9B,2BAAC,UAAI;gBACH,OAAO;oBACL,cAAc;oBACd,YAAY;oBACZ,QAAQ;oBACR,cAAc;gBAChB;gBACA,QAAQ;oBAAE,MAAM;wBAAE,SAAS;oBAAO;gBAAE;0BAEpC,cAAA,2BAAC,SAAG;oBAAC,OAAM;oBAAS,SAAQ;;wBAEzB,gCACC,2BAAC,SAAG;sCACF,cAAA,2BAAC,YAAM;gCACL,MAAK;gCACL,oBAAM,2BAAC,wBAAiB;;;;;gCACxB,SAAS;gCACT,OAAO;oCACL,OAAO;oCACP,UAAU;oCACV,SAAS;gCACX;0CACD;;;;;;;;;;;sCAOL,2BAAC,SAAG;4BACF,MAAK;4BACL,OAAO;gCACL,SAAS;gCACT,gBAAgB;gCAChB,UAAU;4BACZ;sCAEE,cAAA,2BAAC,WAAK;gCAAC,MAAK;gCAAQ,OAAM;;kDACxB,2BAAC,YAAM;wCACL,MAAM;wCACN,oBAAM,2BAAC,mBAAY;;;;;wCACnB,OAAO;4CACL,iBAAiB;4CACjB,OAAO;4CACP,UAAU;wCACZ;;;;;;kDAEF,2BAAC;;0DACC,2BAAC,WAAK;gDAAC,OAAM;gDAAS,OAAO;oDAAE,cAAc;gDAAE;;kEAC7C,2BAAC;wDAAM,OAAO;wDAAG,OAAO;4DAAE,OAAO;4DAAS,QAAQ;wDAAE;kEACjD,WAAW,IAAI;;;;;;oDAEjB,WAAW,SAAS,kBACnB,2BAAC,SAAG;wDACF,oBAAM,2BAAC,oBAAa;;;;;wDACpB,OAAM;wDACN,OAAO;4DAAE,UAAU;wDAAG;kEACvB;;;;;;kEAIH,2BAAC,WAAK;wDACJ,OAAO;wDACP,oBACE,2BAAC;4DAAK,OAAO;gEAAE,OAAO;4DAA2B;sEAC9C;;;;;;;;;;;;;;;;;0DAKT,2BAAC;gDACC,OAAO;oDACL,OAAO;oDACP,QAAQ;oDACR,WAAW;gDACb;gDACA,UAAU;oDAAE,MAAM;gDAAE;0DAEnB,WAAW,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;sCAOnC,2BAAC,SAAG;sCACD,WAAW,SAAS,kBACnB,2BAAC,cAAQ;gCACP,MAAM;oCAAE,OAAO;gCAAkB;gCACjC,SAAS;oCAAC;iCAAQ;gCAClB,WAAU;0CAEV,cAAA,2BAAC,YAAM;oCACL,MAAK;oCACL,oBAAM,2BAAC,sBAAe;;;;;oCACtB,OAAO;wCACL,OAAO;wCACP,UAAU;wCACV,OAAO;wCACP,QAAQ;oCACV;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASZ,2BAAC,SAAG;gBAAC,QAAQ;oBAAC;oBAAI;iBAAG;gBAAE,OAAO;oBAAE,cAAc;gBAAG;;kCAC/C,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,WAAW,WAAW;gCAC7B,QAAO;gCACP,sBAAQ,2BAAC,mBAAY;oCAAC,OAAO;wCAAE,OAAO;oCAAU;;;;;;gCAChD,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;kCAIrC,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,WAAW,WAAW,SAAS;gCACtC,sBAAQ,2BAAC,uBAAgB;oCAAC,OAAO;wCAAE,OAAO;oCAAU;;;;;;gCACpD,YAAY;oCAAE,OAAO;oCAAW,UAAU;gCAAG;;;;;;;;;;;;;;;;kCAInD,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,WAAW,WAAW,SAAS;gCACtC,sBAAQ,2BAAC,0BAAmB;oCAAC,OAAO;wCAAE,OAAO;oCAAU;;;;;;gCACvD,YAAY;oCAAE,OAAO;oCAAW,UAAU;gCAAG;;;;;;;;;;;;;;;;kCAInD,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC;gCAAI,OAAO;oCAAE,WAAW;gCAAS;;kDAChC,2BAAC;wCAAK,MAAK;wCAAY,OAAO;4CAAE,UAAU;wCAAG;kDAAG;;;;;;kDAChD,2BAAC;wCAAI,OAAO;4CAAE,WAAW;wCAAE;kDACzB,cAAA,2BAAC,cAAQ;4CACP,MAAK;4CACL,MAAM;4CACN,SAAS,KAAK,GAAG,CAAC,WAAW,WAAW,GAAG,IAAI;4CAC/C,aAAa;4CACb,QAAQ,kBACN,2BAAC;oDAAK,OAAO;wDAAE,UAAU;wDAAI,OAAO;oDAAqB;8DACtD,WAAW,WAAW,IAAI,KAAK,MAC/B,WAAW,WAAW,IAAI,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWrD,2BAAC,uBAAc;gBACb,QAAQ,WAAW,EAAE;gBACrB,WAAW,WAAW,SAAS;gBAC/B,gBAAgB;;;;;;0BAIlB,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,oBAAoB;gBACpC,QAAQ;gBACR,OAAO;0BAEP,cAAA,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;sCAEV,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAU;gCACrC;oCAAE,KAAK;oCAAI,SAAS;gCAAgB;6BACrC;sCAED,cAAA,2BAAC,WAAK;gCAAC,aAAY;;;;;;;;;;;sCAErB,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,KAAK;oCAAK,SAAS;gCAAiB;6BACvC;sCAED,cAAA,2BAAC;gCACC,MAAM;gCACN,aAAY;gCACZ,SAAS;gCACT,WAAW;;;;;;;;;;;sCAGf,2BAAC,UAAI,CAAC,IAAI;4BAAC,OAAO;gCAAE,cAAc;gCAAG,WAAW;4BAAQ;sCACtD,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCAAC,SAAS,IAAM,oBAAoB;kDAAQ;;;;;;kDAGnD,2BAAC,YAAM;wCAAC,MAAK;wCAAU,UAAS;wCAAS,SAAS;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5E;GAjXM;;QAUW,UAAI,CAAC;QACQ,aAAQ;;;KAXhC;IAmXN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1af;;;;;;;;;;;;;;;;;;;CAmBC;;;;4BAmYD;;;eAAA;;;;;;wEAjY2C;6BAkBpC;8BAaA;iCAGqB;;;;;;;;;;AAG5B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,YAAM;AAczB,MAAM,iBAAgD,CAAC,EACrD,MAAM,EACN,SAAS,EACT,cAAc,EACf;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC/E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAc,EAAE;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAS;IAEzD,IAAA,gBAAS,EAAC;QACR;IACF,GAAG;QAAC;KAAO;IAEX;;;;;;;;;;GAUC,GACD,IAAA,gBAAS,EAAC;QACR,kBAAkB;QAClB,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,UAAU;YACvC,mBAAmB,EAAE;YACrB;QACF;QAEA,MAAM,WAAW,QAAQ,MAAM,CAAC,CAAA;YAC9B,sBAAsB;YACtB,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,EAC1C,OAAO;YAGT,gBAAgB;YAChB,MAAM,gBAAgB,CAAC,cACrB,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAE5D,SAAS;YACT,MAAM,gBAAgB,iBAAiB,SACpC,iBAAiB,YAAY,OAAO,QAAQ,IAC5C,iBAAiB,cAAc,CAAC,OAAO,QAAQ,IAC/C,iBAAiB,aAAa,OAAO,SAAS,IAC9C,iBAAiB,YAAY,CAAC,OAAO,SAAS;YAEjD,OAAO,iBAAiB;QAC1B;QACA,mBAAmB;IACrB,GAAG;QAAC;QAAS;QAAY;KAAa;IAEtC;;;;;;;;;;;GAWC,GACD,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,qBAAW,CAAC,cAAc,CAAC;gBAAE,SAAS;gBAAG,UAAU;YAAK;YAC/E,sBAAsB;YACtB,WAAW,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;YACd,qBAAqB;YACrB,WAAW,EAAE;QACf,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,OAAO,SAAS,EAAE;YACpB,aAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC;YACtC,QAAQ;YACR,YAAY;YACZ,MAAM;gBACJ,IAAI;oBACF,MAAM,qBAAW,CAAC,YAAY,CAAC,OAAO,EAAE;oBACxC,aAAO,CAAC,OAAO,CAAC;oBAChB;oBACA,2BAAA,6BAAA;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,WAAW;gBAC3B;YACF;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,SACrC,gBAAgB,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,SAAS;QAG1D,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,aAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC,MAAM,CAAC;YACnD,QAAQ;YACR,YAAY;YACZ,MAAM;gBACJ,IAAI;oBACF,MAAM,QAAQ,GAAG,CACf,gBAAgB,GAAG,CAAC,CAAA,SAAU,qBAAW,CAAC,YAAY,CAAC,OAAO,EAAE;oBAElE,aAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,gBAAgB,MAAM,CAAC,IAAI,CAAC;oBACpD,mBAAmB,EAAE;oBACrB;oBACA,2BAAA,6BAAA;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,aAAa;oBAC3B,aAAO,CAAC,KAAK,CAAC;gBAChB;YACF;QACF;IACF;IAEA,MAAM,UAA2C;QAC/C;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,MAAM,uBACb,2BAAC,WAAK;;sCACJ,2BAAC,YAAM;4BAAC,MAAK;4BAAQ,oBAAM,2BAAC,mBAAY;;;;;;;;;;sCACxC,2BAAC;;8CACC,2BAAC;8CAAK;;;;;;8CACN,2BAAC;oCAAI,OAAO;wCAAE,UAAU;wCAAI,OAAO;oCAAO;8CAAI,OAAO,KAAK;;;;;;;;;;;;;;;;;;QAIlE;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,0BACP,2BAAC,SAAG;oBAAC,OAAO,YAAY,SAAS;oBAAQ,MAAM,0BAAY,2BAAC,oBAAa;;;;+CAAM,2BAAC,mBAAY;;;;;8BACzF,YAAY,QAAQ;;;;;;QAG3B;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,yBACP,2BAAC,SAAG;oBAAC,OAAO,WAAW,UAAU;8BAC9B,WAAW,OAAO;;;;;;QAGzB;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,aAAe,IAAI,KAAK,YAAY,kBAAkB;QACjE;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC;gBACP,MAAM,OAAO,IAAI,KAAK;gBACtB,MAAM,MAAM,IAAI;gBAChB,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,CAAA,IAAI,OAAO,KAAK,KAAK,OAAO,EAAC,IAAM;gBAEhE,IAAI,QAAQ;gBACZ,IAAI,WAAW,GAAG,QAAQ;gBAC1B,IAAI,WAAW,IAAI,QAAQ;gBAE3B,qBACE,2BAAC,aAAO;oBAAC,OAAO,KAAK,cAAc;8BACjC,cAAA,2BAAC,SAAG;wBAAC,OAAO;kCACT,aAAa,IAAI,OAAO,CAAC,EAAE,SAAS,EAAE,CAAC;;;;;;;;;;;YAIhD;QACF;QACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG;gBACV,IAAI,CAAC,aAAa,OAAO,SAAS,EAChC,qBAAO,2BAAC;oBAAK,MAAK;8BAAY;;;;;;gBAGhC,MAAM,YAAgC;oBACpC;wBACE,KAAK;wBACL,OAAO;wBACP,oBAAM,2BAAC,qBAAc;;;;;wBACrB,QAAQ;wBACR,SAAS,IAAM,mBAAmB;oBACpC;iBACD;gBAED,qBACE,2BAAC,WAAK;oBAAC,MAAK;;sCACV,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAM;4BACN,MAAK;4BACL,oBAAM,2BAAC,qBAAc;;;;;4BACrB,SAAS,IAAM,mBAAmB;sCACnC;;;;;;sCAGD,2BAAC,cAAQ;4BAAC,MAAM;gCAAE,OAAO;4BAAU;4BAAG,SAAS;gCAAC;6BAAQ;sCACtD,cAAA,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAK;gCACL,oBAAM,2BAAC,wBAAiB;;;;;;;;;;;;;;;;;;;;;YAKlC;QACF;KACD;IAED,MAAM,eAAe;QACnB;QACA,UAAU,CAAC;YACT,mBAAmB;QACrB;QACA,kBAAkB,CAAC,SAAgC,CAAA;gBACjD,UAAU,OAAO,SAAS;YAC5B,CAAA;IACF;IAEA,qBACE,2BAAC,UAAI;QACH,qBACE,2BAAC,WAAK;;8BACJ,2BAAC;oBAAK,MAAM;8BAAC;;;;;;8BACb,2BAAC,WAAK;oBAAC,OAAO,gBAAgB,MAAM;oBAAE,QAAQ;;;;;;;;;;;;QAGlD,qBACE,2BAAC,WAAK;;8BACJ,2BAAC,YAAM;oBACL,OAAO;oBACP,UAAU;oBACV,OAAO;wBAAE,OAAO;oBAAI;oBACpB,MAAK;;sCAEL,2BAAC;4BAAO,OAAM;sCAAM;;;;;;sCACpB,2BAAC;4BAAO,OAAM;sCAAS;;;;;;sCACvB,2BAAC;4BAAO,OAAM;sCAAW;;;;;;sCACzB,2BAAC;4BAAO,OAAM;sCAAU;;;;;;sCACxB,2BAAC;4BAAO,OAAM;sCAAS;;;;;;;;;;;;8BAEzB,2BAAC,WAAK;oBACJ,aAAY;oBACZ,sBAAQ,2BAAC,qBAAc;;;;;oBACvB,OAAO;oBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oBAC7C,OAAO;wBAAE,OAAO;oBAAI;oBACpB,MAAK;;;;;;;;;;;;;YAKV,gBAAgB,MAAM,GAAG,KAAK,2BAC7B,2BAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAI,SAAS;oBAAI,YAAY;oBAAW,cAAc;gBAAE;0BAClF,cAAA,2BAAC,WAAK;;sCACJ,2BAAC;;gCAAK;gCAAK,gBAAgB,MAAM;gCAAC;;;;;;;sCAClC,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAM;4BACN,oBAAM,2BAAC,qBAAc;;;;;4BACrB,SAAS;sCACV;;;;;;sCAGD,2BAAC,YAAM;4BACL,MAAK;4BACL,SAAS,IAAM,mBAAmB,EAAE;sCACrC;;;;;;;;;;;;;;;;;0BAOP,2BAAC,WAAK;gBACJ,SAAS;gBACT,YAAY;gBACZ,QAAO;gBACP,SAAS;gBACT,cAAc,YAAY,eAAe;gBACzC,YAAY;oBACV,iBAAiB;oBACjB,iBAAiB;oBACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;oBACtC,UAAU;gBACZ;;;;;;;;;;;;AAIR;GA3UM;KAAA;IA6UN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtZf;;CAEC;;;;4BA+LD;;;eAAA;;;;;;wEA7L2C;6BAUpC;8BAOA;iCACqB;;;;;;;;;;AAG5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAElC,MAAM,qBAA+B;;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,eAAQ,EAAC;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAA6B;IAC3E,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAE3B,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI;YACF,WAAW;YACX,MAAM,UAAU,MAAM,qBAAW,CAAC,cAAc;YAChD,eAAe;YACf,KAAK,cAAc,CAAC;gBAClB,MAAM,QAAQ,IAAI;gBAClB,OAAO,QAAQ,KAAK;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,UAAU;YACV,MAAM,aAAuC;gBAC3C,MAAM,OAAO,IAAI;YACnB;YAEA,MAAM,iBAAiB,MAAM,qBAAW,CAAC,iBAAiB,CAAC;YAC3D,eAAe;YACf,WAAW;YACX,aAAO,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,eAAe;QACnB,WAAW;QACX,IAAI,aACF,KAAK,cAAc,CAAC;YAClB,MAAM,YAAY,IAAI;YACtB,OAAO,YAAY,KAAK;QAC1B;IAEJ;IAEA,IAAI,WAAW,CAAC,aACd,qBAAO,2BAAC;kBAAI;;;;;;IAGd,qBACE,2BAAC;kBACC,cAAA,2BAAC;YAAI,OAAO;gBAAE,SAAS;gBAAQ,YAAY;gBAAc,KAAK;YAAG;;8BAE/D,2BAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAS;;sCAChC,2BAAC,YAAM;4BAAC,MAAM;4BAAK,oBAAM,2BAAC,mBAAY;;;;;;;;;;sCACtC,2BAAC;4BAAI,OAAO;gCAAE,WAAW;4BAAG;sCAC1B,cAAA,2BAAC,YAAM;gCACL,gBAAgB;gCAChB,cAAc;oCACZ,aAAO,CAAC,IAAI,CAAC;oCACb,OAAO;gCACT;0CAEA,cAAA,2BAAC,YAAM;oCAAC,oBAAM,2BAAC,qBAAc;;;;;oCAAK,MAAK;8CAAQ;;;;;;;;;;;;;;;;;;;;;;8BAQrD,2BAAC;oBAAI,OAAO;wBAAE,MAAM;oBAAE;;sCACpB,2BAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,gBAAgB;gCAAiB,YAAY;gCAAU,cAAc;4BAAG;;8CACrG,2BAAC;oCAAM,OAAO;oCAAG,OAAO;wCAAE,QAAQ;oCAAE;;sDAClC,2BAAC,mBAAY;;;;;wCAAG;;;;;;;gCAEjB,CAAC,yBACA,2BAAC,YAAM;oCACL,MAAK;oCACL,oBAAM,2BAAC,mBAAY;;;;;oCACnB,SAAS,IAAM,WAAW;8CAC3B;;;;;;;;;;;;sCAML,2BAAC,UAAI;4BACH,MAAM;4BACN,QAAO;4BACP,UAAU;4BACV,UAAU,CAAC;;8CAEX,2BAAC,UAAI,CAAC,IAAI;oCACR,OAAM;oCACN,MAAK;oCACL,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAS;wCACpC;4CAAE,KAAK;4CAAK,SAAS;wCAAgB;qCACtC;8CAED,cAAA,2BAAC,WAAK;wCACJ,sBAAQ,2BAAC,mBAAY;;;;;wCACrB,aAAY;;;;;;;;;;;8CAIhB,2BAAC,UAAI,CAAC,IAAI;oCACR,OAAM;oCACN,MAAK;8CAEL,cAAA,2BAAC,WAAK;wCACJ,sBAAQ,2BAAC,mBAAY;;;;;wCACrB,QAAQ;wCACR,aAAY;;;;;;;;;;;8CAIhB,2BAAC,UAAI,CAAC,IAAI;8CACR,cAAA,2BAAC,WAAK;kDACH,wBACC;;8DACE,2BAAC,YAAM;oDACL,MAAK;oDACL,UAAS;oDACT,SAAS;oDACT,oBAAM,2BAAC,mBAAY;;;;;8DACpB;;;;;;8DAGD,2BAAC,YAAM;oDAAC,SAAS;8DAAc;;;;;;;yEAKjC,2BAAC,YAAM;4CACL,MAAK;4CACL,oBAAM,2BAAC,mBAAY;;;;;4CACnB,SAAS,IAAM,WAAW;sDAC3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAanB;GApKM;;QAKW,UAAI,CAAC;;;KALhB;IAsKN,WAAe"}