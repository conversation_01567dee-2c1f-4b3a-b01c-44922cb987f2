/**
 * TODO服务
 */

import { apiRequest } from '@/utils/request';
import type {
  TodoResponse,
  CreateTodoRequest,
  UpdateTodoRequest,
  TodoStatsResponse,
} from '@/types/api';

export class TodoService {
  /**
   * 获取用户的TODO列表
   */
  static async getUserTodos(): Promise<TodoResponse[]> {
    const response = await apiRequest.get<TodoResponse[]>('/todos');
    return response.data;
  }

  /**
   * 创建TODO
   */
  static async createTodo(request: CreateTodoRequest): Promise<TodoResponse> {
    const response = await apiRequest.post<TodoResponse>('/todos', request);
    return response.data;
  }

  /**
   * 更新TODO
   */
  static async updateTodo(id: number, request: UpdateTodoRequest): Promise<TodoResponse> {
    const response = await apiRequest.put<TodoResponse>(`/todos/${id}`, request);
    return response.data;
  }

  /**
   * 删除TODO
   */
  static async deleteTodo(id: number): Promise<void> {
    await apiRequest.delete(`/todos/${id}`);
  }

  /**
   * 获取TODO统计信息
   */
  static async getTodoStats(): Promise<TodoStatsResponse> {
    const response = await apiRequest.get<TodoStatsResponse>('/todos/stats');
    return response.data;
  }
}
