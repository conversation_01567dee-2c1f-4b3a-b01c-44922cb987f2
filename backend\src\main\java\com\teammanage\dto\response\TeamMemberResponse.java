package com.teammanage.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;


import java.time.LocalDateTime;

/**
 * 团队成员响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class TeamMemberResponse {

    /**
     * 成员记录ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long accountId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户名
     */
    private String name;

    /**
     * 是否为创建者
     */
    private Boolean isCreator;

    /**
     * 加入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime assignedAt;

    /**
     * 最后访问时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastAccessTime;

    /**
     * 是否激活
     */
    private Boolean isActive;

    // Getter and Setter methods
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getAccountId() { return accountId; }
    public void setAccountId(Long accountId) { this.accountId = accountId; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public Boolean getIsCreator() { return isCreator; }
    public void setIsCreator(Boolean isCreator) { this.isCreator = isCreator; }

    public LocalDateTime getAssignedAt() { return assignedAt; }
    public void setAssignedAt(LocalDateTime assignedAt) { this.assignedAt = assignedAt; }

    public LocalDateTime getLastAccessTime() { return lastAccessTime; }
    public void setLastAccessTime(LocalDateTime lastAccessTime) { this.lastAccessTime = lastAccessTime; }

    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }

}
