package com.teammanage.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.teammanage.dto.response.LoginResponse;
import com.teammanage.entity.Team;
import com.teammanage.entity.TeamMember;
import com.teammanage.mapper.TeamMapper;
import com.teammanage.mapper.TeamMemberMapper;

/**
 * AuthService 测试类
 * 专门测试团队信息转换逻辑
 */
@ExtendWith(MockitoExtension.class)
public class AuthServiceTest {

    @Mock
    private TeamMapper teamMapper;

    @Mock
    private TeamMemberMapper teamMemberMapper;

    @InjectMocks
    private AuthService authService;

    private TeamMember testTeamMember;
    private Team testTeam;

    @BeforeEach
    void setUp() {
        // 创建测试数据
        testTeamMember = new TeamMember();
        testTeamMember.setTeamId(1L);
        testTeamMember.setAccountId(1L);
        testTeamMember.setIsCreator(true);
        testTeamMember.setLastAccessTime(LocalDateTime.now());

        testTeam = new Team();
        testTeam.setId(1L);
        testTeam.setName("测试团队");
        testTeam.setDescription("这是一个测试团队");
        testTeam.setIsDeleted(false);
    }

    @Test
    void testConvertToTeamInfo_WithValidTeam() {
        // 模拟数据库查询
        when(teamMapper.selectById(1L)).thenReturn(testTeam);
        when(teamMemberMapper.countByTeamId(1L)).thenReturn(5);

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = AuthService.class.getDeclaredMethod("convertToTeamInfo", TeamMember.class);
            method.setAccessible(true);
            
            LoginResponse.TeamInfo result = (LoginResponse.TeamInfo) method.invoke(authService, testTeamMember);

            // 验证结果
            assertNotNull(result);
            assertEquals(1L, result.getId());
            assertEquals("测试团队", result.getName());
            assertEquals(true, result.getIsCreator());
            assertEquals(5, result.getMemberCount());
            assertNotNull(result.getLastAccessTime());

            // 验证方法调用
            verify(teamMapper, times(1)).selectById(1L);
            verify(teamMemberMapper, times(1)).countByTeamId(1L);

        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    void testConvertToTeamInfo_WithDeletedTeam() {
        // 模拟已删除的团队
        testTeam.setIsDeleted(true);
        when(teamMapper.selectById(1L)).thenReturn(testTeam);

        try {
            java.lang.reflect.Method method = AuthService.class.getDeclaredMethod("convertToTeamInfo", TeamMember.class);
            method.setAccessible(true);
            
            LoginResponse.TeamInfo result = (LoginResponse.TeamInfo) method.invoke(authService, testTeamMember);

            // 验证结果 - 已删除的团队不应该设置名称和成员数量
            assertNotNull(result);
            assertEquals(1L, result.getId());
            assertNull(result.getName()); // 名称应该为空
            assertEquals(true, result.getIsCreator());
            assertNull(result.getMemberCount()); // 成员数量应该为空

            // 验证方法调用
            verify(teamMapper, times(1)).selectById(1L);
            verify(teamMemberMapper, never()).countByTeamId(1L); // 不应该查询成员数量

        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    void testConvertToTeamInfo_WithNullTeam() {
        // 模拟团队不存在
        when(teamMapper.selectById(1L)).thenReturn(null);

        try {
            java.lang.reflect.Method method = AuthService.class.getDeclaredMethod("convertToTeamInfo", TeamMember.class);
            method.setAccessible(true);
            
            LoginResponse.TeamInfo result = (LoginResponse.TeamInfo) method.invoke(authService, testTeamMember);

            // 验证结果 - 团队不存在时不应该设置名称和成员数量
            assertNotNull(result);
            assertEquals(1L, result.getId());
            assertNull(result.getName()); // 名称应该为空
            assertEquals(true, result.getIsCreator());
            assertNull(result.getMemberCount()); // 成员数量应该为空

            // 验证方法调用
            verify(teamMapper, times(1)).selectById(1L);
            verify(teamMemberMapper, never()).countByTeamId(1L); // 不应该查询成员数量

        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }
}
