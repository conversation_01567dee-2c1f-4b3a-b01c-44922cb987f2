package com.teammanage.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 团队成员实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

@TableName("team_member")
public class TeamMember extends BaseEntity {

    /**
     * 团队ID
     */
    private Long teamId;

    /**
     * 用户ID
     */
    private Long accountId;

    /**
     * 是否为创建者
     */
    private Boolean isCreator;

    /**
     * 分配时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime assignedAt;

    /**
     * 最后访问时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastAccessTime;

    /**
     * 账号状态
     */
    private Boolean isActive;

    /**
     * 删除标记
     */
    @TableLogic
    private Boolean isDeleted;

    // 手动添加getter/setter方法
    public Long getTeamId() { return teamId; }
    public void setTeamId(Long teamId) { this.teamId = teamId; }

    public Long getAccountId() { return accountId; }
    public void setAccountId(Long accountId) { this.accountId = accountId; }

    public Boolean getIsCreator() { return isCreator; }
    public void setIsCreator(Boolean isCreator) { this.isCreator = isCreator; }

    public LocalDateTime getAssignedAt() { return assignedAt; }
    public void setAssignedAt(LocalDateTime assignedAt) { this.assignedAt = assignedAt; }

    public LocalDateTime getLastAccessTime() { return lastAccessTime; }
    public void setLastAccessTime(LocalDateTime lastAccessTime) { this.lastAccessTime = lastAccessTime; }

    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }

    public Boolean getIsDeleted() { return isDeleted; }
    public void setIsDeleted(Boolean isDeleted) { this.isDeleted = isDeleted; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        TeamMember that = (TeamMember) o;
        return Objects.equals(teamId, that.teamId) &&
               Objects.equals(accountId, that.accountId) &&
               Objects.equals(isCreator, that.isCreator) &&
               Objects.equals(assignedAt, that.assignedAt) &&
               Objects.equals(lastAccessTime, that.lastAccessTime) &&
               Objects.equals(isActive, that.isActive) &&
               Objects.equals(isDeleted, that.isDeleted);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), teamId, accountId, isCreator, assignedAt, lastAccessTime, isActive, isDeleted);
    }

}
