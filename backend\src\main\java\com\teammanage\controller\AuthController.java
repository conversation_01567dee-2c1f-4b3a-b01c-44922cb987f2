package com.teammanage.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.teammanage.common.ApiResponse;
import com.teammanage.dto.request.LoginRequest;
import com.teammanage.dto.request.RegisterRequest;
import com.teammanage.dto.request.SelectTeamRequest;
import com.teammanage.dto.response.LoginResponse;
import com.teammanage.service.AuthService;
import com.teammanage.util.JwtTokenUtil;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
 * 认证控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/auth")
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {

    @Autowired
    private AuthService authService;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "新用户注册账号")
    public ApiResponse<Void> register(@Valid @RequestBody RegisterRequest request) {
        authService.register(request);
        return ApiResponse.<Void>success("注册成功", null);
    }

    /**
     * 用户登录（单阶段）
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户登录，验证凭据并返回用户Token和团队列表")
    public ApiResponse<LoginResponse> login(@Valid @RequestBody LoginRequest request,
                                          HttpServletRequest httpRequest) {
        LoginResponse response = authService.login(request, httpRequest);
        return ApiResponse.success("登录成功", response);
    }



    /**
     * 刷新Token
     */
    @PostMapping("/refresh-token")
    @Operation(summary = "刷新Token", description = "刷新即将过期的Token")
    public ApiResponse<LoginResponse> refreshToken(HttpServletRequest httpRequest) {
        String currentToken = getTokenFromRequest(httpRequest);
        LoginResponse response = authService.refreshToken(currentToken);
        return ApiResponse.success("Token刷新成功", response);
    }

    /**
     * 选择团队
     */
    @PostMapping("/select-team")
    @Operation(summary = "选择团队", description = "选择团队并获取包含团队信息的Token")
    public ApiResponse<LoginResponse> selectTeam(@RequestBody SelectTeamRequest request,
                                               HttpServletRequest httpRequest) {
        String currentToken = getTokenFromRequest(httpRequest);
        LoginResponse response = authService.selectTeam(request.getTeamId(), currentToken);
        return ApiResponse.success("团队选择成功", response);
    }

    /**
     * 切换团队
     */
    @PostMapping("/switch-team")
    @Operation(summary = "切换团队", description = "切换到其他团队")
    public ApiResponse<LoginResponse> switchTeam(@RequestBody SelectTeamRequest request,
                                               HttpServletRequest httpRequest) {
        String currentToken = getTokenFromRequest(httpRequest);
        LoginResponse response = authService.switchTeam(request.getTeamId(), currentToken);
        return ApiResponse.success("团队切换成功", response);
    }

    /**
     * 清除团队上下文
     */
    @PostMapping("/clear-team")
    @Operation(summary = "清除团队上下文", description = "清除当前团队上下文，回到用户级别权限")
    public ApiResponse<String> clearTeam(HttpServletRequest httpRequest) {
        String currentToken = getTokenFromRequest(httpRequest);
        String newToken = authService.clearTeam(currentToken);
        return ApiResponse.success("团队上下文已清除", newToken);
    }

    // ========== 兼容性端点 ==========



    /**
     * 登出
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出，使Token失效")
    public ApiResponse<Void> logout(HttpServletRequest httpRequest) {
        String token = getTokenFromRequest(httpRequest);
        authService.logout(token);
        return ApiResponse.<Void>success("登出成功", null);
    }

    /**
     * 验证Token
     */
    @GetMapping("/validate")
    @Operation(summary = "验证Token", description = "验证当前Token是否有效")
    public ApiResponse<Boolean> validateToken(HttpServletRequest httpRequest) {
        String token = getTokenFromRequest(httpRequest);
        if (!StringUtils.hasText(token)) {
            return ApiResponse.success("Token验证结果", false);
        }
        
        boolean isValid = jwtTokenUtil.validateToken(token);
        return ApiResponse.success("Token验证结果", isValid);
    }

    /**
     * 从请求中获取Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

}
