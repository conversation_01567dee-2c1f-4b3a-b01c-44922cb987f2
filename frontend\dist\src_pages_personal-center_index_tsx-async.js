((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['src/pages/personal-center/index.tsx'],
{ "src/pages/personal-center/components/FriendManageContent.tsx": function (module, exports, __mako_require__){
/**
 * 个人中心 - 好友管理内容组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text, Title } = _antd.Typography;
const { Search } = _antd.Input;
const FriendManageContent = ()=>{
    _s();
    // 标签页状态
    const [activeTab, setActiveTab] = (0, _react.useState)('list');
    // 好友列表状态
    const [friends, setFriends] = (0, _react.useState)([]);
    const [loading, setLoading] = (0, _react.useState)(false);
    const [searchKeyword, setSearchKeyword] = (0, _react.useState)('');
    const [currentPage, setCurrentPage] = (0, _react.useState)(1);
    const [pageSize] = (0, _react.useState)(8);
    // 操作状态
    const [removing, setRemoving] = (0, _react.useState)(null);
    const [remarkModalVisible, setRemarkModalVisible] = (0, _react.useState)(false);
    const [addFriendModalVisible, setAddFriendModalVisible] = (0, _react.useState)(false);
    const [currentFriend, setCurrentFriend] = (0, _react.useState)(null);
    // 表单
    const [remarkForm] = _antd.Form.useForm();
    const [addFriendForm] = _antd.Form.useForm();
    // 添加好友相关状态
    const [searchUsers, setSearchUsers] = (0, _react.useState)([]);
    const [searchingUsers, setSearchingUsers] = (0, _react.useState)(false);
    const [addingFriend, setAddingFriend] = (0, _react.useState)(null);
    // 获取好友列表
    const fetchFriends = async ()=>{
        try {
            setLoading(true);
            const friendList = await _services.FriendService.getFriends();
            setFriends(friendList);
        } catch (error) {
            console.error('获取好友列表失败:', error);
            _antd.message.error('获取好友列表失败');
        } finally{
            setLoading(false);
        }
    };
    // 初始化加载
    (0, _react.useEffect)(()=>{
        fetchFriends();
    }, []);
    // 过滤好友列表
    const filteredFriends = friends.filter((friend)=>{
        var _friend_name, _friend_email;
        return ((_friend_name = friend.name) === null || _friend_name === void 0 ? void 0 : _friend_name.toLowerCase().includes(searchKeyword.toLowerCase())) || ((_friend_email = friend.email) === null || _friend_email === void 0 ? void 0 : _friend_email.toLowerCase().includes(searchKeyword.toLowerCase()));
    });
    // 分页处理
    const paginatedFriends = filteredFriends.slice((currentPage - 1) * pageSize, currentPage * pageSize);
    // 删除好友
    const handleRemoveFriend = async (friend)=>{
        try {
            setRemoving(friend.id);
            await _services.FriendService.removeFriend(friend.id);
            _antd.message.success(`已删除好友 "${friend.name}"`);
            fetchFriends();
        } catch (error) {
            console.error('删除好友失败:', error);
            _antd.message.error('删除好友失败');
        } finally{
            setRemoving(null);
        }
    };
    // 编辑备注
    const handleEditRemark = async (friend)=>{
        setCurrentFriend(friend);
        setRemarkModalVisible(true);
        try {
            // 获取当前备注
            const remark = await _services.FriendService.getFriendRemark(friend.id);
            remarkForm.setFieldsValue({
                remark
            });
        } catch (error) {
            console.error('获取好友备注失败:', error);
            remarkForm.setFieldsValue({
                remark: ''
            });
        }
    };
    // 保存备注
    const handleSaveRemark = async (values)=>{
        if (!currentFriend) return;
        try {
            await _services.FriendService.setFriendRemark({
                friendId: currentFriend.id,
                remark: values.remark
            });
            _antd.message.success('备注保存成功');
            setRemarkModalVisible(false);
            setCurrentFriend(null);
            remarkForm.resetFields();
            fetchFriends();
        } catch (error) {
            console.error('保存备注失败:', error);
            _antd.message.error('保存备注失败');
        }
    };
    // 搜索用户
    const handleSearchUsers = async (email)=>{
        if (!email.trim()) {
            setSearchUsers([]);
            return;
        }
        try {
            setSearchingUsers(true);
            const users = await _services.UserService.searchUsersByEmail(email);
            // 过滤掉已经是好友的用户
            const friendIds = friends.map((f)=>f.id);
            const availableUsers = users.filter((user)=>!friendIds.includes(user.id));
            setSearchUsers(availableUsers);
        } catch (error) {
            console.error('搜索用户失败:', error);
            _antd.message.error('搜索用户失败');
        } finally{
            setSearchingUsers(false);
        }
    };
    // 添加好友
    const handleAddFriend = async (user)=>{
        try {
            setAddingFriend(user.id);
            await _services.FriendService.sendFriendRequest({
                email: user.email
            });
            _antd.message.success(`已向 "${user.name}" 发送好友请求`);
            setSearchUsers(searchUsers.filter((u)=>u.id !== user.id));
        } catch (error) {
            console.error('发送好友请求失败:', error);
            _antd.message.error('发送好友请求失败');
        } finally{
            setAddingFriend(null);
        }
    };
    // 打开添加好友模态框
    const handleOpenAddFriend = ()=>{
        setAddFriendModalVisible(true);
        setSearchUsers([]);
        addFriendForm.resetFields();
    };
    // 好友列表组件
    const renderFriendList = ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                    gutter: [
                        16,
                        16
                    ],
                    style: {
                        marginBottom: 24
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            sm: 12,
                            md: 8,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Search, {
                                placeholder: "搜索好友姓名或邮箱",
                                allowClear: true,
                                value: searchKeyword,
                                onChange: (e)=>setSearchKeyword(e.target.value),
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                    lineNumber: 207,
                                    columnNumber: 21
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                lineNumber: 202,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                            lineNumber: 201,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            sm: 12,
                            md: 16,
                            style: {
                                textAlign: 'right'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                            lineNumber: 214,
                                            columnNumber: 21
                                        }, void 0),
                                        onClick: handleOpenAddFriend,
                                        children: "添加好友"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 212,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ReloadOutlined, {}, void 0, false, {
                                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                            lineNumber: 220,
                                            columnNumber: 21
                                        }, void 0),
                                        onClick: fetchFriends,
                                        loading: loading,
                                        children: "刷新"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 219,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                lineNumber: 211,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                            lineNumber: 210,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                    lineNumber: 200,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    size: "small",
                    style: {
                        marginBottom: 16
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        split: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                            type: "vertical"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                            lineNumber: 232,
                            columnNumber: 23
                        }, void 0),
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("strong", {
                                        children: "好友总数："
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 234,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "success",
                                        children: friends.length
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 235,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                lineNumber: 233,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("strong", {
                                        children: "当前显示："
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 238,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "primary",
                                        children: filteredFriends.length
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 239,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                lineNumber: 237,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                        lineNumber: 232,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                    lineNumber: 231,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                        spinning: loading,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                                dataSource: paginatedFriends,
                                renderItem: (friend)=>{
                                    var _friend_name;
                                    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                        actions: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                title: "编辑备注",
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "text",
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                        lineNumber: 255,
                                                        columnNumber: 29
                                                    }, void 0),
                                                    onClick: ()=>handleEditRemark(friend),
                                                    size: "small",
                                                    children: "备注"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                    lineNumber: 253,
                                                    columnNumber: 21
                                                }, void 0)
                                            }, "remark", false, {
                                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                lineNumber: 252,
                                                columnNumber: 19
                                            }, void 0),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                                title: "确认删除好友",
                                                description: `确定要删除好友 "${friend.name}" 吗？`,
                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                                    style: {
                                                        color: 'red'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                    lineNumber: 266,
                                                    columnNumber: 27
                                                }, void 0),
                                                onConfirm: ()=>handleRemoveFriend(friend),
                                                okText: "确认删除",
                                                cancelText: "取消",
                                                okType: "danger",
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "text",
                                                    danger: true,
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                        lineNumber: 275,
                                                        columnNumber: 29
                                                    }, void 0),
                                                    loading: removing === friend.id,
                                                    size: "small",
                                                    children: "删除"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                    lineNumber: 272,
                                                    columnNumber: 21
                                                }, void 0)
                                            }, "delete", false, {
                                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                lineNumber: 262,
                                                columnNumber: 19
                                            }, void 0)
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                                            avatar: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                size: 48,
                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                    lineNumber: 288,
                                                    columnNumber: 29
                                                }, void 0),
                                                style: {
                                                    backgroundColor: '#1890ff'
                                                },
                                                children: (_friend_name = friend.name) === null || _friend_name === void 0 ? void 0 : _friend_name.charAt(0).toUpperCase()
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                lineNumber: 286,
                                                columnNumber: 21
                                            }, void 0),
                                            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        strong: true,
                                                        children: friend.name
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                        lineNumber: 296,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    friend.remark && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        type: "secondary",
                                                        style: {
                                                            fontSize: 12
                                                        },
                                                        children: [
                                                            "(",
                                                            friend.remark,
                                                            ")"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                        lineNumber: 298,
                                                        columnNumber: 25
                                                    }, void 0)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                lineNumber: 295,
                                                columnNumber: 21
                                            }, void 0),
                                            description: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                direction: "vertical",
                                                size: 4,
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        type: "secondary",
                                                        children: friend.email
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                        lineNumber: 306,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        type: "secondary",
                                                        style: {
                                                            fontSize: 12
                                                        },
                                                        children: [
                                                            "添加时间: ",
                                                            friend.createdAt ? new Date(friend.createdAt).toLocaleDateString() : '未知'
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                        lineNumber: 307,
                                                        columnNumber: 23
                                                    }, void 0)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                lineNumber: 305,
                                                columnNumber: 21
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                            lineNumber: 284,
                                            columnNumber: 17
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 250,
                                        columnNumber: 15
                                    }, void 0);
                                },
                                locale: {
                                    emptyText: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                                        image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                                        description: searchKeyword ? `没有找到包含 "${searchKeyword}" 的好友` : "暂无好友，点击上方按钮添加好友"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 317,
                                        columnNumber: 17
                                    }, void 0)
                                }
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                lineNumber: 247,
                                columnNumber: 11
                            }, this),
                            filteredFriends.length > pageSize && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    textAlign: 'center',
                                    marginTop: 16
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Pagination, {
                                    current: currentPage,
                                    pageSize: pageSize,
                                    total: filteredFriends.length,
                                    onChange: setCurrentPage,
                                    showSizeChanger: false,
                                    showQuickJumper: true,
                                    showTotal: (total, range)=>`第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                    lineNumber: 332,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                lineNumber: 331,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                        lineNumber: 246,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                    lineNumber: 245,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
            lineNumber: 198,
            columnNumber: 5
        }, this);
    // 添加好友组件
    const renderAddFriend = ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                    level: 4,
                    children: "添加好友"
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                    lineNumber: 353,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: addFriendForm,
                    layout: "vertical",
                    onFinish: (values)=>handleSearchUsers(values.email),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                        label: "搜索用户",
                        name: "email",
                        rules: [
                            {
                                required: true,
                                message: '请输入邮箱'
                            },
                            {
                                type: 'email',
                                message: '请输入有效的邮箱地址'
                            }
                        ],
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Search, {
                            placeholder: "请输入用户邮箱进行搜索",
                            enterButton: "搜索",
                            loading: searchingUsers,
                            onSearch: handleSearchUsers
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                            lineNumber: 367,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                        lineNumber: 359,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                    lineNumber: 354,
                    columnNumber: 7
                }, this),
                searchUsers.length > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        marginTop: 24
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                            level: 5,
                            children: "搜索结果"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                            lineNumber: 379,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                            dataSource: searchUsers,
                            renderItem: (user)=>{
                                var _user_name;
                                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                    actions: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                lineNumber: 388,
                                                columnNumber: 27
                                            }, void 0),
                                            loading: addingFriend === user.id,
                                            onClick: ()=>handleAddFriend(user),
                                            size: "small",
                                            children: "添加好友"
                                        }, "add", false, {
                                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                            lineNumber: 385,
                                            columnNumber: 19
                                        }, void 0)
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                                        avatar: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                            size: 40,
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                                lineNumber: 401,
                                                columnNumber: 29
                                            }, void 0),
                                            style: {
                                                backgroundColor: '#52c41a'
                                            },
                                            children: (_user_name = user.name) === null || _user_name === void 0 ? void 0 : _user_name.charAt(0).toUpperCase()
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                            lineNumber: 399,
                                            columnNumber: 21
                                        }, void 0),
                                        title: user.name,
                                        description: user.email
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 397,
                                        columnNumber: 17
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                    lineNumber: 383,
                                    columnNumber: 15
                                }, void 0);
                            }
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                            lineNumber: 380,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                    lineNumber: 378,
                    columnNumber: 9
                }, this),
                searchUsers.length === 0 && addFriendForm.getFieldValue('email') && !searchingUsers && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                    image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                    description: "未找到匹配的用户",
                    style: {
                        marginTop: 24
                    }
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                    lineNumber: 417,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
            lineNumber: 352,
            columnNumber: 5
        }, this);
    const tabItems = [
        {
            key: 'list',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UnorderedListOutlined, {}, void 0, false, {
                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                        lineNumber: 431,
                        columnNumber: 11
                    }, this),
                    "我的好友",
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Badge, {
                        count: friends.length,
                        showZero: true
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                        lineNumber: 433,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                lineNumber: 430,
                columnNumber: 9
            }, this),
            children: renderFriendList()
        },
        {
            key: 'add',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                        lineNumber: 442,
                        columnNumber: 11
                    }, this),
                    "添加好友"
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                lineNumber: 441,
                columnNumber: 9
            }, this),
            children: renderAddFriend()
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                activeKey: activeTab,
                onChange: setActiveTab,
                items: tabItems,
                size: "large"
            }, void 0, false, {
                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                lineNumber: 452,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "编辑好友备注",
                open: remarkModalVisible,
                onCancel: ()=>{
                    setRemarkModalVisible(false);
                    setCurrentFriend(null);
                    remarkForm.resetFields();
                },
                footer: null,
                width: 400,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: remarkForm,
                    layout: "vertical",
                    onFinish: handleSaveRemark,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "好友备注",
                            name: "remark",
                            rules: [
                                {
                                    max: 50,
                                    message: '备注长度不能超过50个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.TextArea, {
                                placeholder: "为好友添加备注...",
                                rows: 3,
                                maxLength: 50,
                                showCount: true
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                lineNumber: 483,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                            lineNumber: 476,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            style: {
                                marginBottom: 0,
                                textAlign: 'right'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>{
                                            setRemarkModalVisible(false);
                                            setCurrentFriend(null);
                                            remarkForm.resetFields();
                                        },
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 492,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        children: "保存"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                        lineNumber: 499,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                                lineNumber: 491,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                            lineNumber: 490,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                    lineNumber: 471,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
                lineNumber: 460,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/personal-center/components/FriendManageContent.tsx",
        lineNumber: 451,
        columnNumber: 5
    }, this);
};
_s(FriendManageContent, "HNQ3PsfhzwhDkMXHrwOnG8eiQ0M=", false, function() {
    return [
        _antd.Form.useForm,
        _antd.Form.useForm
    ];
});
_c = FriendManageContent;
var _default = FriendManageContent;
var _c;
$RefreshReg$(_c, "FriendManageContent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/personal-center/components/TeamManageContent.tsx": function (module, exports, __mako_require__){
/**
 * 团队管理内容组件 - 用于个人中心
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var _TeamDetailContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/detail/components/TeamDetailContent.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { TextArea } = _antd.Input;
const TeamManageContent = ()=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(false);
    const [teams, setTeams] = (0, _react.useState)([]);
    const [createModalVisible, setCreateModalVisible] = (0, _react.useState)(false);
    const [selectedTeam, setSelectedTeam] = (0, _react.useState)(null);
    const [viewMode, setViewMode] = (0, _react.useState)('list');
    const [createForm] = _antd.Form.useForm();
    (0, _react.useEffect)(()=>{
        fetchTeams();
    }, []);
    /**
   * 获取用户的团队列表
   */ const fetchTeams = async ()=>{
        try {
            setLoading(true);
            const teamList = await _services.TeamService.getUserTeams();
            setTeams(teamList);
        } catch (error) {
            console.error('获取团队列表失败:', error);
            _antd.message.error('获取团队列表失败');
        } finally{
            setLoading(false);
        }
    };
    /**
   * 创建团队
   */ const handleCreateTeam = async (values)=>{
        try {
            await _services.TeamService.createTeam(values);
            _antd.message.success('团队创建成功');
            setCreateModalVisible(false);
            createForm.resetFields();
            fetchTeams();
        } catch (error) {
            console.error('创建团队失败:', error);
            _antd.message.error('创建团队失败');
        }
    };
    /**
   * 查看团队详情
   */ const handleViewTeamDetail = (team)=>{
        setSelectedTeam(team);
        setViewMode('detail');
    };
    /**
   * 返回团队列表
   */ const handleBackToList = ()=>{
        setSelectedTeam(null);
        setViewMode('list');
    };
    // 如果是详情模式，显示团队详情
    if (viewMode === 'detail' && selectedTeam) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamDetailContent.default, {
        teamDetail: selectedTeam,
        loading: false,
        showBackButton: true,
        onBack: handleBackToList,
        onRefresh: ()=>{
            fetchTeams();
        // 刷新团队列表数据
        }
    }, void 0, false, {
        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
        lineNumber: 98,
        columnNumber: 7
    }, this);
    // 默认显示团队列表
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                    fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                    lineNumber: 116,
                    columnNumber: 11
                }, void 0),
                "我的团队"
            ]
        }, void 0, true, {
            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
            lineNumber: 115,
            columnNumber: 9
        }, void 0),
        extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    type: "primary",
                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                        lineNumber: 124,
                        columnNumber: 19
                    }, void 0),
                    onClick: ()=>setCreateModalVisible(true),
                    children: "创建团队"
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                    lineNumber: 122,
                    columnNumber: 11
                }, void 0),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    onClick: fetchTeams,
                    loading: loading,
                    children: "刷新"
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                    lineNumber: 129,
                    columnNumber: 11
                }, void 0)
            ]
        }, void 0, true, {
            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
            lineNumber: 121,
            columnNumber: 9
        }, void 0),
        children: [
            teams.length === 0 && !loading ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                description: "您还没有创建或加入任何团队",
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    type: "primary",
                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                        lineNumber: 145,
                        columnNumber: 19
                    }, void 0),
                    onClick: ()=>setCreateModalVisible(true),
                    children: "创建第一个团队"
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                    lineNumber: 143,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                lineNumber: 139,
                columnNumber: 9
            }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                loading: loading,
                itemLayout: "horizontal",
                dataSource: teams,
                renderItem: (team)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                        actions: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "primary",
                                size: "small",
                                onClick: ()=>handleViewTeamDetail(team),
                                children: "查看详情"
                            }, "view", false, {
                                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                lineNumber: 159,
                                columnNumber: 17
                            }, void 0)
                        ],
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                            avatar: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                    fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                    lineNumber: 172,
                                    columnNumber: 27
                                }, void 0),
                                style: {
                                    backgroundColor: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                lineNumber: 171,
                                columnNumber: 19
                            }, void 0),
                            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    team.name,
                                    team.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                        color: "gold",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                            lineNumber: 180,
                                            columnNumber: 47
                                        }, void 0),
                                        children: "创建者"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                        lineNumber: 180,
                                        columnNumber: 23
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                lineNumber: 177,
                                columnNumber: 19
                            }, void 0),
                            description: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        children: team.description || '暂无描述'
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                        lineNumber: 188,
                                        columnNumber: 21
                                    }, void 0),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        style: {
                                            marginTop: 4,
                                            color: '#666'
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                                            lineNumber: 192,
                                                            columnNumber: 27
                                                        }, void 0),
                                                        " ",
                                                        team.memberCount,
                                                        " 名成员"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                                    lineNumber: 191,
                                                    columnNumber: 25
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                    children: [
                                                        "创建于 ",
                                                        new Date(team.createdAt).toLocaleDateString()
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                                    lineNumber: 194,
                                                    columnNumber: 25
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                            lineNumber: 190,
                                            columnNumber: 23
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                        lineNumber: 189,
                                        columnNumber: 21
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                lineNumber: 187,
                                columnNumber: 19
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                            lineNumber: 169,
                            columnNumber: 15
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                        lineNumber: 157,
                        columnNumber: 13
                    }, void 0)
            }, void 0, false, {
                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                lineNumber: 152,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "创建团队",
                open: createModalVisible,
                onCancel: ()=>{
                    setCreateModalVisible(false);
                    createForm.resetFields();
                },
                footer: null,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: createForm,
                    layout: "vertical",
                    onFinish: handleCreateTeam,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队名称",
                            name: "name",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入团队名称'
                                },
                                {
                                    max: 100,
                                    message: '团队名称不能超过100个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                placeholder: "请输入团队名称"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                lineNumber: 228,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                            lineNumber: 220,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队描述",
                            name: "description",
                            rules: [
                                {
                                    max: 500,
                                    message: '团队描述不能超过500个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                rows: 4,
                                placeholder: "请输入团队描述（可选）"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                lineNumber: 238,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                            lineNumber: 231,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        children: "创建团队"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                        lineNumber: 246,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>{
                                            setCreateModalVisible(false);
                                            createForm.resetFields();
                                        },
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                        lineNumber: 249,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                                lineNumber: 245,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                            lineNumber: 244,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                    lineNumber: 215,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
                lineNumber: 206,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/personal-center/components/TeamManageContent.tsx",
        lineNumber: 113,
        columnNumber: 5
    }, this);
};
_s(TeamManageContent, "PHl6v+YamxUW9QXJcqOTwLzfazI=", false, function() {
    return [
        _antd.Form.useForm
    ];
});
_c = TeamManageContent;
var _default = TeamManageContent;
var _c;
$RefreshReg$(_c, "TeamManageContent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/personal-center/index.tsx": function (module, exports, __mako_require__){
/**
 * 个人中心页面 - 整合个人资料、团队管理、订阅管理
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _services = __mako_require__("src/services/index.ts");
var _UserProfileContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/user/components/UserProfileContent.tsx"));
var _UnifiedSubscriptionContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/subscription/components/UnifiedSubscriptionContent.tsx"));
var _TeamManageContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/components/TeamManageContent.tsx"));
var _FriendManageContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/components/FriendManageContent.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title } = _antd.Typography;
const PersonalCenterPage = ()=>{
    _s();
    const [activeTab, setActiveTab] = (0, _react.useState)('profile');
    const [currentSubscription, setCurrentSubscription] = (0, _react.useState)(null);
    const [subscriptionLoading, setSubscriptionLoading] = (0, _react.useState)(false);
    const [currentTeamDetail, setCurrentTeamDetail] = (0, _react.useState)(null);
    const [teamLoading, setTeamLoading] = (0, _react.useState)(false);
    const { initialState } = (0, _max.useModel)('@@initialState');
    const currentTeam = initialState === null || initialState === void 0 ? void 0 : initialState.currentTeam;
    // 获取当前订阅信息
    const fetchCurrentSubscription = async ()=>{
        try {
            setSubscriptionLoading(true);
            const subscription = await _services.SubscriptionService.getCurrentSubscription();
            setCurrentSubscription(subscription);
        } catch (error) {
            console.error('获取当前订阅失败:', error);
        } finally{
            setSubscriptionLoading(false);
        }
    };
    // 获取当前团队详情
    const fetchCurrentTeamDetail = async ()=>{
        if (!(currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id)) return;
        try {
            setTeamLoading(true);
            const teamDetail = await _services.TeamService.getTeamDetail(currentTeam.id);
            setCurrentTeamDetail(teamDetail);
        } catch (error) {
            console.error('获取团队详情失败:', error);
        } finally{
            setTeamLoading(false);
        }
    };
    // 根据当前选中的标签页加载对应数据
    _react.default.useEffect(()=>{
        if (activeTab.startsWith('subscription')) fetchCurrentSubscription();
        else if (activeTab === 'team-detail') fetchCurrentTeamDetail();
    }, [
        activeTab,
        currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id
    ]);
    const tabItems = [
        {
            key: 'profile',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                        fileName: "src/pages/personal-center/index.tsx",
                        lineNumber: 89,
                        columnNumber: 11
                    }, this),
                    "个人资料"
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 88,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UserProfileContent.default, {}, void 0, false, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 93,
                columnNumber: 17
            }, this)
        },
        {
            key: 'teams',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                        fileName: "src/pages/personal-center/index.tsx",
                        lineNumber: 99,
                        columnNumber: 11
                    }, this),
                    "我的团队"
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 98,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamManageContent.default, {}, void 0, false, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 103,
                columnNumber: 17
            }, this)
        },
        {
            key: 'friends',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UsergroupAddOutlined, {}, void 0, false, {
                        fileName: "src/pages/personal-center/index.tsx",
                        lineNumber: 109,
                        columnNumber: 11
                    }, this),
                    "好友管理"
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 108,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_FriendManageContent.default, {}, void 0, false, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 113,
                columnNumber: 17
            }, this)
        },
        {
            key: 'subscription',
            label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                        fileName: "src/pages/personal-center/index.tsx",
                        lineNumber: 119,
                        columnNumber: 11
                    }, this),
                    "订阅管理"
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 118,
                columnNumber: 9
            }, this),
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UnifiedSubscriptionContent.default, {
                currentSubscription: currentSubscription,
                loading: subscriptionLoading,
                onRefresh: fetchCurrentSubscription
            }, void 0, false, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 124,
                columnNumber: 9
            }, this)
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "个人中心",
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                activeKey: activeTab,
                onChange: setActiveTab,
                items: tabItems,
                size: "large",
                tabPosition: "left"
            }, void 0, false, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 136,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "src/pages/personal-center/index.tsx",
            lineNumber: 135,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/personal-center/index.tsx",
        lineNumber: 134,
        columnNumber: 5
    }, this);
};
_s(PersonalCenterPage, "VMosU3tOpL8D49eATzdu/M4aaVU=", false, function() {
    return [
        _max.useModel
    ];
});
_c = PersonalCenterPage;
var _default = PersonalCenterPage;
var _c;
$RefreshReg$(_c, "PersonalCenterPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src_pages_personal-center_index_tsx-async.js.map