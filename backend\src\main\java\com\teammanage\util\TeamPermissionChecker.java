package com.teammanage.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.teammanage.context.TeamContextHolder;
import com.teammanage.exception.InsufficientPermissionException;

/**
 * 团队权限检查工具类
 *
 * 功能说明：
 * - 提供统一的团队权限验证机制
 * - 基于团队上下文进行权限检查
 * - 支持不同级别的权限控制
 *
 * 权限级别：
 * - 团队管理：只有创建者可以修改团队信息、删除团队
 * - 成员管理：只有创建者可以邀请、移除成员
 * - 数据访问：所有团队成员都可以访问团队数据
 *
 * 使用方式：
 * - 在Service层调用相应的检查方法
 * - 权限不足时抛出InsufficientPermissionException
 * - 配合TeamContextHolder获取当前团队上下文
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class TeamPermissionChecker {

    private static final Logger log = LoggerFactory.getLogger(TeamPermissionChecker.class);

    /**
     * 检查是否可以管理团队
     * 
     * @param isCreator 是否为创建者
     * @return 是否有权限
     */
    public boolean canManageTeam(boolean isCreator) {
        return isCreator;
    }

    /**
     * 检查是否可以管理成员
     * 
     * @param isCreator 是否为创建者
     * @return 是否有权限
     */
    public boolean canManageMembers(boolean isCreator) {
        return isCreator;
    }

    /**
     * 检查是否可以访问数据
     * 
     * @param isCreator 是否为创建者
     * @return 是否有权限
     */
    public boolean canAccessData(boolean isCreator) {
        return true; // 所有成员都可以访问数据
    }

    /**
     * 检查团队管理权限，如果没有权限则抛出异常
     * 
     * @throws InsufficientPermissionException 权限不足异常
     */
    public void checkTeamManagePermission() {
        if (!TeamContextHolder.hasTeamContext()) {
            throw new InsufficientPermissionException("需要团队上下文才能执行此操作");
        }
        
        if (!TeamContextHolder.isCurrentUserCreator()) {
            throw new InsufficientPermissionException("只有团队创建者可以管理团队");
        }
    }

    /**
     * 检查成员管理权限，如果没有权限则抛出异常
     * 
     * @throws InsufficientPermissionException 权限不足异常
     */
    public void checkMemberManagePermission() {
        if (!TeamContextHolder.hasTeamContext()) {
            throw new InsufficientPermissionException("需要团队上下文才能执行此操作");
        }
        
        if (!TeamContextHolder.isCurrentUserCreator()) {
            throw new InsufficientPermissionException("只有团队创建者可以管理成员");
        }
    }

    /**
     * 检查数据访问权限，如果没有权限则抛出异常
     * 
     * @throws InsufficientPermissionException 权限不足异常
     */
    public void checkDataAccessPermission() {
        if (!TeamContextHolder.hasTeamContext()) {
            throw new InsufficientPermissionException("需要团队上下文才能访问数据");
        }
        // 所有团队成员都可以访问数据，无需额外检查
    }

    /**
     * 检查是否为指定团队的成员
     * 
     * @param teamId 团队ID
     * @throws InsufficientPermissionException 权限不足异常
     */
    public void checkTeamMembership(Long teamId) {
        if (!TeamContextHolder.hasTeamContext()) {
            throw new InsufficientPermissionException("需要团队上下文才能执行此操作");
        }
        
        Long currentTeamId = TeamContextHolder.getCurrentTeamId();
        if (!teamId.equals(currentTeamId)) {
            throw new InsufficientPermissionException("无权访问指定团队的数据");
        }
    }

    /**
     * 检查是否为指定用户本人或团队创建者
     * 
     * @param targetUserId 目标用户ID
     * @throws InsufficientPermissionException 权限不足异常
     */
    public void checkUserAccessPermission(Long targetUserId) {
        if (!TeamContextHolder.hasTeamContext()) {
            throw new InsufficientPermissionException("需要团队上下文才能执行此操作");
        }
        
        Long currentUserId = TeamContextHolder.getCurrentUserId();
        boolean isCreator = TeamContextHolder.isCurrentUserCreator();
        
        if (!targetUserId.equals(currentUserId) && !isCreator) {
            throw new InsufficientPermissionException("只能访问自己的信息或需要创建者权限");
        }
    }

}
