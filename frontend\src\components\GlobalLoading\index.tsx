/**
 * 全局加载状态组件
 */

import React, { useState, useEffect } from 'react';
import { Spin } from 'antd';
import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token }) => {
  return {
    container: {
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      zIndex: 9999,
      backdropFilter: 'blur(2px)',
    },
    content: {
      textAlign: 'center',
      padding: '24px',
      backgroundColor: token.colorBgContainer,
      borderRadius: token.borderRadius,
      boxShadow: token.boxShadowTertiary,
    },
  };
});

interface GlobalLoadingProps {
  loading?: boolean;
  tip?: string;
  children?: React.ReactNode;
}

// 全局加载状态管理
class LoadingManager {
  private static instance: LoadingManager;
  private loadingCount = 0;
  private listeners: Array<(loading: boolean) => void> = [];

  static getInstance(): LoadingManager {
    if (!LoadingManager.instance) {
      LoadingManager.instance = new LoadingManager();
    }
    return LoadingManager.instance;
  }

  show(): void {
    this.loadingCount++;
    this.notifyListeners(true);
  }

  hide(): void {
    this.loadingCount = Math.max(0, this.loadingCount - 1);
    if (this.loadingCount === 0) {
      this.notifyListeners(false);
    }
  }

  isLoading(): boolean {
    return this.loadingCount > 0;
  }

  subscribe(listener: (loading: boolean) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(loading: boolean): void {
    this.listeners.forEach(listener => listener(loading));
  }
}

export const loadingManager = LoadingManager.getInstance();

// Hook for using global loading
export const useGlobalLoading = () => {
  const [loading, setLoading] = useState(loadingManager.isLoading());

  useEffect(() => {
    const unsubscribe = loadingManager.subscribe(setLoading);
    return unsubscribe;
  }, []);

  return {
    loading,
    showLoading: () => loadingManager.show(),
    hideLoading: () => loadingManager.hide(),
  };
};

const GlobalLoading: React.FC<GlobalLoadingProps> = ({
  loading: propLoading,
  tip = '加载中...',
  children,
}) => {
  const { styles } = useStyles();
  const { loading: globalLoading } = useGlobalLoading();
  
  const isLoading = propLoading !== undefined ? propLoading : globalLoading;

  if (!isLoading) {
    return <>{children}</>;
  }

  return (
    <>
      {children}
      <div className={styles.container}>
        <div className={styles.content}>
          <Spin size="large" tip={tip} />
        </div>
      </div>
    </>
  );
};

export default GlobalLoading;
