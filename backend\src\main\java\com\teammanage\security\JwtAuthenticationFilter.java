package com.teammanage.security;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import com.teammanage.context.TeamContextHolder;
import com.teammanage.service.UserSessionService;
import com.teammanage.util.JwtTokenUtil;

import io.jsonwebtoken.Claims;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * JWT认证过滤器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger log = LoggerFactory.getLogger(JwtAuthenticationFilter.class);

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    private UserSessionService userSessionService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        String token = getTokenFromRequest(request);
        
        if (StringUtils.hasText(token) && jwtTokenUtil.validateToken(token)) {
            try {
                Claims claims = jwtTokenUtil.getClaimsFromToken(token);
                String jti = claims.get("jti", String.class);

                // 检查会话是否有效
                if (!userSessionService.isSessionValid(jti)) {
                    log.warn("会话无效或已过期: {}", jti);
                    filterChain.doFilter(request, response);
                    return;
                }

                String email = jwtTokenUtil.getEmailFromToken(token);
                Long userId = jwtTokenUtil.getUserIdFromToken(token);
                Long teamId = jwtTokenUtil.getTeamIdFromToken(token);

                // 根据Token中是否包含团队信息确定权限
                List<SimpleGrantedAuthority> authorities = new ArrayList<>();
                authorities.add(new SimpleGrantedAuthority("USER"));
                if (teamId != null) {
                    authorities.add(new SimpleGrantedAuthority("TEAM"));
                }

                // 创建认证对象
                UsernamePasswordAuthenticationToken authentication =
                    new UsernamePasswordAuthenticationToken(
                        email,
                        null,
                        authorities
                    );

                // 设置用户详情
                UserPrincipal userPrincipal = new UserPrincipal();
                userPrincipal.setUserId(userId);
                userPrincipal.setEmail(email);

                authentication.setDetails(userPrincipal);

                // 如果Token包含团队信息，设置团队上下文
                if (teamId != null) {
                    Boolean isCreator = jwtTokenUtil.getIsCreatorFromToken(token);
                    TeamContextHolder.setTeamContext(teamId, userId, isCreator != null ? isCreator : false);
                }

                SecurityContextHolder.getContext().setAuthentication(authentication);

                // 更新会话活动时间
                userSessionService.updateSessionActivity(jti);

            } catch (Exception e) {
                log.error("JWT认证失败", e);
            }
        }
        
        filterChain.doFilter(request, response);
    }

    /**
     * 从请求中获取Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

}
