import React from "react";
import { <PERSON><PERSON>, <PERSON>, Col, Row } from "antd";
 
import TodoManagement from './TodoManagement';
import TeamListCard from './TeamListCard';
import UserProfileCard from "./UserProfileCard";

const FleetManagementDashboard: React.FC = () => {
  return (
    <div style={{ minHeight: "100vh", background: "#f5f8ff" }}>
      <Row gutter={[24, 24]}>
        {/* 个人信息卡片 */}
        <Col xs={24}>
          <UserProfileCard />
        </Col>
      </Row>

      <Row gutter={[24, 24]} style={{ marginTop: 16 }}>
        {/* 待办事项 */}
        <Col xs={24} lg={12}>
          <TodoManagement />
        </Col>

        {/* 团队列表 */}
        <Col xs={24} lg={12}>
          <TeamListCard />
        </Col>
      </Row>


    </div>
  );
};

export default FleetManagementDashboard;