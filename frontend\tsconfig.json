{"compilerOptions": {"baseUrl": "./", "target": "esnext", "moduleResolution": "node", "jsx": "react-jsx", "esModuleInterop": true, "experimentalDecorators": true, "strict": true, "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "declaration": true, "skipLibCheck": true, "resolveJsonModule": true, "paths": {"@/*": ["./src/*"], "@@/*": ["./src/.umi/*"], "@@test/*": ["./src/.umi-test/*"]}}, "include": ["./**/*.d.ts", "./**/*.ts", "./**/*.tsx"]}