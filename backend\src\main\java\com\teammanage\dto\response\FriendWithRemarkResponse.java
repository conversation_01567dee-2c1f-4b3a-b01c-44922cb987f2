package com.teammanage.dto.response;

/**
 * 包含备注信息的好友响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class FriendWithRemarkResponse {
    
    /**
     * 好友ID
     */
    private Long id;
    
    /**
     * 好友邮箱
     */
    private String email;
    
    /**
     * 好友姓名
     */
    private String name;
    
    /**
     * 好友备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private String createdAt;
    
    /**
     * 更新时间
     */
    private String updatedAt;
    
    // 构造函数
    public FriendWithRemarkResponse() {}
    
    public FriendWithRemarkResponse(Long id, String email, String name, String remark, String createdAt, String updatedAt) {
        this.id = id;
        this.email = email;
        this.name = name;
        this.remark = remark;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
    
    // Getter and Setter methods
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
    
    public String getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }
    
    public String getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }
}
