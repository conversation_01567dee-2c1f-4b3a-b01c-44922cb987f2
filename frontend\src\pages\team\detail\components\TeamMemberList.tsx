/**
 * 团队成员列表组件
 *
 * 功能特性：
 * - 展示团队所有成员信息（头像、姓名、邮箱、角色、状态等）
 * - 支持成员搜索和筛选功能
 * - 提供成员管理操作（移除成员、角色变更等）
 * - 区分创建者和普通成员的权限显示
 * - 响应式表格设计，适配不同屏幕尺寸
 *
 * 权限控制：
 * - 只有团队创建者可以看到管理操作按钮
 * - 创建者不能移除自己
 * - 普通成员只能查看成员列表
 *
 * 交互设计：
 * - 支持批量操作（预留功能）
 * - 提供详细的操作确认对话框
 * - 实时更新成员状态和数量
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Avatar,
  Tag,
  Button,
  Space,
  message,
  Modal,
  Input,
  Tooltip,
  Dropdown,
  Checkbox,
  Typography,
  Badge,
  Divider,
  Select
} from 'antd';
import {
  UserOutlined,
  CrownOutlined,
  DeleteOutlined,
  SearchOutlined,
  MoreOutlined,
  UserSwitchOutlined,
  StopOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  FilterOutlined,
  HarmonyOSOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { MenuProps } from 'antd';
import { TeamService } from '@/services';
import type { TeamMemberResponse } from '@/types/api';

const { Text } = Typography;
const { Option } = Select;

/**
 * 团队成员列表组件的Props接口
 */
interface TeamMemberListProps {
  /** 团队ID，用于获取成员列表 */
  teamId: number;
  /** 当前用户是否为团队创建者，控制管理功能的显示 */
  isCreator: boolean;
  /** 成员变更时的回调函数，用于通知父组件刷新数据 */
  onMemberChange?: () => void;
}

const TeamMemberList: React.FC<TeamMemberListProps> = ({
  teamId,
  isCreator,
  onMemberChange,
}) => {
  const [loading, setLoading] = useState(true);
  const [members, setMembers] = useState<TeamMemberResponse[]>([]);
  const [searchText, setSearchText] = useState('');
  const [filteredMembers, setFilteredMembers] = useState<TeamMemberResponse[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [statusFilter, setStatusFilter] = useState<string>('all');

  useEffect(() => {
    fetchMembers();
  }, [teamId]);

  /**
   * 成员列表过滤效果
   *
   * 过滤条件：
   * 1. 搜索文本：匹配成员姓名或邮箱（不区分大小写）
   * 2. 状态筛选：全部/活跃/非活跃/创建者/普通成员
   *
   * 安全性：
   * - 添加空值检查，防止数据异常导致的错误
   * - 确保成员对象的必要属性存在
   */
  useEffect(() => {
    // 过滤成员列表 - 添加空值检查
    if (!members || !Array.isArray(members)) {
      setFilteredMembers([]);
      return;
    }

    const filtered = members.filter(member => {
      // 确保member对象存在且有必要的属性
      if (!member || !member.name || !member.email) {
        return false;
      }

      // 搜索文本匹配（姓名或邮箱）
      const matchesSearch = !searchText ||
        member.name.toLowerCase().includes(searchText.toLowerCase()) ||
        member.email.toLowerCase().includes(searchText.toLowerCase());

      // 状态筛选匹配
      const matchesStatus = statusFilter === 'all' ||
        (statusFilter === 'active' && member.isActive) ||
        (statusFilter === 'inactive' && !member.isActive) ||
        (statusFilter === 'creator' && member.isCreator) ||
        (statusFilter === 'member' && !member.isCreator);

      return matchesSearch && matchesStatus;
    });
    setFilteredMembers(filtered);
  }, [members, searchText, statusFilter]);

  /**
   * 获取团队成员列表
   *
   * 功能：
   * - 调用API获取当前团队的所有成员
   * - 设置加载状态，提供用户反馈
   * - 处理错误情况，确保组件稳定性
   *
   * 数据处理：
   * - 确保返回数据为数组格式，防止渲染错误
   * - 错误时设置空数组，保持组件正常显示
   */
  const fetchMembers = async () => {
    try {
      setLoading(true);
      const response = await TeamService.getTeamMembers({ current: 1, pageSize: 1000 });
      // 确保返回的数据是数组格式，防止渲染错误
      setMembers(response?.list || []);
    } catch (error) {
      console.error('获取团队成员失败:', error);
      message.error('获取团队成员失败');
      // 出错时设置为空数组，保持组件正常显示
      setMembers([]);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveMember = (member: TeamMemberResponse) => {
    if (member.isCreator) {
      message.warning('不能移除团队创建者');
      return;
    }

    Modal.confirm({
      title: '确认移除成员',
      content: `确定要移除成员 "${member.name}" 吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          await TeamService.removeMember(member.id);
          message.success('成员移除成功');
          fetchMembers();
          onMemberChange?.();
        } catch (error) {
          console.error('移除成员失败:', error);
        }
      },
    });
  };

  const handleBatchRemove = () => {
    const selectedMembers = members.filter(member =>
      selectedRowKeys.includes(member.id) && !member.isCreator
    );

    if (selectedMembers.length === 0) {
      message.warning('请选择要移除的成员');
      return;
    }

    Modal.confirm({
      title: '批量移除成员',
      content: `确定要移除选中的 ${selectedMembers.length} 名成员吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          await Promise.all(
            selectedMembers.map(member => TeamService.removeMember(member.id))
          );
          message.success(`成功移除 ${selectedMembers.length} 名成员`);
          setSelectedRowKeys([]);
          fetchMembers();
          onMemberChange?.();
        } catch (error) {
          console.error('批量移除成员失败:', error);
          message.error('批量移除失败');
        }
      },
    });
  };

  const columns: ColumnsType<TeamMemberResponse> = [
    {
      title: '成员',
      dataIndex: 'name',
      key: 'name',
      render: (name, record) => (
        <Space>
          <Avatar size="small" icon={<UserOutlined />} />
          <div>
            <div>{name}</div>
            <div style={{ fontSize: 12, color: '#999' }}>{record.email}</div>
          </div>
        </Space>
      ),
    },
    {
      title: '角色',
      dataIndex: 'isCreator',
      key: 'role',
      width: 100,
      render: (isCreator) => (
        <Tag color={isCreator ? 'gold' : 'blue'} icon={isCreator ? <CrownOutlined /> : <UserOutlined />}>
          {isCreator ? '创建者' : '成员'}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'status',
      width: 80,
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '活跃' : '停用'}
        </Tag>
      ),
    },
    {
      title: '加入时间',
      dataIndex: 'assignedAt',
      key: 'assignedAt',
      width: 150,
      render: (assignedAt) => new Date(assignedAt).toLocaleDateString(),
    },
    {
      title: '最后访问',
      dataIndex: 'lastAccessTime',
      key: 'lastAccessTime',
      width: 150,
      render: (lastAccessTime) => {
        const date = new Date(lastAccessTime);
        const now = new Date();
        const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
        
        let color = 'green';
        if (diffDays > 7) color = 'orange';
        if (diffDays > 30) color = 'red';
        
        return (
          <Tooltip title={date.toLocaleString()}>
            <Tag color={color}>
              {diffDays === 0 ? '今天' : `${diffDays}天前`}
            </Tag>
          </Tooltip>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => {
        if (!isCreator || record.isCreator) {
          return <Text type="secondary">-</Text>;
        }

        const menuItems: MenuProps['items'] = [
          {
            key: 'remove',
            label: '移除成员',
            icon: <DeleteOutlined />,
            danger: true,
            onClick: () => handleRemoveMember(record),
          },
        ];

        return (
          <Space size="small">
            <Button
              type="text"
              danger
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => handleRemoveMember(record)}
            >
              移除
            </Button>
            <Dropdown menu={{ items: menuItems }} trigger={['click']}>
              <Button
                type="text"
                size="small"
                icon={<HarmonyOSOutlined />}
              />
            </Dropdown>
          </Space>
        );
      },
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
    getCheckboxProps: (record: TeamMemberResponse) => ({
      disabled: record.isCreator, // 创建者不能被选中
    }),
  };

  return (
    <Card
      title={
        <Space>
          <Text strong>团队成员</Text>
          <Badge count={filteredMembers.length} showZero />
        </Space>
      }
      extra={
        <Space>
          <Select
            value={statusFilter}
            onChange={setStatusFilter}
            style={{ width: 120 }}
            size="small"
          >
            <Option value="all">全部</Option>
            <Option value="active">活跃</Option>
            <Option value="inactive">停用</Option>
            <Option value="creator">创建者</Option>
            <Option value="member">成员</Option>
          </Select>
          <Input
            placeholder="搜索成员"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 200 }}
            size="small"
          />
        </Space>
      }
    >
      {selectedRowKeys.length > 0 && isCreator && (
        <div style={{ marginBottom: 16, padding: 12, background: '#f5f5f5', borderRadius: 6 }}>
          <Space>
            <Text>已选择 {selectedRowKeys.length} 名成员</Text>
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={handleBatchRemove}
            >
              批量移除
            </Button>
            <Button
              size="small"
              onClick={() => setSelectedRowKeys([])}
            >
              取消选择
            </Button>
          </Space>
        </div>
      )}

      <Table
        columns={columns}
        dataSource={filteredMembers}
        rowKey="id"
        loading={loading}
        rowSelection={isCreator ? rowSelection : undefined}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 名成员`,
          pageSize: 10,
        }}
      />
    </Card>
  );
};

export default TeamMemberList;
