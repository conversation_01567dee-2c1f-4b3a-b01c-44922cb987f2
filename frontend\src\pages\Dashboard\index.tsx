/**
 * 仪表板页面
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Typography, 
  Space, 
  Button,
  List,
  Avatar,
  Tag,
  Progress,
  Alert
} from 'antd';
import { 
  TeamOutlined, 
  UserOutlined, 
  CrownOutlined,
  DatabaseOutlined,
  CalendarOutlined,
  RightOutlined
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { TeamService, UserService, SubscriptionService } from '@/services';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [teamStats, setTeamStats] = useState<any>(null);
  const [userProfile, setUserProfile] = useState<any>(null);
  const [subscriptionStatus, setSubscriptionStatus] = useState<any>(null);
  const [usageInfo, setUsageInfo] = useState<any>(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const [stats, profile, subStatus, usage] = await Promise.all([
        TeamService.getTeamStats().catch(() => null),
        UserService.getUserProfile().catch(() => null),
        SubscriptionService.checkSubscriptionStatus().catch(() => null),
        SubscriptionService.getSubscriptionUsage().catch(() => null),
      ]);
      
      setTeamStats(stats);
      setUserProfile(profile);
      setSubscriptionStatus(subStatus);
      setUsageInfo(usage);
    } catch (error) {
      console.error('获取仪表板数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      title: '邀请成员',
      description: '邀请新成员加入团队',
      icon: <UserOutlined />,
      action: () => history.push('/team/detail'),
    },
    {
      title: '查看团队',
      description: '查看团队详情和成员',
      icon: <TeamOutlined />,
      action: () => history.push('/team/detail'),
    },
    {
      title: '管理订阅',
      description: '查看和管理订阅套餐',
      icon: <CrownOutlined />,
      action: () => history.push('/subscription/manage'),
    },
    {
      title: '个人设置',
      description: '修改个人资料和偏好',
      icon: <UserOutlined />,
      action: () => history.push('/user/profile'),
    },
  ];

  return (
    <PageContainer
      title="仪表板"
      subTitle="欢迎回来！这里是您的工作概览"
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 警告信息 */}
        {subscriptionStatus?.isExpiringSoon && (
          <Alert
            message="订阅即将到期"
            description={`您的订阅将在 ${subscriptionStatus.daysUntilExpiry} 天后到期，请及时续费。`}
            type="warning"
            showIcon
            action={
              <Button size="small" onClick={() => history.push('/subscription/manage')}>
                立即续费
              </Button>
            }
          />
        )}

        {usageInfo?.usagePercentage > 80 && (
          <Alert
            message="存储空间不足"
            description={`您的存储空间使用量已达到 ${usageInfo.usagePercentage.toFixed(1)}%，建议升级套餐。`}
            type="warning"
            showIcon
            action={
              <Button size="small" onClick={() => history.push('/subscription/plans')}>
                升级套餐
              </Button>
            }
          />
        )}

        {/* 统计卡片 */}
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="团队成员"
                value={teamStats?.memberCount || 0}
                prefix={<TeamOutlined />}
                suffix="人"
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="活跃成员"
                value={teamStats?.activeMembers || 0}
                prefix={<UserOutlined />}
                suffix="人"
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="存储使用"
                value={usageInfo?.usagePercentage || 0}
                prefix={<DatabaseOutlined />}
                suffix="%"
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="剩余天数"
                value={usageInfo?.remainingDays || 0}
                prefix={<CalendarOutlined />}
                suffix="天"
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          {/* 存储使用情况 */}
          <Col xs={24} lg={12}>
            <Card title="存储使用情况" loading={loading}>
              {usageInfo && (
                <div>
                  <div style={{ marginBottom: 16 }}>
                    <Text>已使用 {usageInfo.currentUsage}GB / {usageInfo.maxUsage}GB</Text>
                  </div>
                  <Progress
                    percent={usageInfo.usagePercentage}
                    status={usageInfo.usagePercentage > 80 ? 'exception' : 'normal'}
                    strokeColor={usageInfo.usagePercentage > 80 ? '#ff4d4f' : '#1890ff'}
                  />
                  <div style={{ marginTop: 8, textAlign: 'right' }}>
                    <Button
                      type="link"
                      size="small"
                      onClick={() => history.push('/subscription/plans')}
                    >
                      升级存储空间
                    </Button>
                  </div>
                </div>
              )}
            </Card>
          </Col>

          {/* 快速操作 */}
          <Col xs={24} lg={12}>
            <Card title="快速操作">
              <List
                dataSource={quickActions}
                renderItem={(item) => (
                  <List.Item
                    style={{ cursor: 'pointer' }}
                    onClick={item.action}
                  >
                    <List.Item.Meta
                      avatar={<Avatar icon={item.icon} />}
                      title={item.title}
                      description={item.description}
                    />
                    <RightOutlined />
                  </List.Item>
                )}
              />
            </Card>
          </Col>
        </Row>

        {/* 用户信息和订阅状态 */}
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="个人信息" loading={loading}>
              {userProfile && (
                <div>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <Text strong>用户名：</Text>
                      <Text>{userProfile.name}</Text>
                    </div>
                    <div>
                      <Text strong>邮箱：</Text>
                      <Text>{userProfile.email}</Text>
                    </div>
                    <div>
                      <Text strong>注册时间：</Text>
                      <Text>{new Date(userProfile.createdAt).toLocaleDateString()}</Text>
                    </div>
                  </Space>
                  <div style={{ marginTop: 16, textAlign: 'right' }}>
                    <Button
                      type="link"
                      size="small"
                      onClick={() => history.push('/user/profile')}
                    >
                      编辑资料
                    </Button>
                  </div>
                </div>
              )}
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <Card title="订阅状态" loading={loading}>
              {subscriptionStatus && (
                <div>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <Text strong>订阅状态：</Text>
                      <Tag color={subscriptionStatus.hasActiveSubscription ? 'green' : 'red'}>
                        {subscriptionStatus.hasActiveSubscription ? '已订阅' : '未订阅'}
                      </Tag>
                    </div>
                    {subscriptionStatus.hasActiveSubscription && (
                      <>
                        <div>
                          <Text strong>到期时间：</Text>
                          <Text>{subscriptionStatus.daysUntilExpiry} 天后</Text>
                        </div>
                        {subscriptionStatus.needsUpgrade && (
                          <div>
                            <Tag color="orange">建议升级</Tag>
                            <Text type="secondary">存储使用量较高</Text>
                          </div>
                        )}
                      </>
                    )}
                  </Space>
                  <div style={{ marginTop: 16, textAlign: 'right' }}>
                    <Button
                      type="link"
                      size="small"
                      onClick={() => history.push('/subscription/manage')}
                    >
                      管理订阅
                    </Button>
                  </div>
                </div>
              )}
            </Card>
          </Col>
        </Row>
      </Space>
    </PageContainer>
  );
};

export default Dashboard;
