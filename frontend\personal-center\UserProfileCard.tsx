import {
  CalendarOutlined,
  CarOutlined,
  CheckOutlined,
  EditOutlined,
  LogoutOutlined,
  MailOutlined,
  PhoneOutlined,
  SettingOutlined,
  TagOutlined,
  TeamOutlined,
  UserOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import {
  Avatar,
  Button,
  Card,
  Col,
  Divider,
  Dropdown,
  Flex,
  Form,
  Input,
  Menu,
  Modal,
  Row,
  Space,
  Steps,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import React, { useState } from "react";

const { Title, Text } = Typography;
const { Step } = Steps;

const UserProfileCard: React.FC = () => {
  // 个人信息数据
  const userInfo = {
    name: "张明",
    position: "车队管理员",
    email: "<EMAIL>",
    phone: "13800138000",
    registerDate: "2020年5月10日",
    lastLoginTime: "2025年7月25日 18:30:45",
    lastLoginTeam: "运输车队管理员",
    teamCount: 8, // 新增团队总数
  };

  // 个人统计数据
  const personalStats = {
    vehicles: 48,
    personnel: 16,
    warnings: 5,
    alerts: 3,
  };

  // 订阅计划数据
  const subscriptionPlans = [
    {
      id: "basic",
      name: "基础版",
      price: 0,
      description: "适合小团队使用",
      features: ["最多5个团队", "最多20辆车辆", "基础安全监控", "基本报告功能"],
    },
    {
      id: "professional",
      name: "专业版",
      price: 199,
      description: "适合中小型企业",
      features: [
        "最多20个团队",
        "最多100辆车辆",
        "高级安全监控",
        "详细分析报告",
        "设备状态预警",
        "优先技术支持",
      ],
    },
    {
      id: "enterprise",
      name: "企业版",
      price: 499,
      description: "适合大型企业",
      features: [
        "不限团队数量",
        "不限车辆数量",
        "AI安全分析",
        "实时监控告警",
        "定制化报告",
        "专属客户经理",
        "24/7技术支持",
      ],
    },
  ];

  // 当前订阅信息
  const currentSubscription = {
    planId: "basic",
    expires: "2025-12-31",
  };

  // 状态管理
  const [editProfileModalVisible, setEditProfileModalVisible] = useState(false);
  const [subscriptionModalVisible, setSubscriptionModalVisible] =
    useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [editProfileForm] = Form.useForm();

  return (
    <>
      <Card
        className="dashboard-card"
        style={{
          borderRadius: 12,
          boxShadow: "0 2px 8px rgba(0,0,0,0.05)",
          border: "none",
          background: "linear-gradient(145deg, #ffffff, #f0f7ff)",
          position: "relative",
        }}
      >
        {/* 设置按钮和退出登录按钮容器 */}
        <Flex
          gap={2}
          align="center"
          style={{
            position: "absolute",
            top: 12,
            right: 12,
            zIndex: 1,
          }}
        >
          <Dropdown
            overlay={
              <Menu>
                <Menu.Item
                  key="editProfile"
                  icon={<EditOutlined />}
                  onClick={() => {
                    setEditProfileModalVisible(true);
                    setCurrentStep(0);
                    editProfileForm.setFieldsValue({
                      name: userInfo.name,
                      email: userInfo.email,
                      phone: userInfo.phone,
                    });
                  }}
                >
                  修改资料
                </Menu.Item>
                <Menu.Item
                  key="subscription"
                  icon={<TagOutlined />}
                  onClick={() => setSubscriptionModalVisible(true)}
                >
                  订阅套餐
                </Menu.Item>
              </Menu>
            }
            trigger={["click"]}
          >
            <Button
              type="text"
              icon={
                <SettingOutlined style={{ fontSize: 16, color: "#8c8c8c" }} />
              }
              style={{ padding: "4px 6px" }}
            />
          </Dropdown>

          {/* 退出登录按钮 */}
          <Button
            type="text"
            icon={<LogoutOutlined style={{ fontSize: 16, color: "#8c8c8c" }} />}
            onClick={() => {
              console.log("执行退出登录操作");
              // 实际项目中这里会调用退出登录API
            }}
            style={{ padding: "4px 6px" }}
          />
        </Flex>

        {/* 三列布局 */}
        <Row gutter={0} style={{ margin: 0 }}>
          {/* 第一列：姓名、头像和邮箱等信息 */}
          <Col xs={24} sm={8}>
            <Flex align="center">
              <Avatar
                size={52}
                style={{
                  backgroundColor: "#1890ff",
                  fontSize: 18,
                  boxShadow: "0 0 0 3px rgba(24, 144, 255, 0.2)",
                  marginRight: 10,
                }}
              >
                {userInfo.name.substring(0, 1)}
              </Avatar>

              <Space direction="vertical" size={0}>
                <Flex align="center" style={{ marginBottom: 4 }}>
                  <Title level={4} style={{ margin: 0, fontSize: 16 }}>
                    {userInfo.name}
                  </Title>
                  <Tooltip title="最后登录时间">
                    <Text
                      type="secondary"
                      style={{ marginLeft: 8, fontSize: 11 }}
                    >
                      <CalendarOutlined style={{ marginRight: 2 }} />
                      {userInfo.lastLoginTime}
                    </Text>
                  </Tooltip>
                </Flex>
                {/* 邮箱、电话和注册日期信息 */}
                <Flex wrap="wrap" gap={6}>
                  <Tag
                    icon={<MailOutlined style={{ fontSize: 12 }} />}
                    style={{ fontSize: 13 }}
                  >
                    {userInfo.email}
                  </Tag>
                  <Tag
                    icon={<PhoneOutlined style={{ fontSize: 12 }} />}
                    style={{ fontSize: 13 }}
                  >
                    {userInfo.phone}
                  </Tag>
                  <Tag
                    icon={<CalendarOutlined style={{ fontSize: 12 }} />}
                    style={{ fontSize: 13 }}
                  >
                    {userInfo.registerDate}
                  </Tag>
                </Flex>
              </Space>
            </Flex>
          </Col>

          {/* 第二列：车辆人员预警告警统计 */}
          <Col xs={24} sm={10}>
            <Row gutter={8} justify="space-around">
              {/* 车辆统计 */}
              <Col>
                <Space direction="vertical">
                  <Flex
                    align="center"
                    style={{ color: "#595959", fontSize: 12 }}
                  >
                    <CarOutlined />
                    <Text style={{ marginLeft: 4 }}>车辆</Text>
                  </Flex>
                  <Text
                    strong
                    style={{
                      fontSize: 24,
                      color: "#1890ff",
                      fontWeight: 600,
                      lineHeight: "28px",
                    }}
                  >
                    {personalStats.vehicles}
                  </Text>
                </Space>
              </Col>

              {/* 人员统计 */}
              <Col>
                <Space direction="vertical">
                  <Flex
                    align="center"
                    style={{ color: "#595959", fontSize: 12 }}
                  >
                    <UserOutlined />
                    <Text style={{ marginLeft: 4 }}>人员</Text>
                  </Flex>
                  <Text
                    strong
                    style={{
                      fontSize: 24,
                      color: "#52c41a",
                      fontWeight: 600,
                      lineHeight: "28px",
                    }}
                  >
                    {personalStats.personnel}
                  </Text>
                </Space>
              </Col>

              {/* 预警统计 */}
              <Col>
                <Space direction="vertical">
                  <Flex
                    align="center"
                    style={{ color: "#595959", fontSize: 12 }}
                  >
                    <WarningOutlined />
                    <Text style={{ marginLeft: 4 }}>预警</Text>
                  </Flex>
                  <Text
                    strong
                    style={{
                      fontSize: 24,
                      color: "#faad14",
                      fontWeight: 600,
                      lineHeight: "28px",
                    }}
                  >
                    {personalStats.warnings}
                  </Text>
                </Space>
              </Col>

              {/* 告警统计 */}
              <Col>
                <Space direction="vertical">
                  <Flex
                    align="center"
                    style={{ color: "#595959", fontSize: 12 }}
                  >
                    <WarningOutlined style={{ color: "#ff4d4f" }} />
                    <Text style={{ marginLeft: 4 }}>告警</Text>
                  </Flex>
                  <Text
                    strong
                    style={{
                      fontSize: 24,
                      color: "#ff4d4f",
                      fontWeight: 600,
                      lineHeight: "28px",
                    }}
                  >
                    {personalStats.alerts}
                  </Text>
                </Space>
              </Col>
            </Row>
          </Col>

          {/* 第三列：最后登录信息 */}
          <Col xs={24} sm={6}>
            <Space direction="vertical">
              <Flex align="center">
                <TeamOutlined
                  style={{ color: "#8c8c8c", marginRight: 6, fontSize: 12 }}
                />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  最后登录团队: <Text strong>{userInfo.lastLoginTeam}</Text>
                </Text>
              </Flex>
              <Flex align="center">
                <TeamOutlined
                  style={{ color: "#8c8c8c", marginRight: 6, fontSize: 12 }}
                />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  团队总数: <Text strong>{userInfo.teamCount}</Text>
                </Text>
              </Flex>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 修改资料模态框 */}
      <Modal
        title="修改个人资料"
        open={editProfileModalVisible}
        onCancel={() => {
          setEditProfileModalVisible(false);
          setCurrentStep(0);
        }}
        footer={[
          currentStep === 1 && (
            <Button key="back" onClick={() => setCurrentStep(0)}>
              上一步
            </Button>
          ),
          <Button
            key="submit"
            type="primary"
            onClick={() => {
              if (currentStep === 0) {
                editProfileForm.validateFields().then(() => {
                  setCurrentStep(1);
                });
              } else {
                editProfileForm.validateFields().then((values) => {
                  console.log("个人资料表单值:", values);
                  // 提交表单，这里简化处理，只输出到控制台
                  setEditProfileModalVisible(false);
                  setCurrentStep(0);
                });
              }
            }}
          >
            {currentStep === 0 ? "下一步" : "确定"}
          </Button>,
        ]}
      >
        <Steps current={currentStep} style={{ marginBottom: 16 }}>
          <Step title="填写信息" />
          <Step title="安全验证" />
        </Steps>

        <Form form={editProfileForm} layout="vertical" hideRequiredMark>
          {currentStep === 0 ? (
            <>
              <Form.Item
                name="name"
                label="用户名"
                rules={[{ required: true, message: "请输入用户名" }]}
              >
                <Input placeholder="请输入用户名" />
              </Form.Item>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: "请输入邮箱地址" },
                  { type: "email", message: "请输入有效的邮箱地址" },
                ]}
              >
                <Input placeholder="请输入邮箱地址" />
              </Form.Item>
              <Form.Item
                name="phone"
                label="手机号"
                rules={[
                  { required: true, message: "请输入手机号" },
                  { pattern: /^1\d{10}$/, message: "请输入有效的手机号" },
                ]}
              >
                <Input placeholder="请输入手机号" />
              </Form.Item>
            </>
          ) : (
            <div style={{ textAlign: "center" }}>
              <div style={{ margin: "12px 0", textAlign: "center" }}>
                <Text>
                  验证码已发送至您的手机号{" "}
                  <Text strong>{editProfileForm.getFieldValue("phone")}</Text>
                </Text>
              </div>
              <Form.Item
                name="verificationCode"
                label="验证码"
                rules={[{ required: true, message: "请输入验证码" }]}
              >
                <Input
                  placeholder="请输入6位验证码"
                  maxLength={6}
                  style={{ width: "50%", textAlign: "center" }}
                />
              </Form.Item>
              <Button type="link" style={{ padding: 0 }}>
                重新发送验证码
              </Button>
            </div>
          )}
        </Form>
      </Modal>

      {/* 订阅套餐模态框 */}
      <Modal
        title="订阅套餐"
        open={subscriptionModalVisible}
        onCancel={() => setSubscriptionModalVisible(false)}
        footer={null}
        width={800}
      >
        <div
          style={{
            background: "#f9f9f9",
            padding: 12,
            borderRadius: 8,
            marginBottom: 16,
          }}
        >
          <Flex justify="space-between" align="center">
            <Text strong>当前套餐: </Text>
            <Tag color="green" style={{ marginLeft: 8, fontSize: 13 }}>
              {
                subscriptionPlans.find(
                  (p) => p.id === currentSubscription.planId
                )?.name
              }
            </Tag>
            <Text type="secondary">
              到期时间: {currentSubscription.expires}
            </Text>
          </Flex>
        </div>

        <Row gutter={24}>
          {subscriptionPlans.map((plan) => (
            <Col span={8} key={plan.id}>
              <div
                style={{
                  height: "100%",
                  borderRadius: 8,
                  border: `1px solid ${
                    plan.id === currentSubscription.planId
                      ? "#52c41a"
                      : "#d9d9d9"
                  }`,
                  background: "#fff",
                  position: "relative",
                  overflow: "hidden",
                }}
              >
                {plan.id === currentSubscription.planId && (
                  <Tag
                    color="green"
                    style={{
                      position: "absolute",
                      top: -10,
                      right: -10,
                      borderRadius: 2,
                      boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                    }}
                  >
                    当前套餐
                  </Tag>
                )}
                <div style={{ padding: 16 }}>
                  <Title
                    level={4}
                    style={{ textAlign: "center", margin: "12px 0 8px" }}
                  >
                    {plan.name}
                  </Title>
                  <Flex vertical align="center" style={{ marginBottom: 12 }}>
                    {plan.price > 0 ? (
                      <>
                        <Title level={2} style={{ marginBottom: 0 }}>
                          ¥{plan.price}
                        </Title>
                        <Text type="secondary">/月</Text>
                      </>
                    ) : (
                      <Title
                        level={2}
                        style={{ color: "#52c41a", marginBottom: 0 }}
                      >
                        免费
                      </Title>
                    )}
                    <Text type="secondary" style={{ marginTop: 4 }}>
                      {plan.description}
                    </Text>
                  </Flex>

                  <Divider style={{ margin: "8px 0" }} />

                  <div style={{ minHeight: 170 }}>
                    {plan.features.map((feature, index) => (
                      <Space
                        key={index}
                        align="start"
                        style={{ marginBottom: 6 }}
                      >
                        <CheckOutlined
                          style={{
                            color: "#52c41a",
                            marginRight: 8,
                            marginTop: 4,
                          }}
                        />
                        <Text>{feature}</Text>
                      </Space>
                    ))}
                  </div>

                  {plan.id !== currentSubscription.planId ? (
                    <Button
                      type="primary"
                      block
                      style={{
                        marginTop: 12,
                        boxShadow: "0 2px 8px rgba(24, 144, 255, 0.3)",
                      }}
                      onClick={() => {
                        console.log("选择套餐:", plan);
                        setSubscriptionModalVisible(false);
                      }}
                    >
                      立即订阅
                    </Button>
                  ) : (
                    <Button
                      block
                      style={{
                        marginTop: 12,
                        background: "#f6ffed",
                        borderColor: "#b7eb8f",
                        color: "#389e0d",
                      }}
                      disabled
                    >
                      当前套餐
                    </Button>
                  )}
                </div>
              </div>
            </Col>
          ))}
        </Row>

        <Flex justify="center" style={{ marginTop: 20 }}>
          <Text type="secondary">订阅服务自动续费，可随时取消</Text>
        </Flex>
      </Modal>
    </>
  );
};

export default UserProfileCard;
