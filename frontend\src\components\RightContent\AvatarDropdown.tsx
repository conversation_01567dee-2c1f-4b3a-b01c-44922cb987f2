import {
  LogoutOutlined,
  SwapOutlined,
} from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import type { MenuProps } from 'antd';
import { Spin, message, Avatar, Typography } from 'antd';
import { createStyles } from 'antd-style';
import React from 'react';
import { flushSync } from 'react-dom';
import { AuthService } from '@/services';
import HeaderDropdown from '../HeaderDropdown';

const { Text } = Typography;

export type GlobalHeaderRightProps = {
  menu?: boolean;
  children?: React.ReactNode;
};

export const AvatarName = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  return <span className="anticon">{currentUser?.name}</span>;
};

const useStyles = createStyles(({ token }) => {
  return {
    action: {
      display: 'flex',
      height: '48px',
      marginLeft: 'auto',
      overflow: 'hidden',
      alignItems: 'center',
      padding: '0 8px',
      cursor: 'pointer',
      borderRadius: token.borderRadius,
      '&:hover': {
        backgroundColor: token.colorBgTextHover,
      },
    },
    dropdownItem: {
      display: 'flex',
      alignItems: 'center',
      gap: 8,
      padding: '8px 12px',
      minWidth: 200,
    },
    currentTeamItem: {
      backgroundColor: token.colorPrimaryBg,
      borderRadius: token.borderRadius,
    },
    sectionTitle: {
      padding: '8px 12px 4px',
      fontSize: 12,
      fontWeight: 600,
      color: token.colorTextSecondary,
      textTransform: 'uppercase',
      letterSpacing: '0.5px',
    },
    teamInfo: {
      display: 'flex',
      alignItems: 'center',
      gap: 8,
    },
  };
});

export const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({
  menu,
  children,
}) => {

  /**
   * 退出登录
   */
  const loginOut = async () => {
    try {
      await AuthService.logout();
      message.success('已退出登录');
    } catch (error) {
      console.error('退出登录失败:', error);
    } finally {
      // 无论是否成功，都跳转到登录页
      history.push('/user/login');
    }
  };

  const { styles } = useStyles();
  const { initialState, setInitialState } = useModel('@@initialState');

  const currentTeam = initialState?.currentTeam;

  const onMenuClick: MenuProps['onClick'] = async (event) => {
    const { key } = event;

    if (key === 'logout') {
      flushSync(() => {
        setInitialState((s) => ({
          ...s,
          currentUser: undefined,
          currentTeam: undefined
        }));
      });
      loginOut();
      return;
    }

    // 处理其他菜单项
    switch (key) {
      case 'switch-team':
        history.push('/user/team-select');
        break;
      default:
        break;
    }
  };

  const loading = (
    <span className={styles.action}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    </span>
  );

  if (!initialState) {
    return loading;
  }

  const { currentUser } = initialState;

  if (!currentUser || !currentUser.name) {
    return loading;
  }

  // 构建菜单项
  const buildMenuItems = () => {
    const items = [];

    if (menu) {
      // 切换团队选项 - 直接导航到团队选择页面
      if (currentTeam) {
        items.push({
          key: 'switch-team',
          icon: <SwapOutlined />,
          label: '切换团队',
        });

        items.push({ type: 'divider' as const });
      }
    }

    // 退出登录
    items.push({
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    });

    return items;
  };

  const menuItems = buildMenuItems();

  return (
    <HeaderDropdown
      menu={{
        selectedKeys: [],
        onClick: onMenuClick,
        items: menuItems,
      }}
    >
      {children}
    </HeaderDropdown>
  );
};
