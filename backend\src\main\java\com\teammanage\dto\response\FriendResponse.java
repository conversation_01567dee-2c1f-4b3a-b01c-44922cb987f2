package com.teammanage.dto.response;

import java.time.LocalDateTime;

/**
 * 好友信息响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class FriendResponse {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户名
     */
    private String name;

    /**
     * 添加好友时间
     */
    private LocalDateTime addedAt;

    /**
     * 是否在线（预留字段）
     */
    private Boolean isOnline;

    // 手动添加getter/setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public LocalDateTime getAddedAt() { return addedAt; }
    public void setAddedAt(LocalDateTime addedAt) { this.addedAt = addedAt; }

    public Boolean getIsOnline() { return isOnline; }
    public void setIsOnline(Boolean isOnline) { this.isOnline = isOnline; }
}
