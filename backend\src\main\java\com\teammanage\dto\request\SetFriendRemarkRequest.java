package com.teammanage.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 设置好友备注请求
 * 
 * <AUTHOR>
 * @since 1.1.0
 */
@Schema(description = "设置好友备注请求")
public class SetFriendRemarkRequest {

    @NotNull(message = "好友ID不能为空")
    @Schema(description = "好友ID", example = "123")
    private Long friendId;

    @Size(max = 255, message = "备注长度不能超过255个字符")
    @Schema(description = "好友备注", example = "我的同事")
    private String remark;

    // Getter and Setter methods
    public Long getFriendId() {
        return friendId;
    }

    public void setFriendId(Long friendId) {
        this.friendId = friendId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
