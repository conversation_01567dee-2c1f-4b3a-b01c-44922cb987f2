# TeamAuth 团队协作管理系统

## 📋 项目概述

TeamAuth 是一个基于 React + Spring Boot 的现代化团队协作管理系统，提供用户认证、团队管理、好友系统和订阅管理等功能。系统采用前后端分离架构，支持多团队切换和基于 JWT 的安全认证。

## 🏗️ 技术架构

### 前端技术栈
- **框架**: React 19.1.0 + TypeScript
- **UI 库**: Ant Design Pro Components 2.7.19
- **构建工具**: UmiJS 4.3.24
- **状态管理**: UmiJS Model (基于 React Hooks)
- **样式方案**: Ant Design + CSS Modules
- **HTTP 客户端**: umi-request
- **开发工具**: Biome (代码格式化和检查)

### 后端技术栈
- **框架**: Spring Boot 3.5.3
- **安全框架**: Spring Security
- **数据库**: MariaDB
- **ORM**: MyBatis Plus 3.5.12
- **认证方案**: JWT (jjwt 0.12.6)
- **缓存**: Caffeine
- **API 文档**: SpringDoc OpenAPI 2.8.9
- **构建工具**: Maven

## 📁 项目结构

### 前端目录结构
```
frontend/
├── config/                 # 配置文件
│   ├── config.ts           # UmiJS 主配置
│   ├── routes.ts           # 路由配置
│   ├── proxy.ts            # 代理配置
│   └── defaultSettings.ts  # 默认设置
├── src/
│   ├── components/         # 公共组件
│   │   ├── RightContent/   # 头部右侧内容
│   │   ├── GlobalLoading/  # 全局加载组件
│   │   └── TeamSwitcher/   # 团队切换组件
│   ├── pages/              # 页面组件
│   │   ├── user/           # 用户相关页面
│   │   │   ├── login/      # 登录页面
│   │   │   └── team-select/ # 团队选择页面
│   │   ├── Dashboard/      # 仪表盘
│   │   ├── team/           # 团队管理
│   │   ├── personal-center/ # 个人中心
│   │   ├── friend/         # 好友管理
│   │   └── subscription/   # 订阅管理
│   ├── services/           # API 服务层
│   │   ├── auth.ts         # 认证服务
│   │   ├── user.ts         # 用户服务
│   │   ├── team.ts         # 团队服务
│   │   ├── friend.ts       # 好友服务
│   │   └── subscription.ts # 订阅服务
│   ├── types/              # TypeScript 类型定义
│   │   └── api.ts          # API 类型定义
│   ├── utils/              # 工具函数
│   │   ├── request.ts      # 请求工具和 Token 管理
│   │   └── tokenUtils.ts   # Token 工具函数
│   └── app.tsx             # 应用入口和全局配置
└── package.json            # 依赖配置
```

### 后端目录结构
```
backend/
├── src/main/java/com/teammanage/
│   ├── config/             # 配置类
│   │   ├── SecurityConfig.java      # Spring Security 配置
│   │   ├── CaffeineConfig.java      # 缓存配置
│   │   ├── SwaggerConfig.java       # API 文档配置
│   │   └── WebConfig.java           # Web 配置
│   ├── controller/         # 控制器层
│   │   ├── AuthController.java      # 认证控制器
│   │   ├── UserController.java      # 用户控制器
│   │   ├── TeamController.java      # 团队控制器
│   │   ├── FriendController.java    # 好友控制器
│   │   └── SubscriptionController.java # 订阅控制器
│   ├── service/            # 服务层
│   │   ├── AuthService.java         # 认证服务
│   │   ├── UserService.java         # 用户服务
│   │   ├── TeamService.java         # 团队服务
│   │   ├── FriendService.java       # 好友服务
│   │   ├── SubscriptionService.java # 订阅服务
│   │   └── UserSessionService.java  # 会话管理服务
│   ├── entity/             # 实体类
│   │   ├── Account.java             # 用户账户
│   │   ├── Team.java                # 团队
│   │   ├── TeamMember.java          # 团队成员
│   │   ├── AccountRelation.java     # 好友关系
│   │   └── SubscriptionPlan.java    # 订阅套餐
│   ├── mapper/             # 数据访问层
│   ├── security/           # 安全相关
│   │   ├── JwtAuthenticationFilter.java # JWT 认证过滤器
│   │   └── UserPrincipal.java           # 用户主体
│   ├── util/               # 工具类
│   │   ├── JwtTokenUtil.java        # JWT 工具类
│   │   ├── PasswordUtil.java        # 密码工具类
│   │   └── SecurityUtil.java        # 安全工具类
│   └── dto/                # 数据传输对象
│       ├── request/        # 请求 DTO
│       └── response/       # 响应 DTO
├── src/main/resources/
│   ├── application.yml     # 应用配置
│   └── sql/schema.sql      # 数据库初始化脚本
└── pom.xml                 # Maven 配置
```

## 🗄️ 数据库架构

### 核心数据表

#### 1. 用户账户表 (account)
```sql
CREATE TABLE account (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  email VARCHAR(255) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(100) NOT NULL,
  default_subscription_plan_id BIGINT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 2. 团队信息表 (team)
```sql
CREATE TABLE team (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  created_by BIGINT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  is_deleted BOOLEAN DEFAULT FALSE
);
```

#### 3. 团队成员表 (team_member)
```sql
CREATE TABLE team_member (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  team_id BIGINT NOT NULL,
  account_id BIGINT NOT NULL,
  is_creator BOOLEAN NOT NULL DEFAULT FALSE,
  assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_access_time TIMESTAMP NULL,
  is_active BOOLEAN DEFAULT TRUE,
  is_deleted BOOLEAN DEFAULT FALSE
);
```

#### 4. 好友关系表 (account_relation)
```sql
CREATE TABLE account_relation (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  account_id BIGINT NOT NULL,
  invited_by BIGINT NOT NULL,
  invited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  remark VARCHAR(255),
  status ENUM('pending', 'accepted', 'rejected') DEFAULT 'pending',
  is_active BOOLEAN DEFAULT TRUE,
  is_deleted BOOLEAN DEFAULT FALSE
);
```

#### 5. 订阅套餐表 (subscription_plan)
```sql
CREATE TABLE subscription_plan (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50) NOT NULL,
  description TEXT,
  max_size INT NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  is_active BOOLEAN DEFAULT TRUE
);
```

### 数据库关系图
```mermaid
erDiagram
    account ||--o{ team_member : "用户-团队成员"
    team ||--o{ team_member : "团队-成员"
    account ||--o{ team : "创建者"
    account ||--o{ account_relation : "好友关系"
    account ||--o{ account_subscription : "订阅记录"
    subscription_plan ||--o{ account_subscription : "套餐-订阅"
    
    account {
        bigint id PK
        string email UK
        string password_hash
        string name
        bigint default_subscription_plan_id FK
        timestamp created_at
        timestamp updated_at
    }
    
    team {
        bigint id PK
        string name
        text description
        bigint created_by FK
        boolean is_deleted
        timestamp created_at
        timestamp updated_at
    }
    
    team_member {
        bigint id PK
        bigint team_id FK
        bigint account_id FK
        boolean is_creator
        timestamp assigned_at
        timestamp last_access_time
        boolean is_active
        boolean is_deleted
    }
    
    account_relation {
        bigint id PK
        bigint account_id FK
        bigint invited_by FK
        timestamp invited_at
        string remark
        enum status
        boolean is_active
        boolean is_deleted
    }
    
    subscription_plan {
        bigint id PK
        string name
        text description
        int max_size
        decimal price
        boolean is_active
    }
```

## 🔐 认证与授权系统

### JWT Token 架构

系统采用单 Token 架构，支持两种模式：

1. **用户 Token**: 仅包含用户身份信息，用于访问个人资源
2. **团队 Token**: 包含用户身份和团队信息，用于访问团队资源

### Token 结构
```json
{
  "sub": "<EMAIL>",
  "userId": 123,
  "teamId": 456,           // 仅团队 Token 包含
  "isCreator": true,       // 仅团队 Token 包含
  "iat": **********,
  "exp": **********,
  "jti": "unique-token-id"
}
```

## 🔄 完整的前后端交互流程

### 1. 系统架构图
```mermaid
graph TB
    subgraph "前端 (React + UmiJS)"
        A[登录页面] --> B[团队选择页面]
        B --> C[主应用界面]
        C --> D[个人中心]
        C --> E[团队管理]
        C --> F[好友管理]
        C --> G[订阅管理]
    end

    subgraph "后端 (Spring Boot)"
        H[认证控制器] --> I[用户服务]
        H --> J[团队服务]
        K[JWT 过滤器] --> L[Spring Security]
        M[会话管理] --> N[Caffeine 缓存]
    end

    subgraph "数据层"
        O[MariaDB 数据库]
        P[MyBatis Plus]
    end

    A --> H
    B --> J
    C --> K
    I --> P
    J --> P
    P --> O
```

### 2. API 端点总览

#### 认证相关 API
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh-token` - 刷新 Token
- `POST /api/v1/auth/select-team` - 选择团队
- `POST /api/v1/auth/clear-team` - 清除团队上下文

#### 用户管理 API
- `GET /api/v1/users/profile` - 获取用户资料
- `PUT /api/v1/users/profile` - 更新用户资料
- `PUT /api/v1/users/password` - 修改密码

#### 团队管理 API
- `POST /api/v1/teams` - 创建团队
- `GET /api/v1/teams` - 获取用户团队列表
- `GET /api/v1/teams/{id}` - 获取团队详情
- `PUT /api/v1/teams/{id}` - 更新团队信息
- `DELETE /api/v1/teams/{id}` - 删除团队

#### 好友管理 API
- `GET /api/v1/friends` - 获取好友列表
- `POST /api/v1/friends/search` - 搜索用户
- `POST /api/v1/friends/add` - 添加好友
- `PUT /api/v1/friends/{id}/remark` - 更新好友备注
- `DELETE /api/v1/friends/{id}` - 删除好友

#### 订阅管理 API
- `GET /api/v1/subscriptions/plans` - 获取套餐列表
- `GET /api/v1/subscriptions/current` - 获取当前订阅
- `POST /api/v1/subscriptions/subscribe` - 订阅套餐

### 3. 请求-响应数据流

#### 标准 API 响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 错误响应格式
```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": null,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 🔐 详细的登录模块分析

### 1. 登录流程序列图
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端 (React)
    participant B as 后端 (Spring Boot)
    participant DB as 数据库
    participant C as 缓存 (Caffeine)

    U->>F: 输入邮箱和密码
    F->>F: 前端表单验证
    F->>B: POST /auth/login

    B->>DB: 查询用户信息
    DB-->>B: 返回用户数据

    B->>B: 验证密码哈希
    B->>B: 生成 JWT Token
    B->>B: 创建会话信息

    B->>C: 存储会话到缓存
    B->>DB: 查询用户团队列表
    DB-->>B: 返回团队数据

    B-->>F: 返回 Token 和团队列表
    F->>F: 存储 Token 到 localStorage

    alt 用户有团队
        F->>F: 跳转到团队选择页面
    else 用户无团队
        F->>F: 跳转到创建团队页面
    end
```

### 2. 登录代码流程分析

#### 前端登录组件
<augment_code_snippet path="frontend/src/pages/user/login/index.tsx" mode="EXCERPT">
````typescript
// 处理登录
const handleLogin = async (values: LoginRequest) => {
  setLoading(true);
  try {
    const response = await AuthService.login(values);
    message.success('登录成功！');

    // 根据团队数量进行不同的跳转处理
    if (response.teams.length === 0) {
      // 没有团队，跳转到创建团队页面
      history.push('/team/create');
    } else {
      // 有团队（无论一个还是多个），都跳转到团队选择页面
      history.push('/user/team-select', { teams: response.teams });
    }
  } catch (error) {
    console.error('登录失败:', error);
  } finally {
    setLoading(false);
  }
};
````
</augment_code_snippet>

#### 前端认证服务
<augment_code_snippet path="frontend/src/services/auth.ts" mode="EXCERPT">
````typescript
/**
 * 用户登录（单阶段）
 */
static async login(data: LoginRequest): Promise<LoginResponse> {
  const response = await apiRequest.post<LoginResponse>('/auth/login', data);

  // 保存用户Token
  if (response.data.token) {
    TokenManager.setToken(response.data.token);
  }

  return response.data;
}
````
</augment_code_snippet>

#### 后端认证控制器
<augment_code_snippet path="backend/src/main/java/com/teammanage/controller/AuthController.java" mode="EXCERPT">
````java
/**
 * 用户登录（单阶段）
 */
@PostMapping("/login")
@Operation(summary = "用户登录", description = "用户登录，验证凭据并返回用户Token和团队列表")
public ApiResponse<LoginResponse> login(@Valid @RequestBody LoginRequest request,
                                      HttpServletRequest httpRequest) {
    LoginResponse response = authService.login(request, httpRequest);
    return ApiResponse.success("登录成功", response);
}
````
</augment_code_snippet>

#### 后端认证服务核心逻辑
<augment_code_snippet path="backend/src/main/java/com/teammanage/service/AuthService.java" mode="EXCERPT">
````java
@Transactional
public LoginResponse login(LoginRequest request, HttpServletRequest httpRequest) {
    // 验证用户凭据
    Account account = accountMapper.findByEmail(request.getEmail());
    if (account == null) {
        throw new BusinessException("邮箱或密码错误");
    }

    if (!passwordUtil.matches(request.getPassword(), account.getPasswordHash())) {
        throw new BusinessException("邮箱或密码错误");
    }

    // 生成用户Token
    String userToken = jwtTokenUtil.generateToken(account);
    String jti = jwtTokenUtil.getJtiFromToken(userToken);

    // 创建会话记录
    SessionInfo sessionInfo = SessionInfo.builder()
            .accountId(account.getId())
            .tokenHash(jti)
            .deviceInfo(getDeviceInfo(httpRequest))
            .ipAddress(getClientIpAddress(httpRequest))
            .userAgent(httpRequest.getHeader("User-Agent"))
            .loginTime(LocalDateTime.now())
            .lastActivityTime(LocalDateTime.now())
            .isActive(true)
            .build();

    userSessionService.createSession(sessionInfo);

    // 查询用户的团队列表
    List<TeamMember> teamMembers = teamMemberMapper.findByAccountId(account.getId());
    List<LoginResponse.TeamInfo> teams = teamMembers.stream()
            .map(this::convertToTeamInfo)
            .collect(Collectors.toList());
````
</augment_code_snippet>

### 3. JWT Token 处理机制

#### Token 生成
<augment_code_snippet path="backend/src/main/java/com/teammanage/util/JwtTokenUtil.java" mode="EXCERPT">
````java
/**
 * 生成用户Token（不包含团队信息）
 */
public String generateToken(Account account) {
    Date now = new Date();
    Date expiryDate = new Date(now.getTime() + tokenExpiration * 1000);
    String jti = UUID.randomUUID().toString();

    return Jwts.builder()
            .subject(account.getEmail())
            .claim("userId", account.getId())
            .claim("jti", jti)
            .issuedAt(now)
            .expiration(expiryDate)
            .signWith(getSigningKey())
            .compact();
}
````
</augment_code_snippet>

#### Token 验证过滤器
<augment_code_snippet path="backend/src/main/java/com/teammanage/security/JwtAuthenticationFilter.java" mode="EXCERPT">
````java
@Override
protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
                              FilterChain filterChain) throws ServletException, IOException {

    String token = getTokenFromRequest(request);

    if (StringUtils.hasText(token) && jwtTokenUtil.validateToken(token)) {
        try {
            Claims claims = jwtTokenUtil.getClaimsFromToken(token);
            String jti = claims.get("jti", String.class);

            // 检查会话是否有效
            if (!userSessionService.isSessionValid(jti)) {
                log.warn("会话无效或已过期: {}", jti);
                filterChain.doFilter(request, response);
                return;
            }

            String email = jwtTokenUtil.getEmailFromToken(token);
            Long userId = jwtTokenUtil.getUserIdFromToken(token);
            Long teamId = jwtTokenUtil.getTeamIdFromToken(token);

            // 根据Token中是否包含团队信息确定权限
            List<SimpleGrantedAuthority> authorities = new ArrayList<>();
            authorities.add(new SimpleGrantedAuthority("USER"));
            if (teamId != null) {
                authorities.add(new SimpleGrantedAuthority("TEAM"));
            }
````
</augment_code_snippet>

### 4. 前端 Token 管理
<augment_code_snippet path="frontend/src/utils/request.ts" mode="EXCERPT">
````typescript
/**
 * Token 管理器（单令牌系统）
 */
class TokenManager {
  private static readonly TOKEN_KEY = 'auth_token';

  /**
   * 获取当前Token
   */
  static getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * 设置Token
   */
  static setToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  /**
   * 清除Token
   */
  static clearToken(): void {
    localStorage.removeItem(this.TOKEN_KEY);
  }
}
````
</augment_code_snippet>

### 5. 错误处理和验证逻辑

#### 前端请求拦截器
<augment_code_snippet path="frontend/src/utils/request.ts" mode="EXCERPT">
````typescript
/**
 * 响应拦截器
 */
request.interceptors.response.use(
  async (response) => {
    const data = await response.clone().json();

    // 检查业务状态码
    if (data.code !== 200) {
      // 认证失败，清除 token 并跳转到登录页
      if (data.code === 401) {
        TokenManager.clearToken();
        message.error('登录已过期，请重新登录');
        // 跳转到登录页，避免重复跳转
        if (window.location.pathname !== '/user/login') {
          window.location.href = '/user/login';
        }
        return Promise.reject(new Error(data.message));
      }

      // 其他业务错误，显示错误消息
      message.error(data.message || '请求失败');
      return Promise.reject(new Error(data.message));
    }

    return response;
  },
````
</augment_code_snippet>

#### 后端全局异常处理
<augment_code_snippet path="backend/src/main/java/com/teammanage/exception/GlobalExceptionHandler.java" mode="EXCERPT">
````java
@ExceptionHandler(BusinessException.class)
public ApiResponse<Void> handleBusinessException(BusinessException e) {
    log.warn("业务异常: {}", e.getMessage());
    return ApiResponse.error(400, e.getMessage());
}

@ExceptionHandler(ResourceNotFoundException.class)
public ApiResponse<Void> handleResourceNotFoundException(ResourceNotFoundException e) {
    log.warn("资源未找到: {}", e.getMessage());
    return ApiResponse.error(404, e.getMessage());
}
````
</augment_code_snippet>

## 🛡️ 安全措施和最佳实践

### 1. 密码安全
- 使用 BCrypt 进行密码哈希
- 密码强度验证
- 防止密码明文传输

### 2. JWT 安全
- Token 过期时间设置（7天）
- JTI (JWT ID) 防重放攻击
- 会话管理和失效机制

### 3. 数据库安全
- 参数化查询防 SQL 注入
- 逻辑删除保护数据
- 索引优化提升性能

### 4. API 安全
- CORS 配置
- 请求频率限制
- 输入验证和清理

## ⚙️ 配置和环境设置

### 前端环境配置
```bash
# 安装依赖
yarn install

# 开发环境启动
yarn start:dev

# 生产构建
yarn build

# 代码检查
yarn lint
```

### 后端环境配置
```yaml
# application.yml
server:
  port: 8080
  servlet:
    context-path: /api/v1

spring:
  datasource:
    driver-class-name: org.mariadb.jdbc.Driver
    url: *****************************************
    username: root
    password: your_password

jwt:
  secret: your_jwt_secret_key
  token-expiration: 604800  # 7天
```

### 数据库初始化
```bash
# 创建数据库
mysql -u root -p < backend/src/main/resources/sql/schema.sql
```

## 🚀 部署说明

### 前端部署
1. 构建生产版本：`yarn build`
2. 将 `dist` 目录部署到 Web 服务器
3. 配置反向代理到后端 API

### 后端部署
1. 打包应用：`mvn clean package`
2. 运行 JAR 文件：`java -jar target/team-manage-1.0.0.jar`
3. 配置数据库连接和环境变量

## 📊 系统特性

### 1. 响应式设计
- 支持桌面端和移动端
- 自适应布局和组件

### 2. 国际化支持
- 多语言切换（预留）
- 时区处理

### 3. 性能优化
- 前端代码分割和懒加载
- 后端缓存机制
- 数据库查询优化

### 4. 监控和日志
- 应用性能监控
- 详细的操作日志
- 错误追踪和报告

## 🔧 开发指南

### 前端开发
1. 遵循 TypeScript 严格模式
2. 使用 Ant Design 组件库
3. 统一的 API 调用方式
4. 组件化和模块化开发

### 后端开发
1. 遵循 RESTful API 设计
2. 使用 Spring Boot 最佳实践
3. 统一的异常处理
4. 完善的单元测试

## 📝 API 文档

系统集成了 Swagger UI，可通过以下地址访问完整的 API 文档：
- 开发环境：http://localhost:8080/api/v1/swagger-ui.html
- 生产环境：根据部署域名调整

## 🤝 贡献指南

1. Fork 项目仓库
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request
5. 代码审查和合并

## 📄 许可证

本项目采用 MIT 许可证，详情请参阅 LICENSE 文件。
