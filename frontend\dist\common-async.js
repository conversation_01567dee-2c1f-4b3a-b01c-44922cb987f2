((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['common'],
{ "src/pages/subscription/components/UnifiedSubscriptionContent.tsx": function (module, exports, __mako_require__){
/**
 * 统一订阅管理内容组件
 * 整合订阅详情和套餐选择功能
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var _api = __mako_require__("src/types/api.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const UnifiedSubscriptionContent = ({ currentSubscription, loading, onRefresh })=>{
    _s();
    // 订阅详情相关状态
    const [subscriptionHistory, setSubscriptionHistory] = (0, _react.useState)([]);
    const [usageInfo, setUsageInfo] = (0, _react.useState)(null);
    const [historyModalVisible, setHistoryModalVisible] = (0, _react.useState)(false);
    // 套餐选择相关状态
    const [plans, setPlans] = (0, _react.useState)([]);
    const [plansLoading, setPlansLoading] = (0, _react.useState)(true);
    const [subscribing, setSubscribing] = (0, _react.useState)(false);
    const [selectedPlan, setSelectedPlan] = (0, _react.useState)(null);
    const [subscribeModalVisible, setSubscribeModalVisible] = (0, _react.useState)(false);
    const [duration, setDuration] = (0, _react.useState)(1);
    (0, _react.useEffect)(()=>{
        fetchPlans();
        if (currentSubscription) {
            fetchSubscriptionHistory();
            fetchUsageInfo();
        }
    }, [
        currentSubscription
    ]);
    // 获取套餐列表
    const fetchPlans = async ()=>{
        try {
            setPlansLoading(true);
            const plansData = await _services.SubscriptionService.getActivePlans();
            setPlans(plansData);
        } catch (error) {
            console.error('获取套餐列表失败:', error);
            _antd.message.error('获取套餐列表失败');
        } finally{
            setPlansLoading(false);
        }
    };
    // 获取订阅历史
    const fetchSubscriptionHistory = async ()=>{
        try {
            const history = await _services.SubscriptionService.getSubscriptionHistory();
            setSubscriptionHistory(history);
        } catch (error) {
            console.error('获取订阅历史失败:', error);
        }
    };
    // 获取使用情况
    const fetchUsageInfo = async ()=>{
        try {
            const usage = await _services.SubscriptionService.getUsageInfo();
            setUsageInfo(usage);
        } catch (error) {
            console.error('获取使用情况失败:', error);
        }
    };
    // 处理订阅
    const handleSubscribe = async ()=>{
        if (!selectedPlan) return;
        try {
            setSubscribing(true);
            const request = {
                planId: selectedPlan.id,
                duration: duration
            };
            await _services.SubscriptionService.createSubscription(request);
            _antd.message.success('订阅成功！');
            setSubscribeModalVisible(false);
            onRefresh();
        } catch (error) {
            console.error('订阅失败:', error);
            _antd.message.error('订阅失败，请稍后重试');
        } finally{
            setSubscribing(false);
        }
    };
    // 取消订阅
    const handleCancelSubscription = async ()=>{
        if (!currentSubscription) return;
        _antd.Modal.confirm({
            title: '确认取消订阅',
            content: '取消订阅后，您将失去当前套餐的所有权益。确定要取消吗？',
            okText: '确认取消',
            cancelText: '保留订阅',
            okType: 'danger',
            onOk: async ()=>{
                try {
                    await _services.SubscriptionService.cancelSubscription(currentSubscription.id);
                    _antd.message.success('订阅已取消');
                    onRefresh();
                } catch (error) {
                    console.error('取消订阅失败:', error);
                    _antd.message.error('取消订阅失败');
                }
            }
        });
    };
    // 获取状态标签
    const getStatusTag = (status)=>{
        const statusConfig = {
            [_api.SubscriptionStatus.ACTIVE]: {
                color: 'green',
                text: '有效'
            },
            [_api.SubscriptionStatus.EXPIRED]: {
                color: 'red',
                text: '已过期'
            },
            [_api.SubscriptionStatus.CANCELED]: {
                color: 'default',
                text: '已取消'
            },
            [_api.SubscriptionStatus.PENDING]: {
                color: 'orange',
                text: '待激活'
            }
        };
        const config = statusConfig[status] || {
            color: 'default',
            text: '未知'
        };
        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
            color: config.color,
            children: config.text
        }, void 0, false, {
            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
            lineNumber: 169,
            columnNumber: 12
        }, this);
    };
    // 获取套餐推荐标签
    const getPlanRecommendation = (plan)=>{
        if (plan.name === '标准版') return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
            color: "orange",
            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.StarOutlined, {}, void 0, false, {
                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                lineNumber: 175,
                columnNumber: 40
            }, void 0),
            children: "推荐"
        }, void 0, false, {
            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
            lineNumber: 175,
            columnNumber: 14
        }, this);
        if (plan.name === '企业版') return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
            color: "gold",
            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                lineNumber: 178,
                columnNumber: 38
            }, void 0),
            children: "热门"
        }, void 0, false, {
            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
            lineNumber: 178,
            columnNumber: 14
        }, this);
        return null;
    };
    // 套餐特性列表
    const getPlanFeatures = (plan)=>{
        const features = [
            `可创建 ${plan.maxSize === 999999 ? '无限' : plan.maxSize} 个团队`,
            '团队成员无限制',
            '数据安全保障',
            '7x24小时技术支持'
        ];
        if (plan.name !== '免费版') features.push('优先客服支持');
        if (plan.name === '企业版') {
            features.push('定制化服务');
            features.push('专属客户经理');
        }
        return features;
    };
    // 历史记录表格列定义
    const historyColumns = [
        {
            title: '套餐名称',
            dataIndex: 'planName',
            key: 'planName'
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            render: (status)=>getStatusTag(status)
        },
        {
            title: '开始时间',
            dataIndex: 'startDate',
            key: 'startDate',
            render: (date)=>new Date(date).toLocaleDateString()
        },
        {
            title: '结束时间',
            dataIndex: 'endDate',
            key: 'endDate',
            render: (date)=>date ? new Date(date).toLocaleDateString() : '永久'
        },
        {
            title: '价格',
            dataIndex: 'price',
            key: 'price',
            render: (price)=>`¥${price.toFixed(2)}`
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 242,
                            columnNumber: 13
                        }, void 0),
                        "当前订阅状态"
                    ]
                }, void 0, true, {
                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                    lineNumber: 241,
                    columnNumber: 11
                }, void 0),
                extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ReloadOutlined, {}, void 0, false, {
                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                        lineNumber: 248,
                        columnNumber: 19
                    }, void 0),
                    onClick: onRefresh,
                    loading: loading,
                    children: "刷新"
                }, void 0, false, {
                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                    lineNumber: 247,
                    columnNumber: 11
                }, void 0),
                style: {
                    marginBottom: 24
                },
                children: currentSubscription ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions, {
                            column: 2,
                            bordered: true,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                    label: "套餐名称",
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        children: [
                                            currentSubscription.planName,
                                            getStatusTag(currentSubscription.status)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                        lineNumber: 261,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 260,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                    label: "团队限制",
                                    children: currentSubscription.maxSize === 999999 ? '无限制' : `${currentSubscription.maxSize} 个`
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 266,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                    label: "开始时间",
                                    children: new Date(currentSubscription.startDate).toLocaleDateString()
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 269,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                    label: "结束时间",
                                    children: currentSubscription.endDate ? new Date(currentSubscription.endDate).toLocaleDateString() : '永久有效'
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 272,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                    label: "月费",
                                    children: [
                                        "¥",
                                        currentSubscription.price.toFixed(2)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 278,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                    label: "剩余天数",
                                    children: (usageInfo === null || usageInfo === void 0 ? void 0 : usageInfo.remainingDays) !== undefined ? `${usageInfo.remainingDays} 天` : '计算中...'
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 281,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 259,
                            columnNumber: 13
                        }, this),
                        usageInfo && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginTop: 16
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    strong: true,
                                    children: "团队使用情况："
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 291,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                                    percent: usageInfo.usagePercentage,
                                    format: ()=>`${usageInfo.currentUsage}/${usageInfo.maxUsage === 999999 ? '∞' : usageInfo.maxUsage}`,
                                    style: {
                                        marginTop: 8
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 292,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 290,
                            columnNumber: 15
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginTop: 16
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UpOutlined, {}, void 0, false, {
                                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                            lineNumber: 304,
                                            columnNumber: 25
                                        }, void 0),
                                        onClick: ()=>setSubscribeModalVisible(true),
                                        children: "升级套餐"
                                    }, void 0, false, {
                                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                        lineNumber: 302,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.HistoryOutlined, {}, void 0, false, {
                                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                            lineNumber: 310,
                                            columnNumber: 25
                                        }, void 0),
                                        onClick: ()=>setHistoryModalVisible(true),
                                        children: "查看历史"
                                    }, void 0, false, {
                                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                        lineNumber: 309,
                                        columnNumber: 17
                                    }, this),
                                    currentSubscription.status === _api.SubscriptionStatus.ACTIVE && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        danger: true,
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.StopOutlined, {}, void 0, false, {
                                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                            lineNumber: 318,
                                            columnNumber: 27
                                        }, void 0),
                                        onClick: handleCancelSubscription,
                                        children: "取消订阅"
                                    }, void 0, false, {
                                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                        lineNumber: 316,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                lineNumber: 301,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 300,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                    lineNumber: 258,
                    columnNumber: 11
                }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                    description: "暂无有效订阅",
                    image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "primary",
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ShoppingCartOutlined, {}, void 0, false, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 334,
                            columnNumber: 21
                        }, void 0),
                        onClick: ()=>setSubscribeModalVisible(true),
                        children: "立即订阅"
                    }, void 0, false, {
                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                        lineNumber: 332,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                    lineNumber: 328,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                lineNumber: 239,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ShoppingCartOutlined, {}, void 0, false, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 347,
                            columnNumber: 13
                        }, void 0),
                        "选择套餐"
                    ]
                }, void 0, true, {
                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                    lineNumber: 346,
                    columnNumber: 11
                }, void 0),
                loading: plansLoading,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                    gutter: [
                        16,
                        16
                    ],
                    children: plans.map((plan)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            sm: 12,
                            lg: 6,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                hoverable: true,
                                className: `plan-card ${(currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id ? 'current-plan' : ''}`,
                                actions: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: (currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id ? 'default' : 'primary',
                                        disabled: (currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id,
                                        onClick: ()=>{
                                            setSelectedPlan(plan);
                                            setSubscribeModalVisible(true);
                                        },
                                        children: (currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id ? '当前套餐' : '选择此套餐'
                                    }, "subscribe", false, {
                                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                        lineNumber: 360,
                                        columnNumber: 19
                                    }, void 0)
                                ],
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        style: {
                                            textAlign: 'center'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                level: 4,
                                                children: [
                                                    plan.name,
                                                    getPlanRecommendation(plan)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                                lineNumber: 374,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 32,
                                                    fontWeight: 'bold',
                                                    color: '#1890ff'
                                                },
                                                children: [
                                                    "¥",
                                                    plan.price.toFixed(0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                        style: {
                                                            fontSize: 14,
                                                            color: '#666'
                                                        },
                                                        children: "/月"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                                        lineNumber: 380,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                                lineNumber: 378,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                children: plan.description
                                            }, void 0, false, {
                                                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                                lineNumber: 382,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                        lineNumber: 373,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                        lineNumber: 385,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                                        size: "small",
                                        dataSource: getPlanFeatures(plan),
                                        renderItem: (feature)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                            style: {
                                                                color: '#52c41a'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                                            lineNumber: 393,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        feature
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                                    lineNumber: 392,
                                                    columnNumber: 23
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                                lineNumber: 391,
                                                columnNumber: 21
                                            }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                        lineNumber: 387,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                lineNumber: 356,
                                columnNumber: 15
                            }, this)
                        }, plan.id, false, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 355,
                            columnNumber: 13
                        }, this))
                }, void 0, false, {
                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                    lineNumber: 353,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                lineNumber: 344,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "确认订阅",
                open: subscribeModalVisible,
                onOk: handleSubscribe,
                onCancel: ()=>setSubscribeModalVisible(false),
                confirmLoading: subscribing,
                okText: "确认订阅",
                cancelText: "取消",
                children: selectedPlan && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                            message: `您选择了 ${selectedPlan.name}`,
                            description: selectedPlan.description,
                            type: "info",
                            style: {
                                marginBottom: 16
                            }
                        }, void 0, false, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 417,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 16
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    strong: true,
                                    children: "订阅时长："
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 425,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.InputNumber, {
                                    min: 1,
                                    max: 12,
                                    value: duration,
                                    onChange: (value)=>setDuration(value || 1),
                                    addonAfter: "个月",
                                    style: {
                                        marginLeft: 8
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 426,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 424,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    strong: true,
                                    children: "总费用："
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 437,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    style: {
                                        fontSize: 18,
                                        color: '#1890ff',
                                        marginLeft: 8
                                    },
                                    children: [
                                        "¥",
                                        (selectedPlan.price * duration).toFixed(2)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 438,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 436,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                    lineNumber: 416,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                lineNumber: 406,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "订阅历史",
                open: historyModalVisible,
                onCancel: ()=>setHistoryModalVisible(false),
                footer: null,
                width: 800,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                    columns: historyColumns,
                    dataSource: subscriptionHistory,
                    rowKey: "id",
                    pagination: {
                        pageSize: 10
                    }
                }, void 0, false, {
                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                    lineNumber: 454,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                lineNumber: 447,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
        lineNumber: 237,
        columnNumber: 5
    }, this);
};
_s(UnifiedSubscriptionContent, "AqX/RzCYRxfO7hNh5ekvwTxLd0E=");
_c = UnifiedSubscriptionContent;
var _default = UnifiedSubscriptionContent;
var _c;
$RefreshReg$(_c, "UnifiedSubscriptionContent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team/detail/components/TeamDetailContent.tsx": function (module, exports, __mako_require__){
/**
 * 团队详情组件 - 增强模式显示
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var _max = __mako_require__("src/.umi/exports.ts");
var _TeamMemberList = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/detail/components/TeamMemberList.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text, Paragraph } = _antd.Typography;
const { TextArea } = _antd.Input;
const TeamDetailContent = ({ teamDetail, loading, onRefresh, showBackButton = false, onBack })=>{
    _s();
    const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
    const [updating, setUpdating] = (0, _react.useState)(false);
    const [deleting, setDeleting] = (0, _react.useState)(false);
    const [form] = _antd.Form.useForm();
    const { setInitialState } = (0, _max.useModel)('@@initialState');
    // 辅助函数
    const formatDate = (dateString)=>{
        return new Date(dateString).toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };
    const getTeamStatusColor = ()=>{
        if (!teamDetail) return '#1890ff';
        const memberCount = teamDetail.memberCount;
        if (memberCount >= 10) return '#52c41a'; // 绿色 - 活跃
        if (memberCount >= 5) return '#faad14'; // 橙色 - 正常
        return '#1890ff'; // 蓝色 - 小团队
    };
    const getTeamStatusText = ()=>{
        if (!teamDetail) return '小型团队';
        const memberCount = teamDetail.memberCount;
        if (memberCount >= 10) return '活跃团队';
        if (memberCount >= 5) return '正常团队';
        return '小型团队';
    };
    const handleGoBack = ()=>{
        if (onBack) onBack();
        else _max.history.push('/user/team-select');
    };
    /**
   * 处理编辑团队信息操作
   */ const handleEdit = ()=>{
        if (!teamDetail) return;
        form.setFieldsValue({
            name: teamDetail.name,
            description: teamDetail.description || ''
        });
        setEditModalVisible(true);
    };
    /**
   * 处理团队信息更新操作
   */ const handleUpdateTeam = async (values)=>{
        if (!teamDetail) return;
        try {
            setUpdating(true);
            await _services.TeamService.updateCurrentTeam(values);
            _antd.message.success('团队信息更新成功');
            setEditModalVisible(false);
            onRefresh();
        } catch (error) {
            console.error('更新团队失败:', error);
            _antd.message.error('更新团队失败');
        } finally{
            setUpdating(false);
        }
    };
    /**
   * 处理删除团队操作
   */ const handleDeleteTeam = ()=>{
        if (!teamDetail) return;
        _antd.Modal.confirm({
            title: '确认删除团队',
            content: `确定要删除团队 "${teamDetail.name}" 吗？此操作不可恢复。`,
            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {}, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 143,
                columnNumber: 13
            }, this),
            okText: '确认删除',
            cancelText: '取消',
            okType: 'danger',
            onOk: async ()=>{
                try {
                    setDeleting(true);
                    await _services.TeamService.deleteCurrentTeam();
                    _antd.message.success('团队删除成功');
                    // 更新全局状态，清除当前团队
                    setInitialState((s)=>({
                            ...s,
                            currentTeam: undefined
                        }));
                    _max.history.push('/user/team-select');
                } catch (error) {
                    console.error('删除团队失败:', error);
                    _antd.message.error('删除团队失败');
                } finally{
                    setDeleting(false);
                }
            }
        });
    };
    // 创建下拉菜单项（增强模式使用）
    const createMenuItems = ()=>[
            {
                key: 'edit',
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                    lineNumber: 169,
                    columnNumber: 13
                }, this),
                label: '编辑团队',
                onClick: handleEdit
            },
            {
                key: 'delete',
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                    lineNumber: 175,
                    columnNumber: 13
                }, this),
                label: '删除团队',
                danger: true,
                onClick: handleDeleteTeam
            }
        ];
    if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        style: {
            textAlign: 'center',
            padding: '50px 0'
        },
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
            size: "large"
        }, void 0, false, {
            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
            lineNumber: 185,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
        lineNumber: 184,
        columnNumber: 7
    }, this);
    if (!teamDetail) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
        image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
        description: "请先选择一个团队"
    }, void 0, false, {
        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
        lineNumber: 192,
        columnNumber: 7
    }, this);
    // 增强模式渲染
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        style: {
            padding: '0 24px'
        },
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                style: {
                    marginBottom: 24,
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    border: 'none',
                    borderRadius: 16
                },
                styles: {
                    body: {
                        padding: '32px'
                    }
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                    align: "middle",
                    justify: "space-between",
                    children: [
                        showBackButton && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "text",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ArrowLeftOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                    lineNumber: 218,
                                    columnNumber: 23
                                }, void 0),
                                onClick: handleGoBack,
                                style: {
                                    color: 'rgba(255, 255, 255, 0.8)',
                                    fontSize: 16,
                                    padding: '4px 8px'
                                },
                                children: "返回"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 216,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 215,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            flex: "auto",
                            style: {
                                display: 'flex',
                                justifyContent: 'center',
                                maxWidth: '60%'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                size: "large",
                                align: "center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                        size: 65,
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 243,
                                            columnNumber: 25
                                        }, void 0),
                                        style: {
                                            backgroundColor: 'rgba(255, 255, 255, 0.2)',
                                            color: 'white',
                                            fontSize: 28
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 241,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                align: "center",
                                                style: {
                                                    marginBottom: 8
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                        level: 2,
                                                        style: {
                                                            color: 'white',
                                                            margin: 0
                                                        },
                                                        children: teamDetail.name
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                        lineNumber: 252,
                                                        columnNumber: 21
                                                    }, this),
                                                    teamDetail.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                            lineNumber: 257,
                                                            columnNumber: 31
                                                        }, void 0),
                                                        color: "gold",
                                                        style: {
                                                            fontSize: 12
                                                        },
                                                        children: "管理员"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                        lineNumber: 256,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Badge, {
                                                        color: getTeamStatusColor(),
                                                        text: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            style: {
                                                                color: 'rgba(255, 255, 255, 0.8)'
                                                            },
                                                            children: getTeamStatusText()
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                            lineNumber: 267,
                                                            columnNumber: 25
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                        lineNumber: 264,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                lineNumber: 251,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                                style: {
                                                    color: 'rgba(255, 255, 255, 0.8)',
                                                    margin: 0,
                                                    textAlign: 'center'
                                                },
                                                ellipsis: {
                                                    rows: 2
                                                },
                                                children: teamDetail.description || '这个团队还没有描述'
                                            }, void 0, false, {
                                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                lineNumber: 273,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 250,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 240,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 232,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            children: teamDetail.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                                menu: {
                                    items: createMenuItems()
                                },
                                trigger: [
                                    'click'
                                ],
                                placement: "bottomRight",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "text",
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 297,
                                        columnNumber: 27
                                    }, void 0),
                                    style: {
                                        color: 'rgba(255, 255, 255, 0.8)',
                                        fontSize: 20,
                                        width: 50,
                                        height: 50
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                    lineNumber: 295,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 290,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 288,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                    lineNumber: 212,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 203,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                gutter: [
                    16,
                    16
                ],
                style: {
                    marginBottom: 24
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        sm: 12,
                        md: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "团队成员",
                                value: teamDetail.memberCount,
                                suffix: "人",
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                    style: {
                                        color: '#1890ff'
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                    lineNumber: 319,
                                    columnNumber: 25
                                }, void 0),
                                valueStyle: {
                                    color: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 315,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 314,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                        lineNumber: 313,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        sm: 12,
                        md: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "创建时间",
                                value: formatDate(teamDetail.createdAt),
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                    style: {
                                        color: '#52c41a'
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                    lineNumber: 329,
                                    columnNumber: 25
                                }, void 0),
                                valueStyle: {
                                    color: '#52c41a',
                                    fontSize: 16
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 326,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 325,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                        lineNumber: 324,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        sm: 12,
                        md: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                title: "最后活动",
                                value: formatDate(teamDetail.updatedAt),
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                    style: {
                                        color: '#faad14'
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                    lineNumber: 339,
                                    columnNumber: 25
                                }, void 0),
                                valueStyle: {
                                    color: '#faad14',
                                    fontSize: 16
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 336,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 335,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                        lineNumber: 334,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        sm: 12,
                        md: 6,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    textAlign: 'center'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        style: {
                                            fontSize: 14
                                        },
                                        children: "团队活跃度"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 347,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        style: {
                                            marginTop: 8
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                                            type: "circle",
                                            size: 60,
                                            percent: Math.min(teamDetail.memberCount * 10, 100),
                                            strokeColor: getTeamStatusColor(),
                                            format: ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontSize: 12,
                                                        color: getTeamStatusColor()
                                                    },
                                                    children: teamDetail.memberCount >= 10 ? '高' : teamDetail.memberCount >= 5 ? '中' : '低'
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                    lineNumber: 355,
                                                    columnNumber: 23
                                                }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 349,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 348,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 346,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 345,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                        lineNumber: 344,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 312,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamMemberList.default, {
                teamId: teamDetail.id,
                isCreator: teamDetail.isCreator,
                onMemberChange: onRefresh
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 368,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "编辑团队信息",
                open: editModalVisible,
                onCancel: ()=>setEditModalVisible(false),
                footer: null,
                width: 600,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: form,
                    layout: "vertical",
                    onFinish: handleUpdateTeam,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队名称",
                            name: "name",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入团队名称'
                                },
                                {
                                    max: 50,
                                    message: '团队名称不能超过50个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                placeholder: "请输入团队名称"
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 395,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 387,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队描述",
                            name: "description",
                            rules: [
                                {
                                    max: 200,
                                    message: '团队描述不能超过200个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                rows: 4,
                                placeholder: "请输入团队描述（可选）",
                                showCount: true,
                                maxLength: 200
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 404,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 397,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            style: {
                                marginBottom: 0,
                                textAlign: 'right'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>setEditModalVisible(false),
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 413,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        loading: updating,
                                        children: "保存"
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 416,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 412,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 411,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                    lineNumber: 382,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                lineNumber: 375,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
        lineNumber: 201,
        columnNumber: 5
    }, this);
};
_s(TeamDetailContent, "UalB0vhOvdT9JI/tG4E1rE8Z8wk=", false, function() {
    return [
        _antd.Form.useForm,
        _max.useModel
    ];
});
_c = TeamDetailContent;
var _default = TeamDetailContent;
var _c;
$RefreshReg$(_c, "TeamDetailContent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team/detail/components/TeamMemberList.tsx": function (module, exports, __mako_require__){
/**
 * 团队成员列表组件
 *
 * 功能特性：
 * - 展示团队所有成员信息（头像、姓名、邮箱、角色、状态等）
 * - 支持成员搜索和筛选功能
 * - 提供成员管理操作（移除成员、角色变更等）
 * - 区分创建者和普通成员的权限显示
 * - 响应式表格设计，适配不同屏幕尺寸
 *
 * 权限控制：
 * - 只有团队创建者可以看到管理操作按钮
 * - 创建者不能移除自己
 * - 普通成员只能查看成员列表
 *
 * 交互设计：
 * - 支持批量操作（预留功能）
 * - 提供详细的操作确认对话框
 * - 实时更新成员状态和数量
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text } = _antd.Typography;
const { Option } = _antd.Select;
const TeamMemberList = ({ teamId, isCreator, onMemberChange })=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(true);
    const [members, setMembers] = (0, _react.useState)([]);
    const [searchText, setSearchText] = (0, _react.useState)('');
    const [filteredMembers, setFilteredMembers] = (0, _react.useState)([]);
    const [selectedRowKeys, setSelectedRowKeys] = (0, _react.useState)([]);
    const [statusFilter, setStatusFilter] = (0, _react.useState)('all');
    (0, _react.useEffect)(()=>{
        fetchMembers();
    }, [
        teamId
    ]);
    /**
   * 成员列表过滤效果
   *
   * 过滤条件：
   * 1. 搜索文本：匹配成员姓名或邮箱（不区分大小写）
   * 2. 状态筛选：全部/活跃/非活跃/创建者/普通成员
   *
   * 安全性：
   * - 添加空值检查，防止数据异常导致的错误
   * - 确保成员对象的必要属性存在
   */ (0, _react.useEffect)(()=>{
        // 过滤成员列表 - 添加空值检查
        if (!members || !Array.isArray(members)) {
            setFilteredMembers([]);
            return;
        }
        const filtered = members.filter((member)=>{
            // 确保member对象存在且有必要的属性
            if (!member || !member.name || !member.email) return false;
            // 搜索文本匹配（姓名或邮箱）
            const matchesSearch = !searchText || member.name.toLowerCase().includes(searchText.toLowerCase()) || member.email.toLowerCase().includes(searchText.toLowerCase());
            // 状态筛选匹配
            const matchesStatus = statusFilter === 'all' || statusFilter === 'active' && member.isActive || statusFilter === 'inactive' && !member.isActive || statusFilter === 'creator' && member.isCreator || statusFilter === 'member' && !member.isCreator;
            return matchesSearch && matchesStatus;
        });
        setFilteredMembers(filtered);
    }, [
        members,
        searchText,
        statusFilter
    ]);
    /**
   * 获取团队成员列表
   *
   * 功能：
   * - 调用API获取当前团队的所有成员
   * - 设置加载状态，提供用户反馈
   * - 处理错误情况，确保组件稳定性
   *
   * 数据处理：
   * - 确保返回数据为数组格式，防止渲染错误
   * - 错误时设置空数组，保持组件正常显示
   */ const fetchMembers = async ()=>{
        try {
            setLoading(true);
            const response = await _services.TeamService.getTeamMembers({
                current: 1,
                pageSize: 1000
            });
            // 确保返回的数据是数组格式，防止渲染错误
            setMembers((response === null || response === void 0 ? void 0 : response.list) || []);
        } catch (error) {
            console.error('获取团队成员失败:', error);
            _antd.message.error('获取团队成员失败');
            // 出错时设置为空数组，保持组件正常显示
            setMembers([]);
        } finally{
            setLoading(false);
        }
    };
    const handleRemoveMember = (member)=>{
        if (member.isCreator) {
            _antd.message.warning('不能移除团队创建者');
            return;
        }
        _antd.Modal.confirm({
            title: '确认移除成员',
            content: `确定要移除成员 "${member.name}" 吗？`,
            okText: '确认',
            cancelText: '取消',
            onOk: async ()=>{
                try {
                    await _services.TeamService.removeMember(member.id);
                    _antd.message.success('成员移除成功');
                    fetchMembers();
                    onMemberChange === null || onMemberChange === void 0 || onMemberChange();
                } catch (error) {
                    console.error('移除成员失败:', error);
                }
            }
        });
    };
    const handleBatchRemove = ()=>{
        const selectedMembers = members.filter((member)=>selectedRowKeys.includes(member.id) && !member.isCreator);
        if (selectedMembers.length === 0) {
            _antd.message.warning('请选择要移除的成员');
            return;
        }
        _antd.Modal.confirm({
            title: '批量移除成员',
            content: `确定要移除选中的 ${selectedMembers.length} 名成员吗？`,
            okText: '确认',
            cancelText: '取消',
            onOk: async ()=>{
                try {
                    await Promise.all(selectedMembers.map((member)=>_services.TeamService.removeMember(member.id)));
                    _antd.message.success(`成功移除 ${selectedMembers.length} 名成员`);
                    setSelectedRowKeys([]);
                    fetchMembers();
                    onMemberChange === null || onMemberChange === void 0 || onMemberChange();
                } catch (error) {
                    console.error('批量移除成员失败:', error);
                    _antd.message.error('批量移除失败');
                }
            }
        });
    };
    const columns = [
        {
            title: '成员',
            dataIndex: 'name',
            key: 'name',
            render: (name, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                lineNumber: 222,
                                columnNumber: 38
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 222,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: name
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                    lineNumber: 224,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        fontSize: 12,
                                        color: '#999'
                                    },
                                    children: record.email
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                    lineNumber: 225,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 223,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 221,
                    columnNumber: 9
                }, this)
        },
        {
            title: '角色',
            dataIndex: 'isCreator',
            key: 'role',
            width: 100,
            render: (isCreator)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                    color: isCreator ? 'gold' : 'blue',
                    icon: isCreator ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                        lineNumber: 236,
                        columnNumber: 68
                    }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                        lineNumber: 236,
                        columnNumber: 88
                    }, void 0),
                    children: isCreator ? '创建者' : '成员'
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 236,
                    columnNumber: 9
                }, this)
        },
        {
            title: '状态',
            dataIndex: 'isActive',
            key: 'status',
            width: 80,
            render: (isActive)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                    color: isActive ? 'green' : 'red',
                    children: isActive ? '活跃' : '停用'
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 247,
                    columnNumber: 9
                }, this)
        },
        {
            title: '加入时间',
            dataIndex: 'assignedAt',
            key: 'assignedAt',
            width: 150,
            render: (assignedAt)=>new Date(assignedAt).toLocaleDateString()
        },
        {
            title: '最后访问',
            dataIndex: 'lastAccessTime',
            key: 'lastAccessTime',
            width: 150,
            render: (lastAccessTime)=>{
                const date = new Date(lastAccessTime);
                const now = new Date();
                const diffDays = Math.floor((now.getTime() - date.getTime()) / 86400000);
                let color = 'green';
                if (diffDays > 7) color = 'orange';
                if (diffDays > 30) color = 'red';
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                    title: date.toLocaleString(),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                        color: color,
                        children: diffDays === 0 ? '今天' : `${diffDays}天前`
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                        lineNumber: 275,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 274,
                    columnNumber: 11
                }, this);
            }
        },
        {
            title: '操作',
            key: 'action',
            width: 120,
            render: (_, record)=>{
                if (!isCreator || record.isCreator) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    type: "secondary",
                    children: "-"
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 288,
                    columnNumber: 18
                }, this);
                const menuItems = [
                    {
                        key: 'remove',
                        label: '移除成员',
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 295,
                            columnNumber: 19
                        }, this),
                        danger: true,
                        onClick: ()=>handleRemoveMember(record)
                    }
                ];
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    size: "small",
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "text",
                            danger: true,
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                lineNumber: 307,
                                columnNumber: 21
                            }, void 0),
                            onClick: ()=>handleRemoveMember(record),
                            children: "移除"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 303,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                            menu: {
                                items: menuItems
                            },
                            trigger: [
                                'click'
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "text",
                                size: "small",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.HarmonyOSOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                    lineNumber: 316,
                                    columnNumber: 23
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                lineNumber: 313,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 312,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 302,
                    columnNumber: 11
                }, this);
            }
        }
    ];
    const rowSelection = {
        selectedRowKeys,
        onChange: (newSelectedRowKeys)=>{
            setSelectedRowKeys(newSelectedRowKeys);
        },
        getCheckboxProps: (record)=>({
                disabled: record.isCreator
            })
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    strong: true,
                    children: "团队成员"
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 339,
                    columnNumber: 11
                }, void 0),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Badge, {
                    count: filteredMembers.length,
                    showZero: true
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 340,
                    columnNumber: 11
                }, void 0)
            ]
        }, void 0, true, {
            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
            lineNumber: 338,
            columnNumber: 9
        }, void 0),
        extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                    value: statusFilter,
                    onChange: setStatusFilter,
                    style: {
                        width: 120
                    },
                    size: "small",
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "all",
                            children: "全部"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 351,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "active",
                            children: "活跃"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 352,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "inactive",
                            children: "停用"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 353,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "creator",
                            children: "创建者"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 354,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                            value: "member",
                            children: "成员"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 355,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 345,
                    columnNumber: 11
                }, void 0),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                    placeholder: "搜索成员",
                    prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                        lineNumber: 359,
                        columnNumber: 21
                    }, void 0),
                    value: searchText,
                    onChange: (e)=>setSearchText(e.target.value),
                    style: {
                        width: 200
                    },
                    size: "small"
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 357,
                    columnNumber: 11
                }, void 0)
            ]
        }, void 0, true, {
            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
            lineNumber: 344,
            columnNumber: 9
        }, void 0),
        children: [
            selectedRowKeys.length > 0 && isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginBottom: 16,
                    padding: 12,
                    background: '#f5f5f5',
                    borderRadius: 6
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            children: [
                                "已选择 ",
                                selectedRowKeys.length,
                                " 名成员"
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 371,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            size: "small",
                            danger: true,
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                                lineNumber: 375,
                                columnNumber: 21
                            }, void 0),
                            onClick: handleBatchRemove,
                            children: "批量移除"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 372,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            size: "small",
                            onClick: ()=>setSelectedRowKeys([]),
                            children: "取消选择"
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                            lineNumber: 380,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                    lineNumber: 370,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                lineNumber: 369,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                columns: columns,
                dataSource: filteredMembers,
                rowKey: "id",
                loading: loading,
                rowSelection: isCreator ? rowSelection : undefined,
                pagination: {
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total)=>`共 ${total} 名成员`,
                    pageSize: 10
                }
            }, void 0, false, {
                fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
                lineNumber: 390,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team/detail/components/TeamMemberList.tsx",
        lineNumber: 336,
        columnNumber: 5
    }, this);
};
_s(TeamMemberList, "VFoYmIR+E+CHWgWFfALfNS25o10=");
_c = TeamMemberList;
var _default = TeamMemberList;
var _c;
$RefreshReg$(_c, "TeamMemberList");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/user/components/UserProfileContent.tsx": function (module, exports, __mako_require__){
/**
 * 用户资料内容组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const UserProfileContent = ()=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(true);
    const [saving, setSaving] = (0, _react.useState)(false);
    const [editing, setEditing] = (0, _react.useState)(false);
    const [userProfile, setUserProfile] = (0, _react.useState)(null);
    const [form] = _antd.Form.useForm();
    (0, _react.useEffect)(()=>{
        fetchUserProfile();
    }, []);
    const fetchUserProfile = async ()=>{
        try {
            setLoading(true);
            const profile = await _services.UserService.getUserProfile();
            setUserProfile(profile);
            form.setFieldsValue({
                name: profile.name,
                email: profile.email
            });
        } catch (error) {
            console.error('获取用户资料失败:', error);
            _antd.message.error('获取用户资料失败');
        } finally{
            setLoading(false);
        }
    };
    const handleSaveProfile = async (values)=>{
        try {
            setSaving(true);
            const updateData = {
                name: values.name
            };
            const updatedProfile = await _services.UserService.updateUserProfile(updateData);
            setUserProfile(updatedProfile);
            setEditing(false);
            _antd.message.success('个人资料更新成功');
        } catch (error) {
            console.error('更新个人资料失败:', error);
            _antd.message.error('更新个人资料失败');
        } finally{
            setSaving(false);
        }
    };
    const handleCancel = ()=>{
        setEditing(false);
        if (userProfile) form.setFieldsValue({
            name: userProfile.name,
            email: userProfile.email
        });
    };
    if (loading || !userProfile) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: "加载中..."
    }, void 0, false, {
        fileName: "src/pages/user/components/UserProfileContent.tsx",
        lineNumber: 86,
        columnNumber: 12
    }, this);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            style: {
                display: 'flex',
                alignItems: 'flex-start',
                gap: 24
            },
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        textAlign: 'center'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                            size: 120,
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                lineNumber: 94,
                                columnNumber: 36
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                            lineNumber: 94,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginTop: 16
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Upload, {
                                showUploadList: false,
                                beforeUpload: ()=>{
                                    _antd.message.info('头像上传功能暂未实现');
                                    return false;
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UploadOutlined, {}, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 103,
                                        columnNumber: 29
                                    }, void 0),
                                    size: "small",
                                    children: "更换头像"
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                    lineNumber: 103,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                lineNumber: 96,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                            lineNumber: 95,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                    lineNumber: 93,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        flex: 1
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                marginBottom: 24
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                    level: 4,
                                    style: {
                                        margin: 0
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                                            lineNumber: 114,
                                            columnNumber: 15
                                        }, this),
                                        " 基本信息"
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                    lineNumber: 113,
                                    columnNumber: 13
                                }, this),
                                !editing && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 119,
                                        columnNumber: 23
                                    }, void 0),
                                    onClick: ()=>setEditing(true),
                                    children: "编辑资料"
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                    lineNumber: 117,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                            lineNumber: 112,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                            form: form,
                            layout: "vertical",
                            onFinish: handleSaveProfile,
                            disabled: !editing,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    label: "用户名",
                                    name: "name",
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入用户名'
                                        },
                                        {
                                            max: 100,
                                            message: '用户名不能超过100个字符'
                                        }
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                                            lineNumber: 142,
                                            columnNumber: 25
                                        }, void 0),
                                        placeholder: "请输入用户名"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 141,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                    lineNumber: 133,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    label: "邮箱地址",
                                    name: "email",
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                                            lineNumber: 152,
                                            columnNumber: 25
                                        }, void 0),
                                        disabled: true,
                                        placeholder: "邮箱地址不可修改"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 151,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                    lineNumber: 147,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        children: editing ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "primary",
                                                    htmlType: "submit",
                                                    loading: saving,
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SaveOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                                        lineNumber: 166,
                                                        columnNumber: 29
                                                    }, void 0),
                                                    children: "保存修改"
                                                }, void 0, false, {
                                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                                    lineNumber: 162,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    onClick: handleCancel,
                                                    children: "取消"
                                                }, void 0, false, {
                                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                                    lineNumber: 170,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                                lineNumber: 177,
                                                columnNumber: 27
                                            }, void 0),
                                            onClick: ()=>setEditing(true),
                                            children: "编辑资料"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                                            lineNumber: 175,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 159,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                    lineNumber: 158,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                            lineNumber: 127,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                    lineNumber: 111,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/user/components/UserProfileContent.tsx",
            lineNumber: 91,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/user/components/UserProfileContent.tsx",
        lineNumber: 90,
        columnNumber: 5
    }, this);
};
_s(UserProfileContent, "CVME3i9AOLUbymU4wh4+JIXSLeA=", false, function() {
    return [
        _antd.Form.useForm
    ];
});
_c = UserProfileContent;
var _default = UserProfileContent;
var _c;
$RefreshReg$(_c, "UserProfileContent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=common-async.js.map