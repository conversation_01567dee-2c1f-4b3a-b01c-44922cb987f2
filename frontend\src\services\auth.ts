/**
 * 认证相关 API 服务
 */

import { apiRequest, TokenManager } from '@/utils/request';
import type {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
} from '@/types/api';

/**
 * 认证服务类（单阶段令牌系统）
 */
export class AuthService {
  /**
   * 用户注册
   */
  static async register(data: RegisterRequest): Promise<void> {
    const response = await apiRequest.post<void>('/auth/register', data);
    return response.data;
  }

  /**
   * 用户登录（单阶段）
   */
  static async login(data: LoginRequest): Promise<LoginResponse> {
    const response = await apiRequest.post<LoginResponse>('/auth/login', data);

    // 保存用户Token
    if (response.data.token) {
      TokenManager.setToken(response.data.token);
    }

    return response.data;
  }



  /**
   * 刷新Token
   */
  static async refreshToken(): Promise<LoginResponse> {
    const response = await apiRequest.post<LoginResponse>('/auth/refresh-token');

    // 更新Token
    if (response.data.token) {
      TokenManager.setToken(response.data.token);
    }

    return response.data;
  }



  /**
   * 用户登出
   */
  static async logout(): Promise<void> {
    try {
      await apiRequest.post<void>('/auth/logout');
    } finally {
      // 无论请求是否成功，都清除本地 token
      TokenManager.clearToken();
    }
  }

  /**
   * 验证 Token 有效性
   */
  static async validateToken(): Promise<boolean> {
    try {
      const response = await apiRequest.get<boolean>('/auth/validate');
      return response.data;
    } catch {
      return false;
    }
  }

  /**
   * 检查是否已登录
   */
  static isLoggedIn(): boolean {
    return TokenManager.hasToken();
  }

  /**
   * 获取当前Token
   */
  static getToken(): string | null {
    return TokenManager.getToken();
  }

  /**
   * 清除Token
   */
  static clearToken(): void {
    TokenManager.clearToken();
  }

  /**
   * 清除团队Token（兼容性方法）
   */
  static clearTeamToken(): void {
    // 在单令牌系统中，清除Token即可
    TokenManager.clearToken();
  }

  /**
   * 选择团队
   */
  static async selectTeam(data: { teamId: number }): Promise<LoginResponse> {
    const response = await apiRequest.post<LoginResponse>('/auth/select-team', data);

    // 更新Token
    if (response.data.token) {
      TokenManager.setToken(response.data.token);
    }

    return response.data;
  }

  /**
   * 切换团队
   */
  static async switchTeam(data: { teamId: number }): Promise<LoginResponse> {
    const response = await apiRequest.post<LoginResponse>('/auth/switch-team', data);

    // 更新Token
    if (response.data.token) {
      TokenManager.setToken(response.data.token);
    }

    return response.data;
  }

  /**
   * 清除团队上下文
   */
  static async clearTeam(): Promise<string> {
    const response = await apiRequest.post<string>('/auth/clear-team');

    // 更新Token
    if (response.data) {
      TokenManager.setToken(response.data);
    }

    return response.data;
  }

  // ========== 兼容性方法 ==========

  /**
   * 检查是否已选择团队（兼容性方法）
   * @deprecated 在单令牌系统中，团队信息包含在Token中，使用 isLoggedIn 检查登录状态
   */
  static hasTeamSelected(): boolean {
    return this.isLoggedIn();
  }
  /**
   * 团队登录（兼容性方法）
   * @deprecated 使用 selectTeam 替代
   */
  static async teamLogin(data: { teamId: number }): Promise<LoginResponse> {
    return this.selectTeam(data);
  }





  /**
   * 清除所有Token（兼容性方法）
   * @deprecated 使用 clearToken 替代
   */
  static clearTokens(): void {
    this.clearToken();
  }
}

// 导出默认实例
export default AuthService;
