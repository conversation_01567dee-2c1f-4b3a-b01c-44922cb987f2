-- 数据库迁移脚本 V1.1
-- 为account_relation表添加好友请求状态管理和备注功能所需的字段

USE team_manage;

-- 添加好友备注字段
ALTER TABLE account_relation 
ADD COLUMN remark VARCHAR(255) COMMENT '好友备注' AFTER invited_at;

-- 添加好友关系状态字段
ALTER TABLE account_relation 
ADD COLUMN status ENUM('pending', 'accepted', 'rejected') DEFAULT 'pending' COMMENT '好友关系状态' AFTER remark;

-- 添加请求发送时间字段
ALTER TABLE account_relation 
ADD COLUMN requested_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '请求发送时间' AFTER status;

-- 添加状态索引
ALTER TABLE account_relation 
ADD INDEX idx_status (status, is_deleted);

-- 添加请求时间索引
ALTER TABLE account_relation 
ADD INDEX idx_requested_at (requested_at);

-- 更新现有数据，将所有现有的好友关系状态设置为已接受
UPDATE account_relation 
SET status = 'accepted', requested_at = invited_at 
WHERE is_deleted = FALSE AND is_active = TRUE;

-- 添加注释说明迁移完成
-- 迁移完成：为account_relation表添加了remark、status、requested_at字段
-- 现有的好友关系已自动设置为accepted状态
