{"version": 3, "sources": ["src/pages/Dashboard/index.tsx"], "sourcesContent": ["/**\n * 仪表板页面\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  Card, \n  Row, \n  Col, \n  Statistic, \n  Typography, \n  Space, \n  Button,\n  List,\n  Avatar,\n  Tag,\n  Progress,\n  Alert\n} from 'antd';\nimport { \n  TeamOutlined, \n  UserOutlined, \n  CrownOutlined,\n  DatabaseOutlined,\n  CalendarOutlined,\n  RightOutlined\n} from '@ant-design/icons';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { history } from '@umijs/max';\nimport { TeamService, UserService, SubscriptionService } from '@/services';\n\nconst { Title, Text } = Typography;\n\nconst Dashboard: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teamStats, setTeamStats] = useState<any>(null);\n  const [userProfile, setUserProfile] = useState<any>(null);\n  const [subscriptionStatus, setSubscriptionStatus] = useState<any>(null);\n  const [usageInfo, setUsageInfo] = useState<any>(null);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      const [stats, profile, subStatus, usage] = await Promise.all([\n        TeamService.getTeamStats().catch(() => null),\n        UserService.getUserProfile().catch(() => null),\n        SubscriptionService.checkSubscriptionStatus().catch(() => null),\n        SubscriptionService.getSubscriptionUsage().catch(() => null),\n      ]);\n      \n      setTeamStats(stats);\n      setUserProfile(profile);\n      setSubscriptionStatus(subStatus);\n      setUsageInfo(usage);\n    } catch (error) {\n      console.error('获取仪表板数据失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const quickActions = [\n    {\n      title: '邀请成员',\n      description: '邀请新成员加入团队',\n      icon: <UserOutlined />,\n      action: () => history.push('/team/detail'),\n    },\n    {\n      title: '查看团队',\n      description: '查看团队详情和成员',\n      icon: <TeamOutlined />,\n      action: () => history.push('/team/detail'),\n    },\n    {\n      title: '管理订阅',\n      description: '查看和管理订阅套餐',\n      icon: <CrownOutlined />,\n      action: () => history.push('/subscription/manage'),\n    },\n    {\n      title: '个人设置',\n      description: '修改个人资料和偏好',\n      icon: <UserOutlined />,\n      action: () => history.push('/user/profile'),\n    },\n  ];\n\n  return (\n    <PageContainer\n      title=\"仪表板\"\n      subTitle=\"欢迎回来！这里是您的工作概览\"\n    >\n      <Space direction=\"vertical\" style={{ width: '100%' }} size=\"large\">\n        {/* 警告信息 */}\n        {subscriptionStatus?.isExpiringSoon && (\n          <Alert\n            message=\"订阅即将到期\"\n            description={`您的订阅将在 ${subscriptionStatus.daysUntilExpiry} 天后到期，请及时续费。`}\n            type=\"warning\"\n            showIcon\n            action={\n              <Button size=\"small\" onClick={() => history.push('/subscription/manage')}>\n                立即续费\n              </Button>\n            }\n          />\n        )}\n\n        {usageInfo?.usagePercentage > 80 && (\n          <Alert\n            message=\"存储空间不足\"\n            description={`您的存储空间使用量已达到 ${usageInfo.usagePercentage.toFixed(1)}%，建议升级套餐。`}\n            type=\"warning\"\n            showIcon\n            action={\n              <Button size=\"small\" onClick={() => history.push('/subscription/plans')}>\n                升级套餐\n              </Button>\n            }\n          />\n        )}\n\n        {/* 统计卡片 */}\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"团队成员\"\n                value={teamStats?.memberCount || 0}\n                prefix={<TeamOutlined />}\n                suffix=\"人\"\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"活跃成员\"\n                value={teamStats?.activeMembers || 0}\n                prefix={<UserOutlined />}\n                suffix=\"人\"\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"存储使用\"\n                value={usageInfo?.usagePercentage || 0}\n                prefix={<DatabaseOutlined />}\n                suffix=\"%\"\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"剩余天数\"\n                value={usageInfo?.remainingDays || 0}\n                prefix={<CalendarOutlined />}\n                suffix=\"天\"\n              />\n            </Card>\n          </Col>\n        </Row>\n\n        <Row gutter={[16, 16]}>\n          {/* 存储使用情况 */}\n          <Col xs={24} lg={12}>\n            <Card title=\"存储使用情况\" loading={loading}>\n              {usageInfo && (\n                <div>\n                  <div style={{ marginBottom: 16 }}>\n                    <Text>已使用 {usageInfo.currentUsage}GB / {usageInfo.maxUsage}GB</Text>\n                  </div>\n                  <Progress\n                    percent={usageInfo.usagePercentage}\n                    status={usageInfo.usagePercentage > 80 ? 'exception' : 'normal'}\n                    strokeColor={usageInfo.usagePercentage > 80 ? '#ff4d4f' : '#1890ff'}\n                  />\n                  <div style={{ marginTop: 8, textAlign: 'right' }}>\n                    <Button\n                      type=\"link\"\n                      size=\"small\"\n                      onClick={() => history.push('/subscription/plans')}\n                    >\n                      升级存储空间\n                    </Button>\n                  </div>\n                </div>\n              )}\n            </Card>\n          </Col>\n\n          {/* 快速操作 */}\n          <Col xs={24} lg={12}>\n            <Card title=\"快速操作\">\n              <List\n                dataSource={quickActions}\n                renderItem={(item) => (\n                  <List.Item\n                    style={{ cursor: 'pointer' }}\n                    onClick={item.action}\n                  >\n                    <List.Item.Meta\n                      avatar={<Avatar icon={item.icon} />}\n                      title={item.title}\n                      description={item.description}\n                    />\n                    <RightOutlined />\n                  </List.Item>\n                )}\n              />\n            </Card>\n          </Col>\n        </Row>\n\n        {/* 用户信息和订阅状态 */}\n        <Row gutter={[16, 16]}>\n          <Col xs={24} lg={12}>\n            <Card title=\"个人信息\" loading={loading}>\n              {userProfile && (\n                <div>\n                  <Space direction=\"vertical\" style={{ width: '100%' }}>\n                    <div>\n                      <Text strong>用户名：</Text>\n                      <Text>{userProfile.name}</Text>\n                    </div>\n                    <div>\n                      <Text strong>邮箱：</Text>\n                      <Text>{userProfile.email}</Text>\n                    </div>\n                    <div>\n                      <Text strong>注册时间：</Text>\n                      <Text>{new Date(userProfile.createdAt).toLocaleDateString()}</Text>\n                    </div>\n                  </Space>\n                  <div style={{ marginTop: 16, textAlign: 'right' }}>\n                    <Button\n                      type=\"link\"\n                      size=\"small\"\n                      onClick={() => history.push('/user/profile')}\n                    >\n                      编辑资料\n                    </Button>\n                  </div>\n                </div>\n              )}\n            </Card>\n          </Col>\n\n          <Col xs={24} lg={12}>\n            <Card title=\"订阅状态\" loading={loading}>\n              {subscriptionStatus && (\n                <div>\n                  <Space direction=\"vertical\" style={{ width: '100%' }}>\n                    <div>\n                      <Text strong>订阅状态：</Text>\n                      <Tag color={subscriptionStatus.hasActiveSubscription ? 'green' : 'red'}>\n                        {subscriptionStatus.hasActiveSubscription ? '已订阅' : '未订阅'}\n                      </Tag>\n                    </div>\n                    {subscriptionStatus.hasActiveSubscription && (\n                      <>\n                        <div>\n                          <Text strong>到期时间：</Text>\n                          <Text>{subscriptionStatus.daysUntilExpiry} 天后</Text>\n                        </div>\n                        {subscriptionStatus.needsUpgrade && (\n                          <div>\n                            <Tag color=\"orange\">建议升级</Tag>\n                            <Text type=\"secondary\">存储使用量较高</Text>\n                          </div>\n                        )}\n                      </>\n                    )}\n                  </Space>\n                  <div style={{ marginTop: 16, textAlign: 'right' }}>\n                    <Button\n                      type=\"link\"\n                      size=\"small\"\n                      onClick={() => history.push('/subscription/manage')}\n                    >\n                      管理订阅\n                    </Button>\n                  </div>\n                </div>\n              )}\n            </Card>\n          </Col>\n        </Row>\n      </Space>\n    </PageContainer>\n  );\n};\n\nexport default Dashboard;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BA2SD;;;eAAA;;;;;;wEAzS2C;6BAcpC;8BAQA;sCACuB;4BACN;iCACsC;;;;;;;;;;AAE9D,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAElC,MAAM,YAAsB;;IAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAM;IAChD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAM;IACpD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAM;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAM;IAEhD,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI;YACF,WAAW;YACX,MAAM,CAAC,OAAO,SAAS,WAAW,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC3D,qBAAW,CAAC,YAAY,GAAG,KAAK,CAAC,IAAM;gBACvC,qBAAW,CAAC,cAAc,GAAG,KAAK,CAAC,IAAM;gBACzC,6BAAmB,CAAC,uBAAuB,GAAG,KAAK,CAAC,IAAM;gBAC1D,6BAAmB,CAAC,oBAAoB,GAAG,KAAK,CAAC,IAAM;aACxD;YAED,aAAa;YACb,eAAe;YACf,sBAAsB;YACtB,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;QAC9B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB;YACE,OAAO;YACP,aAAa;YACb,oBAAM,2BAAC,mBAAY;;;;;YACnB,QAAQ,IAAM,YAAO,CAAC,IAAI,CAAC;QAC7B;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,2BAAC,mBAAY;;;;;YACnB,QAAQ,IAAM,YAAO,CAAC,IAAI,CAAC;QAC7B;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,2BAAC,oBAAa;;;;;YACpB,QAAQ,IAAM,YAAO,CAAC,IAAI,CAAC;QAC7B;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,2BAAC,mBAAY;;;;;YACnB,QAAQ,IAAM,YAAO,CAAC,IAAI,CAAC;QAC7B;KACD;IAED,qBACE,2BAAC,4BAAa;QACZ,OAAM;QACN,UAAS;kBAET,cAAA,2BAAC,WAAK;YAAC,WAAU;YAAW,OAAO;gBAAE,OAAO;YAAO;YAAG,MAAK;;gBAExD,CAAA,+BAAA,yCAAA,mBAAoB,cAAc,mBACjC,2BAAC,WAAK;oBACJ,SAAQ;oBACR,aAAa,CAAC,OAAO,EAAE,mBAAmB,eAAe,CAAC,YAAY,CAAC;oBACvE,MAAK;oBACL,QAAQ;oBACR,sBACE,2BAAC,YAAM;wBAAC,MAAK;wBAAQ,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;kCAAyB;;;;;;;;;;;gBAO/E,CAAA,sBAAA,gCAAA,UAAW,eAAe,IAAG,oBAC5B,2BAAC,WAAK;oBACJ,SAAQ;oBACR,aAAa,CAAC,aAAa,EAAE,UAAU,eAAe,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;oBAC5E,MAAK;oBACL,QAAQ;oBACR,sBACE,2BAAC,YAAM;wBAAC,MAAK;wBAAQ,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;kCAAwB;;;;;;;;;;;8BAQ/E,2BAAC,SAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;;sCACnB,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,2BAAC,UAAI;0CACH,cAAA,2BAAC,eAAS;oCACR,OAAM;oCACN,OAAO,CAAA,sBAAA,gCAAA,UAAW,WAAW,KAAI;oCACjC,sBAAQ,2BAAC,mBAAY;;;;;oCACrB,QAAO;;;;;;;;;;;;;;;;sCAIb,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,2BAAC,UAAI;0CACH,cAAA,2BAAC,eAAS;oCACR,OAAM;oCACN,OAAO,CAAA,sBAAA,gCAAA,UAAW,aAAa,KAAI;oCACnC,sBAAQ,2BAAC,mBAAY;;;;;oCACrB,QAAO;;;;;;;;;;;;;;;;sCAIb,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,2BAAC,UAAI;0CACH,cAAA,2BAAC,eAAS;oCACR,OAAM;oCACN,OAAO,CAAA,sBAAA,gCAAA,UAAW,eAAe,KAAI;oCACrC,sBAAQ,2BAAC,uBAAgB;;;;;oCACzB,QAAO;;;;;;;;;;;;;;;;sCAIb,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,2BAAC,UAAI;0CACH,cAAA,2BAAC,eAAS;oCACR,OAAM;oCACN,OAAO,CAAA,sBAAA,gCAAA,UAAW,aAAa,KAAI;oCACnC,sBAAQ,2BAAC,uBAAgB;;;;;oCACzB,QAAO;;;;;;;;;;;;;;;;;;;;;;8BAMf,2BAAC,SAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;;sCAEnB,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;sCACf,cAAA,2BAAC,UAAI;gCAAC,OAAM;gCAAS,SAAS;0CAC3B,2BACC,2BAAC;;sDACC,2BAAC;4CAAI,OAAO;gDAAE,cAAc;4CAAG;sDAC7B,cAAA,2BAAC;;oDAAK;oDAAK,UAAU,YAAY;oDAAC;oDAAM,UAAU,QAAQ;oDAAC;;;;;;;;;;;;sDAE7D,2BAAC,cAAQ;4CACP,SAAS,UAAU,eAAe;4CAClC,QAAQ,UAAU,eAAe,GAAG,KAAK,cAAc;4CACvD,aAAa,UAAU,eAAe,GAAG,KAAK,YAAY;;;;;;sDAE5D,2BAAC;4CAAI,OAAO;gDAAE,WAAW;gDAAG,WAAW;4CAAQ;sDAC7C,cAAA,2BAAC,YAAM;gDACL,MAAK;gDACL,MAAK;gDACL,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;0DAC7B;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUX,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;sCACf,cAAA,2BAAC,UAAI;gCAAC,OAAM;0CACV,cAAA,2BAAC,UAAI;oCACH,YAAY;oCACZ,YAAY,CAAC,qBACX,2BAAC,UAAI,CAAC,IAAI;4CACR,OAAO;gDAAE,QAAQ;4CAAU;4CAC3B,SAAS,KAAK,MAAM;;8DAEpB,2BAAC,UAAI,CAAC,IAAI,CAAC,IAAI;oDACb,sBAAQ,2BAAC,YAAM;wDAAC,MAAM,KAAK,IAAI;;;;;;oDAC/B,OAAO,KAAK,KAAK;oDACjB,aAAa,KAAK,WAAW;;;;;;8DAE/B,2BAAC,oBAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS1B,2BAAC,SAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;;sCACnB,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;sCACf,cAAA,2BAAC,UAAI;gCAAC,OAAM;gCAAO,SAAS;0CACzB,6BACC,2BAAC;;sDACC,2BAAC,WAAK;4CAAC,WAAU;4CAAW,OAAO;gDAAE,OAAO;4CAAO;;8DACjD,2BAAC;;sEACC,2BAAC;4DAAK,MAAM;sEAAC;;;;;;sEACb,2BAAC;sEAAM,YAAY,IAAI;;;;;;;;;;;;8DAEzB,2BAAC;;sEACC,2BAAC;4DAAK,MAAM;sEAAC;;;;;;sEACb,2BAAC;sEAAM,YAAY,KAAK;;;;;;;;;;;;8DAE1B,2BAAC;;sEACC,2BAAC;4DAAK,MAAM;sEAAC;;;;;;sEACb,2BAAC;sEAAM,IAAI,KAAK,YAAY,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;sDAG7D,2BAAC;4CAAI,OAAO;gDAAE,WAAW;gDAAI,WAAW;4CAAQ;sDAC9C,cAAA,2BAAC,YAAM;gDACL,MAAK;gDACL,MAAK;gDACL,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;0DAC7B;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASX,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;sCACf,cAAA,2BAAC,UAAI;gCAAC,OAAM;gCAAO,SAAS;0CACzB,oCACC,2BAAC;;sDACC,2BAAC,WAAK;4CAAC,WAAU;4CAAW,OAAO;gDAAE,OAAO;4CAAO;;8DACjD,2BAAC;;sEACC,2BAAC;4DAAK,MAAM;sEAAC;;;;;;sEACb,2BAAC,SAAG;4DAAC,OAAO,mBAAmB,qBAAqB,GAAG,UAAU;sEAC9D,mBAAmB,qBAAqB,GAAG,QAAQ;;;;;;;;;;;;gDAGvD,mBAAmB,qBAAqB,kBACvC;;sEACE,2BAAC;;8EACC,2BAAC;oEAAK,MAAM;8EAAC;;;;;;8EACb,2BAAC;;wEAAM,mBAAmB,eAAe;wEAAC;;;;;;;;;;;;;wDAE3C,mBAAmB,YAAY,kBAC9B,2BAAC;;8EACC,2BAAC,SAAG;oEAAC,OAAM;8EAAS;;;;;;8EACpB,2BAAC;oEAAK,MAAK;8EAAY;;;;;;;;;;;;;;;;;;;;sDAMjC,2BAAC;4CAAI,OAAO;gDAAE,WAAW;gDAAI,WAAW;4CAAQ;sDAC9C,cAAA,2BAAC,YAAM;gDACL,MAAK;gDACL,MAAK;gDACL,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;0DAC7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB;GA1QM;KAAA;IA4QN,WAAe"}