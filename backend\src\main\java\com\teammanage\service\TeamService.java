package com.teammanage.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.teammanage.context.TeamContextHolder;
import com.teammanage.dto.request.CreateTeamRequest;
import com.teammanage.dto.request.InviteMembersRequest;
import com.teammanage.dto.request.UpdateTeamRequest;
import com.teammanage.dto.response.TeamDetailResponse;
import com.teammanage.dto.response.TeamMemberResponse;
import com.teammanage.entity.Account;
import com.teammanage.entity.Team;
import com.teammanage.entity.TeamMember;
import com.teammanage.exception.BusinessException;
import com.teammanage.exception.InsufficientPermissionException;
import com.teammanage.exception.ResourceNotFoundException;
import com.teammanage.mapper.AccountMapper;
import com.teammanage.mapper.TeamMapper;
import com.teammanage.mapper.TeamMemberMapper;
import com.teammanage.util.TeamPermissionChecker;

/**
 * 团队管理服务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class TeamService {

    private static final Logger log = LoggerFactory.getLogger(TeamService.class);

    @Autowired
    private TeamMapper teamMapper;

    @Autowired
    private TeamMemberMapper teamMemberMapper;

    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private TeamPermissionChecker permissionChecker;

    @Autowired
    private FriendService friendService;

    @Autowired
    private com.teammanage.service.SubscriptionService subscriptionService;

    @Value("${app.team.max-members:100}")
    private int maxMembers;

    /**
     * 创建团队
     *
     * @param request 创建团队请求
     * @param creatorId 创建者ID
     * @return 团队详情
     */
    @Transactional
    public TeamDetailResponse createTeam(CreateTeamRequest request, Long creatorId) {
        // 检查用户订阅限制 - 验证可创建的团队数量
        int teamLimit = subscriptionService.getUserTeamLimit(creatorId);
        int currentTeamCount = getUserCreatedTeamCount(creatorId);

        if (currentTeamCount >= teamLimit) {
            throw new BusinessException("已达到订阅套餐的团队创建数量限制，当前限制：" + teamLimit + "个团队");
        }

        // 检查团队名称是否已存在
        if (teamMapper.existsByName(request.getName())) {
            throw new BusinessException("团队名称已存在");
        }

        // 创建团队
        Team team = new Team();
        team.setName(request.getName());
        team.setDescription(request.getDescription());
        team.setCreatedBy(creatorId);
        team.setIsDeleted(false);

        teamMapper.insert(team);

        // 添加创建者为团队成员
        TeamMember creatorMember = new TeamMember();
        creatorMember.setTeamId(team.getId());
        creatorMember.setAccountId(creatorId);
        creatorMember.setIsCreator(true);
        creatorMember.setAssignedAt(LocalDateTime.now());
        creatorMember.setLastAccessTime(LocalDateTime.now());
        creatorMember.setIsActive(true);
        creatorMember.setIsDeleted(false);

        teamMemberMapper.insert(creatorMember);

        log.info("团队创建成功: teamId={}, name={}, creatorId={}, currentCount={}/{}",
                team.getId(), team.getName(), creatorId, currentTeamCount + 1, teamLimit);

        return getTeamDetail(team.getId());
    }

    /**
     * 获取团队详情
     * 
     * @param teamId 团队ID
     * @return 团队详情
     */
    public TeamDetailResponse getTeamDetail(Long teamId) {
        Team team = teamMapper.selectById(teamId);
        if (team == null || team.getIsDeleted()) {
            throw new ResourceNotFoundException("团队不存在");
        }

        // 统计成员数量
        int memberCount = teamMemberMapper.countByTeamId(teamId);

        TeamDetailResponse response = new TeamDetailResponse();
        response.setId(team.getId());
        response.setName(team.getName());
        response.setDescription(team.getDescription());
        response.setCreatedBy(team.getCreatedBy());
        response.setMemberCount(memberCount);
        response.setCreatedAt(team.getCreatedAt());
        response.setUpdatedAt(team.getUpdatedAt());

        return response;
    }

    /**
     * 获取当前团队详情
     * 
     * @return 团队详情
     */
    public TeamDetailResponse getCurrentTeamDetail() {
        Long teamId = TeamContextHolder.getCurrentTeamId();
        return getTeamDetail(teamId);
    }

    /**
     * 更新团队信息
     * 
     * @param request 更新请求
     * @return 更新后的团队详情
     */
    @Transactional
    public TeamDetailResponse updateTeam(UpdateTeamRequest request) {
        // 检查权限
        permissionChecker.checkTeamManagePermission();

        Long teamId = TeamContextHolder.getCurrentTeamId();
        Team team = teamMapper.selectById(teamId);
        if (team == null || team.getIsDeleted()) {
            throw new ResourceNotFoundException("团队不存在");
        }

        // 检查团队名称是否已被其他团队使用
        if (!team.getName().equals(request.getName()) && teamMapper.existsByName(request.getName())) {
            throw new BusinessException("团队名称已存在");
        }

        // 更新团队信息
        team.setName(request.getName());
        team.setDescription(request.getDescription());
        teamMapper.updateById(team);

        log.info("团队信息更新成功: teamId={}, name={}", teamId, request.getName());

        return getTeamDetail(teamId);
    }

    /**
     * 获取团队成员列表
     * 
     * @param page 页码
     * @param size 页大小
     * @return 成员列表
     */
    public Page<TeamMemberResponse> getTeamMembers(int page, int size) {
        Long teamId = TeamContextHolder.getCurrentTeamId();
        
        QueryWrapper<TeamMember> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("team_id", teamId)
                   .eq("is_active", true)
                   .eq("is_deleted", false)
                   .orderByDesc("is_creator")
                   .orderByDesc("assigned_at");

        Page<TeamMember> memberPage = teamMemberMapper.selectPage(new Page<>(page, size), queryWrapper);
        
        // 转换为响应DTO
        List<TeamMemberResponse> memberResponses = memberPage.getRecords().stream()
                .map(this::convertToMemberResponse)
                .collect(Collectors.toList());

        Page<TeamMemberResponse> responsePage = new Page<>(page, size);
        responsePage.setRecords(memberResponses);
        responsePage.setTotal(memberPage.getTotal());
        responsePage.setPages(memberPage.getPages());

        return responsePage;
    }

    /**
     * 邀请成员
     *
     * @param request 邀请请求
     */
    @Transactional
    public void inviteMembers(InviteMembersRequest request) {
        // 检查权限
        permissionChecker.checkMemberManagePermission();

        Long teamId = TeamContextHolder.getCurrentTeamId();
        Long inviterId = TeamContextHolder.getCurrentUserId();

        // 移除团队成员数量限制检查，允许无限制添加成员

        for (String email : request.getEmails()) {
            try {
                inviteMember(teamId, email, inviterId);
            } catch (Exception e) {
                log.warn("邀请成员失败: email={}, error={}", email, e.getMessage());
                // 继续处理其他邮箱，不中断整个流程
            }
        }

        log.info("批量邀请成员完成: teamId={}, inviterCount={}", teamId, request.getEmails().size());
    }

    /**
     * 邀请单个成员
     */
    private void inviteMember(Long teamId, String email, Long inviterId) {
        // 查找用户
        Account account = accountMapper.findByEmail(email);
        if (account == null) {
            throw new BusinessException("用户不存在: " + email);
        }

        // 检查是否已是团队成员
        TeamMember existingMember = teamMemberMapper.findByTeamIdAndAccountId(teamId, account.getId());
        if (existingMember != null && !existingMember.getIsDeleted()) {
            throw new BusinessException("用户已是团队成员: " + email);
        }

        // 添加团队成员
        TeamMember member = new TeamMember();
        member.setTeamId(teamId);
        member.setAccountId(account.getId());
        member.setIsCreator(false);
        member.setAssignedAt(LocalDateTime.now());
        member.setIsActive(true);
        member.setIsDeleted(false);

        teamMemberMapper.insert(member);

        log.info("成员邀请成功: teamId={}, email={}, memberId={}", teamId, email, account.getId());
    }

    /**
     * 直接分配单个成员到团队（无需被分配者同意）
     */
    private void assignMemberToTeam(Long teamId, String email, Long assignerId) {
        // 查找用户
        Account account = accountMapper.findByEmail(email);
        if (account == null) {
            throw new BusinessException("用户不存在: " + email);
        }

        // 检查是否已是团队成员
        TeamMember existingMember = teamMemberMapper.findByTeamIdAndAccountId(teamId, account.getId());
        if (existingMember != null && !existingMember.getIsDeleted()) {
            throw new BusinessException("用户已是团队成员: " + email);
        }

        // 直接添加团队成员，无需同意
        TeamMember member = new TeamMember();
        member.setTeamId(teamId);
        member.setAccountId(account.getId());
        member.setIsCreator(false);
        member.setAssignedAt(LocalDateTime.now());
        member.setIsActive(true);
        member.setIsDeleted(false);

        teamMemberMapper.insert(member);

        log.info("成员分配成功: teamId={}, email={}, memberId={}, assignerId={}",
                teamId, email, account.getId(), assignerId);
    }

    /**
     * 移除团队成员
     * 
     * @param memberId 成员ID
     */
    @Transactional
    public void removeMember(Long memberId) {
        // 检查权限
        permissionChecker.checkMemberManagePermission();

        Long teamId = TeamContextHolder.getCurrentTeamId();
        Long currentUserId = TeamContextHolder.getCurrentUserId();

        TeamMember member = teamMemberMapper.selectById(memberId);
        if (member == null || member.getIsDeleted() || !member.getTeamId().equals(teamId)) {
            throw new ResourceNotFoundException("团队成员不存在");
        }

        // 不能移除自己
        if (member.getAccountId().equals(currentUserId)) {
            throw new BusinessException("不能移除自己");
        }

        // 不能移除其他创建者
        if (member.getIsCreator()) {
            throw new BusinessException("不能移除团队创建者");
        }

        // 软删除成员
        member.setIsDeleted(true);
        member.setIsActive(false);
        teamMemberMapper.updateById(member);

        log.info("团队成员移除成功: teamId={}, memberId={}, accountId={}",
                teamId, memberId, member.getAccountId());
    }

    /**
     * 从好友列表中分配成员到团队
     *
     * 业务逻辑：
     * 1. 检查当前用户是否有成员管理权限（只有团队创建者）
     * 2. 获取当前用户的好友列表，验证所有要分配的用户都在好友列表中
     * 3. 直接将好友分配到指定团队，无需被分配者同意
     * 4. 移除团队成员数量限制，允许无限制添加成员
     * 5. 统计成功分配的数量并记录日志
     *
     * 安全性：
     * - 只能分配自己的好友，防止恶意分配陌生用户
     * - 只有团队创建者可以进行成员分配操作
     * - 使用事务确保数据一致性
     *
     * @param friendIds 要分配的好友ID列表
     * @throws BusinessException 当权限不足、好友不存在时
     */
    @Transactional
    public void assignFriendsToTeam(List<Long> friendIds) {
        // 检查权限 - 只有团队创建者可以分配成员
        permissionChecker.checkMemberManagePermission();

        Long teamId = TeamContextHolder.getCurrentTeamId();
        Long assignerId = TeamContextHolder.getCurrentUserId();

        // 移除团队成员数量限制检查，允许无限制添加成员

        // 获取当前用户的好友列表
        List<Account> friends = friendService.getFriends(assignerId);
        List<Long> availableFriendIds = friends.stream()
                .map(Account::getId)
                .collect(Collectors.toList());

        // 验证所有要分配的用户都是好友
        for (Long friendId : friendIds) {
            if (!availableFriendIds.contains(friendId)) {
                Account account = accountMapper.selectById(friendId);
                String email = account != null ? account.getEmail() : "未知用户";
                throw new BusinessException("用户 " + email + " 不在您的好友列表中");
            }
        }

        // 直接分配好友到团队
        int successCount = 0;
        for (Long friendId : friendIds) {
            try {
                Account friend = accountMapper.selectById(friendId);
                if (friend != null) {
                    assignMemberToTeam(teamId, friend.getEmail(), assignerId);
                    successCount++;
                }
            } catch (Exception e) {
                log.warn("分配好友失败: friendId={}, error={}", friendId, e.getMessage());
                // 继续处理其他好友，不中断整个流程
            }
        }

        log.info("从好友列表分配成员完成: teamId={}, assignerId={}, totalCount={}, successCount={}",
                teamId, assignerId, friendIds.size(), successCount);
    }

    /**
     * 兼容旧接口的邀请好友方法
     * @deprecated 使用 assignFriendsToTeam 替代
     */
    @Deprecated
    @Transactional
    public void inviteFriendsToTeam(List<Long> friendIds) {
        assignFriendsToTeam(friendIds);
    }

    /**
     * 删除团队
     *
     * 业务逻辑：
     * 1. 检查当前用户是否为团队创建者（通过权限检查器）
     * 2. 获取团队信息并验证团队存在且未被删除
     * 3. 双重验证：确保当前用户确实是团队创建者
     * 4. 软删除团队记录（设置is_deleted=true）
     * 5. 级联软删除所有团队成员关系
     * 6. 记录删除操作日志
     *
     * 注意：此操作使用软删除，数据不会从数据库中物理删除
     *
     * @throws InsufficientPermissionException 当用户不是团队创建者时
     * @throws ResourceNotFoundException 当团队不存在或已被删除时
     */
    @Transactional
    public void deleteTeam() {
        // 检查权限 - 只有团队创建者可以删除团队
        permissionChecker.checkTeamManagePermission();

        Long teamId = TeamContextHolder.getCurrentTeamId();
        Long currentUserId = TeamContextHolder.getCurrentUserId();

        // 获取团队信息
        Team team = teamMapper.selectById(teamId);
        if (team == null || team.getIsDeleted()) {
            throw new ResourceNotFoundException("团队不存在");
        }

        // 双重检查：确保当前用户是团队创建者
        if (!team.getCreatedBy().equals(currentUserId)) {
            throw new InsufficientPermissionException("只有团队创建者可以删除团队");
        }

        // 软删除团队
        team.setIsDeleted(true);
        teamMapper.updateById(team);

        // 软删除所有团队成员关系
        QueryWrapper<TeamMember> memberQueryWrapper = new QueryWrapper<>();
        memberQueryWrapper.eq("team_id", teamId)
                         .eq("is_deleted", false);

        List<TeamMember> teamMembers = teamMemberMapper.selectList(memberQueryWrapper);
        for (TeamMember member : teamMembers) {
            member.setIsDeleted(true);
            member.setIsActive(false);
            teamMemberMapper.updateById(member);
        }

        log.info("团队删除成功: teamId={}, teamName={}, creatorId={}, memberCount={}",
                teamId, team.getName(), currentUserId, teamMembers.size());
    }

    /**
     * 获取用户参与的团队列表
     *
     * @param userId 用户ID
     * @return 团队列表
     */
    public List<TeamDetailResponse> getUserTeams(Long userId) {
        List<TeamMember> teamMembers = teamMemberMapper.findByAccountId(userId);

        return teamMembers.stream()
                .map(member -> {
                    TeamDetailResponse response = getTeamDetail(member.getTeamId());
                    response.setIsCreator(member.getIsCreator());
                    response.setLastAccessTime(member.getLastAccessTime());
                    return response;
                })
                .collect(Collectors.toList());
    }

    /**
     * 转换团队成员为响应DTO
     */
    private TeamMemberResponse convertToMemberResponse(TeamMember member) {
        Account account = accountMapper.selectById(member.getAccountId());
        
        TeamMemberResponse response = new TeamMemberResponse();
        response.setId(member.getId());
        response.setAccountId(member.getAccountId());
        response.setEmail(account != null ? account.getEmail() : "");
        response.setName(account != null ? account.getName() : "");
        response.setIsCreator(member.getIsCreator());
        response.setAssignedAt(member.getAssignedAt());
        response.setLastAccessTime(member.getLastAccessTime());
        response.setIsActive(member.getIsActive());
        
        return response;
    }

    /**
     * 获取团队成员数量
     *
     * @param teamId 团队ID
     * @return 成员数量
     */
    public int getMemberCount(Long teamId) {
        return teamMemberMapper.countByTeamId(teamId);
    }

    /**
     * 获取用户创建的团队数量
     *
     * @param userId 用户ID
     * @return 创建的团队数量
     */
    public int getUserCreatedTeamCount(Long userId) {
        QueryWrapper<Team> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("created_by", userId)
                   .eq("is_deleted", false);
        return Math.toIntExact(teamMapper.selectCount(queryWrapper));
    }

}
