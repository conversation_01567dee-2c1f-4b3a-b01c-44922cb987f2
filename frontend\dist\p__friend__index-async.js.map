{"version": 3, "sources": ["src/pages/friend/components/AddFriend.tsx", "src/pages/friend/components/FriendList.tsx", "src/pages/friend/components/FriendRequests.tsx", "src/pages/friend/index.tsx"], "sourcesContent": ["/**\n * 添加好友组件\n */\n\nimport React, { useState } from 'react';\nimport { \n  Card, \n  Form, \n  Input, \n  Button, \n  Space, \n  Typography, \n  message,\n  List,\n  Avatar,\n  Empty,\n  Divider\n} from 'antd';\nimport { \n  UserOutlined, \n  SearchOutlined,\n  UserAddOutlined,\n  MailOutlined\n} from '@ant-design/icons';\nimport { FriendService } from '@/services';\nimport type { Account, AddFriendRequest } from '@/types/api';\n\nconst { Title, Text, Paragraph } = Typography;\n\ninterface AddFriendProps {\n  onAddSuccess: () => void;\n}\n\nconst AddFriend: React.FC<AddFriendProps> = ({ onAddSuccess }) => {\n  const [form] = Form.useForm();\n  const [searching, setSearching] = useState(false);\n  const [adding, setAdding] = useState<number | null>(null);\n  const [searchResults, setSearchResults] = useState<Account[]>([]);\n  const [searchKeyword, setSearchKeyword] = useState('');\n\n  /**\n   * 搜索用户的处理函数\n   *\n   * 执行流程：\n   * 1. 验证输入的邮箱关键词不为空\n   * 2. 设置搜索状态，显示加载动画\n   * 3. 调用API搜索匹配的用户\n   * 4. 更新搜索结果状态\n   * 5. 根据结果数量显示相应提示\n   * 6. 处理错误情况\n   *\n   * @param values 表单提交的值，包含邮箱关键词\n   */\n  const handleSearch = async (values: { email: string }) => {\n    const { email } = values;\n    if (!email.trim()) {\n      message.warning('请输入邮箱关键词');\n      return;\n    }\n\n    try {\n      setSearching(true);\n      setSearchKeyword(email);\n      const users = await FriendService.searchUsers(email.trim());\n      setSearchResults(users);\n\n      if (users.length === 0) {\n        message.info('没有找到匹配的用户');\n      }\n    } catch (error) {\n      console.error('搜索用户失败:', error);\n      message.error('搜索用户失败，请稍后重试');\n    } finally {\n      setSearching(false);\n    }\n  };\n\n  /**\n   * 发送好友请求的处理函数\n   *\n   * 执行流程：\n   * 1. 设置添加状态，显示对应用户的加载动画\n   * 2. 构造好友请求对象\n   * 3. 调用API发送好友请求\n   * 4. 从搜索结果中移除已发送请求的用户，避免重复发送\n   * 5. 调用父组件成功回调\n   * 6. 处理错误情况并显示错误消息\n   *\n   * @param user 要发送好友请求的用户对象\n   */\n  const handleAddFriend = async (user: Account) => {\n    try {\n      setAdding(user.id);\n      const request: AddFriendRequest = {\n        email: user.email\n      };\n      await FriendService.sendFriendRequest(request);\n\n      // 从搜索结果中移除已发送请求的用户，避免重复发送\n      setSearchResults(prev => prev.filter(u => u.id !== user.id));\n      message.success(`已向 \"${user.name}\" 发送好友请求`);\n      onAddSuccess();\n    } catch (error) {\n      console.error('发送好友请求失败:', error);\n      message.error('发送好友请求失败，请稍后重试');\n    } finally {\n      setAdding(null);\n    }\n  };\n\n  // 清空搜索结果\n  const handleClearSearch = () => {\n    setSearchResults([]);\n    setSearchKeyword('');\n    form.resetFields();\n  };\n\n  return (\n    <div>\n      {/* 搜索表单 */}\n      <Card title=\"搜索用户\" style={{ marginBottom: 24 }}>\n        <Form\n          form={form}\n          layout=\"inline\"\n          onFinish={handleSearch}\n          style={{ width: '100%' }}\n        >\n          <Form.Item\n            name=\"email\"\n            rules={[\n              { required: true, message: '请输入邮箱关键词' },\n              { type: 'email', message: '请输入有效的邮箱格式' }\n            ]}\n            style={{ flex: 1, marginRight: 16 }}\n          >\n            <Input\n              prefix={<MailOutlined />}\n              placeholder=\"输入邮箱地址搜索用户\"\n              size=\"large\"\n              allowClear\n            />\n          </Form.Item>\n          <Form.Item>\n            <Space>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                icon={<SearchOutlined />}\n                loading={searching}\n                size=\"large\"\n              >\n                搜索\n              </Button>\n              {searchResults.length > 0 && (\n                <Button\n                  onClick={handleClearSearch}\n                  size=\"large\"\n                >\n                  清空\n                </Button>\n              )}\n            </Space>\n          </Form.Item>\n        </Form>\n\n        <Divider />\n\n        <Paragraph type=\"secondary\">\n          <Text strong>使用说明：</Text>\n          <br />\n          • 输入完整的邮箱地址或邮箱关键词进行搜索\n          <br />\n          • 找到目标用户后点击\"添加好友\"按钮\n          <br />\n          • 添加成功后可以在\"我的好友\"中查看\n        </Paragraph>\n      </Card>\n\n      {/* 搜索结果 */}\n      {searchKeyword && (\n        <Card \n          title={`搜索结果 - \"${searchKeyword}\"`}\n          extra={\n            <Text type=\"secondary\">\n              找到 {searchResults.length} 个用户\n            </Text>\n          }\n        >\n          {searchResults.length === 0 ? (\n            <Empty\n              image={Empty.PRESENTED_IMAGE_SIMPLE}\n              description=\"没有找到匹配的用户\"\n              style={{ padding: '50px 0' }}\n            >\n              <Text type=\"secondary\">\n                请尝试使用其他关键词搜索\n              </Text>\n            </Empty>\n          ) : (\n            <List\n              dataSource={searchResults}\n              renderItem={(user) => (\n                <List.Item\n                  actions={[\n                    <Button\n                      key=\"add\"\n                      type=\"primary\"\n                      icon={<UserAddOutlined />}\n                      loading={adding === user.id}\n                      onClick={() => handleAddFriend(user)}\n                    >\n                      添加好友\n                    </Button>\n                  ]}\n                >\n                  <List.Item.Meta\n                    avatar={\n                      <Avatar \n                        size={48} \n                        icon={<UserOutlined />}\n                        style={{ backgroundColor: '#52c41a' }}\n                      >\n                        {user.name.charAt(0).toUpperCase()}\n                      </Avatar>\n                    }\n                    title={\n                      <Space>\n                        <Text strong>{user.name}</Text>\n                      </Space>\n                    }\n                    description={\n                      <Space direction=\"vertical\" size={4}>\n                        <Text type=\"secondary\">{user.email}</Text>\n                        <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                          注册时间: {new Date(user.createdAt).toLocaleDateString()}\n                        </Text>\n                      </Space>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n          )}\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default AddFriend;\n", "/**\n * 好友列表组件\n */\n\nimport React, { useState } from 'react';\nimport {\n  List,\n  Avatar,\n  Button,\n  Space,\n  Typography,\n  Input,\n  message,\n  Empty,\n  Modal,\n  Popconfirm,\n  Form,\n  Tooltip\n} from 'antd';\nimport {\n  UserOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  ExclamationCircleOutlined,\n  EditOutlined\n} from '@ant-design/icons';\nimport { FriendService } from '@/services';\nimport type { FriendWithRemark } from '@/types/api';\n\nconst { Text } = Typography;\nconst { Search } = Input;\n\ninterface FriendListProps {\n  friends: FriendWithRemark[];\n  loading: boolean;\n  onRemoveFriend: () => void;\n  onRefresh: () => void;\n}\n\nconst FriendList: React.FC<FriendListProps> = ({\n  friends,\n  loading,\n  onRemoveFriend,\n  onRefresh\n}) => {\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [removing, setRemoving] = useState<number | null>(null);\n  const [remarkModalVisible, setRemarkModalVisible] = useState(false);\n  const [currentFriend, setCurrentFriend] = useState<FriendWithRemark | null>(null);\n  const [remarkForm] = Form.useForm();\n\n  /**\n   * 根据搜索关键词过滤好友列表\n   *\n   * 搜索范围：\n   * - 好友姓名（不区分大小写）\n   * - 好友邮箱（不区分大小写）\n   */\n  const filteredFriends = friends.filter(friend =>\n    friend.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||\n    friend.email.toLowerCase().includes(searchKeyword.toLowerCase())\n  );\n\n  /**\n   * 删除好友的处理函数\n   *\n   * 执行流程：\n   * 1. 设置删除状态，显示加载动画\n   * 2. 调用API删除好友关系\n   * 3. 调用父组件回调，刷新好友列表\n   * 4. 处理错误情况并显示错误消息\n   * 5. 清除删除状态\n   *\n   * @param friend 要删除的好友对象\n   */\n  const handleRemoveFriend = async (friend: Account) => {\n    try {\n      setRemoving(friend.id);\n      await FriendService.removeFriend(friend.id);\n      message.success(`已删除好友 \"${friend.name}\"`);\n      onRemoveFriend();\n    } catch (error) {\n      console.error('删除好友失败:', error);\n      message.error('删除好友失败，请稍后重试');\n    } finally {\n      setRemoving(null);\n    }\n  };\n\n  /**\n   * 打开备注编辑模态框\n   */\n  const handleEditRemark = (friend: FriendWithRemark) => {\n    setCurrentFriend(friend);\n    setRemarkModalVisible(true);\n    // 直接使用好友对象中的备注信息\n    remarkForm.setFieldsValue({ remark: friend.remark || '' });\n  };\n\n  /**\n   * 保存好友备注\n   */\n  const handleSaveRemark = async (values: { remark: string }) => {\n    if (!currentFriend) return;\n\n    try {\n      await FriendService.setFriendRemark({\n        friendId: currentFriend.id,\n        remark: values.remark\n      });\n      message.success('备注保存成功');\n      setRemarkModalVisible(false);\n      setCurrentFriend(null);\n      remarkForm.resetFields();\n      onRefresh();\n    } catch (error) {\n      console.error('保存备注失败:', error);\n      message.error('保存备注失败');\n    }\n  };\n\n  if (friends.length === 0 && !loading) {\n    return (\n      <Empty\n        image={Empty.PRESENTED_IMAGE_SIMPLE}\n        description=\"暂无好友\"\n        style={{ padding: '50px 0' }}\n      >\n        <Text type=\"secondary\">\n          您还没有添加任何好友，快去添加好友吧！\n        </Text>\n      </Empty>\n    );\n  }\n\n  return (\n    <div>\n      {/* 搜索框 */}\n      <div style={{ marginBottom: 16 }}>\n        <Search\n          placeholder=\"搜索好友姓名或邮箱\"\n          allowClear\n          enterButton={<SearchOutlined />}\n          size=\"large\"\n          value={searchKeyword}\n          onChange={(e) => setSearchKeyword(e.target.value)}\n          style={{ maxWidth: 400 }}\n        />\n      </div>\n\n      {/* 好友列表 */}\n      <List\n        loading={loading}\n        dataSource={filteredFriends}\n        renderItem={(friend) => (\n          <List.Item\n            actions={[\n              <Tooltip key=\"remark\" title=\"编辑备注\">\n                <Button\n                  type=\"text\"\n                  icon={<EditOutlined />}\n                  onClick={() => handleEditRemark(friend)}\n                  size=\"small\"\n                >\n                  备注\n                </Button>\n              </Tooltip>,\n              <Popconfirm\n                key=\"delete\"\n                title=\"确认删除好友\"\n                description={`确定要删除好友 \"${friend.name}\" 吗？`}\n                icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}\n                onConfirm={() => handleRemoveFriend(friend)}\n                okText=\"确认删除\"\n                cancelText=\"取消\"\n                okType=\"danger\"\n              >\n                <Button\n                  type=\"text\"\n                  danger\n                  icon={<DeleteOutlined />}\n                  loading={removing === friend.id}\n                  size=\"small\"\n                >\n                  删除\n                </Button>\n              </Popconfirm>\n            ]}\n          >\n            <List.Item.Meta\n              avatar={\n                <Avatar \n                  size={48} \n                  icon={<UserOutlined />}\n                  style={{ backgroundColor: '#1890ff' }}\n                >\n                  {friend.name.charAt(0).toUpperCase()}\n                </Avatar>\n              }\n              title={\n                <Space>\n                  <Text strong>{friend.name}</Text>\n                </Space>\n              }\n              description={\n                <Space direction=\"vertical\" size={4}>\n                  <Text type=\"secondary\">{friend.email}</Text>\n                  {friend.remark && (\n                    <Text type=\"secondary\" style={{ fontSize: 12, fontStyle: 'italic' }}>\n                      备注: {friend.remark}\n                    </Text>\n                  )}\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    加入时间: {new Date(friend.createdAt).toLocaleDateString()}\n                  </Text>\n                </Space>\n              }\n            />\n          </List.Item>\n        )}\n        pagination={{\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => \n            `第 ${range[0]}-${range[1]} 条，共 ${total} 个好友`,\n        }}\n      />\n\n      {filteredFriends.length === 0 && searchKeyword && (\n        <Empty\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\n          description={`没有找到包含 \"${searchKeyword}\" 的好友`}\n          style={{ padding: '50px 0' }}\n        />\n      )}\n\n      {/* 备注编辑模态框 */}\n      <Modal\n        title=\"编辑好友备注\"\n        open={remarkModalVisible}\n        onCancel={() => {\n          setRemarkModalVisible(false);\n          setCurrentFriend(null);\n          remarkForm.resetFields();\n        }}\n        footer={null}\n        width={400}\n      >\n        <Form\n          form={remarkForm}\n          layout=\"vertical\"\n          onFinish={handleSaveRemark}\n        >\n          <Form.Item\n            label=\"好友备注\"\n            name=\"remark\"\n            rules={[\n              { max: 50, message: '备注长度不能超过50个字符' }\n            ]}\n          >\n            <Input.TextArea\n              placeholder=\"为好友添加备注...\"\n              rows={3}\n              maxLength={50}\n              showCount\n            />\n          </Form.Item>\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => {\n                setRemarkModalVisible(false);\n                setCurrentFriend(null);\n                remarkForm.resetFields();\n              }}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\">\n                保存\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default FriendList;\n", "/**\n * 好友请求管理组件\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  List,\n  Avatar,\n  Button,\n  Space,\n  Typography,\n  message,\n  Empty,\n  ExclamationCircleOutlined\n} from 'antd';\nimport {\n  UserOutlined,\n  SendOutlined\n} from '@ant-design/icons';\nimport { FriendService } from '@/services';\nimport type { Account } from '@/types/api';\n\nconst { Text, Title } = Typography;\n\ninterface FriendRequest {\n  id: number;\n  accountId: number;\n  invitedBy: number;\n  invitedAt: string;\n  requestedAt: string;\n  status: string;\n  account?: Account;\n  inviter?: Account;\n}\n\ninterface FriendRequestsProps {\n  onRequestHandled: () => void;\n}\n\nconst FriendRequests: React.FC<FriendRequestsProps> = ({ onRequestHandled }) => {\n  const [loading, setLoading] = useState(false);\n  const [sentRequests, setSentRequests] = useState<FriendRequest[]>([]);\n\n  useEffect(() => {\n    fetchRequests();\n  }, []);\n\n  const fetchRequests = async () => {\n    try {\n      setLoading(true);\n      const sent = await FriendService.getSentFriendRequests();\n      setSentRequests(sent);\n    } catch (error) {\n      console.error('获取好友请求失败:', error);\n      message.error('获取好友请求失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 由于移除了接受、拒绝、取消好友请求的功能，这里只保留查看发送的请求\n\n  if (sentRequests.length === 0 && !loading) {\n    return (\n      <Card>\n        <Empty\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\n          description=\"暂无发送的好友请求\"\n          style={{ padding: '50px 0' }}\n        />\n      </Card>\n    );\n  }\n\n  return (\n    <Card title=\"发送的好友请求\" extra={\n      <Button onClick={fetchRequests} loading={loading}>\n        刷新\n      </Button>\n    }>\n      <List\n        loading={loading}\n        dataSource={sentRequests}\n        renderItem={(request) => (\n          <List.Item>\n            <List.Item.Meta\n              avatar={\n                <Avatar\n                  size={48}\n                  icon={<UserOutlined />}\n                  style={{ backgroundColor: '#1890ff' }}\n                >\n                  {request.account?.name?.charAt(0).toUpperCase()}\n                </Avatar>\n              }\n              title={\n                <Space>\n                  <Text strong>{request.account?.name || '未知用户'}</Text>\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    (待处理)\n                  </Text>\n                </Space>\n              }\n              description={\n                <Space direction=\"vertical\" size={4}>\n                  <Text type=\"secondary\">{request.account?.email}</Text>\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    发送时间: {new Date(request.requestedAt).toLocaleString()}\n                  </Text>\n                </Space>\n              }\n            />\n          </List.Item>\n        )}\n        pagination={{\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) =>\n            `第 ${range[0]}-${range[1]} 条，共 ${total} 个请求`,\n        }}\n      />\n    </Card>\n  );\n};\n\nexport default FriendRequests;\n", "/**\n * 好友管理页面\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  Card, \n  Tabs, \n  Typography, \n  message,\n  Button,\n  Space\n} from 'antd';\nimport {\n  UserAddOutlined,\n  TeamOutlined,\n  UnorderedListOutlined,\n  InboxOutlined\n} from '@ant-design/icons';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { FriendService } from '@/services';\nimport type { FriendWithRemark } from '@/types/api';\n\n// 导入组件\nimport FriendList from './components/FriendList';\nimport AddFriend from './components/AddFriend';\nimport FriendRequests from './components/FriendRequests';\n\nconst { Title } = Typography;\n\nconst FriendManagePage: React.FC = () => {\n  const [activeTab, setActiveTab] = useState('list');\n  const [loading, setLoading] = useState(false);\n  const [friends, setFriends] = useState<FriendWithRemark[]>([]);\n\n  /**\n   * 获取好友列表（包含备注信息）\n   *\n   * 功能说明：\n   * 1. 请求好友列表，包含备注信息\n   * 2. 更新组件状态，触发UI重新渲染\n   * 3. 统一的错误处理和加载状态管理\n   */\n  const fetchFriends = async () => {\n    try {\n      setLoading(true);\n      const friendList = await FriendService.getFriends();\n      setFriends(friendList);\n    } catch (error) {\n      console.error('获取好友列表失败:', error);\n      message.error('获取好友列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchFriends();\n  }, []);\n\n  /**\n   * 添加好友成功的回调处理\n   *\n   * 执行步骤：\n   * 1. 显示成功提示消息\n   * 2. 刷新好友列表数据\n   * 3. 自动切换到好友列表标签页，方便用户查看新添加的好友\n   */\n  const handleAddFriendSuccess = () => {\n    message.success('好友添加成功');\n    fetchFriends(); // 刷新好友列表\n    setActiveTab('list'); // 切换到好友列表\n  };\n\n  /**\n   * 删除好友成功的回调处理\n   *\n   * 执行步骤：\n   * 1. 显示成功提示消息\n   * 2. 刷新好友列表数据，移除已删除的好友\n   */\n  const handleRemoveFriendSuccess = () => {\n    message.success('好友删除成功');\n    fetchFriends(); // 刷新好友列表\n  };\n\n  const tabItems = [\n    {\n      key: 'list',\n      label: (\n        <Space>\n          <UnorderedListOutlined />\n          我的好友 ({friends.length})\n        </Space>\n      ),\n      children: (\n        <FriendList \n          friends={friends}\n          loading={loading}\n          onRemoveFriend={handleRemoveFriendSuccess}\n          onRefresh={fetchFriends}\n        />\n      )\n    },\n    {\n      key: 'add',\n      label: (\n        <Space>\n          <UserAddOutlined />\n          添加好友\n        </Space>\n      ),\n      children: (\n        <AddFriend\n          onAddSuccess={handleAddFriendSuccess}\n        />\n      )\n    },\n    {\n      key: 'requests',\n      label: (\n        <Space>\n          <InboxOutlined />\n          好友请求\n        </Space>\n      ),\n      children: (\n        <FriendRequests\n          onRequestHandled={fetchFriends}\n        />\n      )\n    }\n  ];\n\n  return (\n    <PageContainer\n      title=\"好友管理\"\n      subTitle=\"管理您的好友关系，邀请好友加入团队\"\n      extra={[\n        <Button \n          key=\"refresh\" \n          onClick={fetchFriends}\n          loading={loading}\n        >\n          刷新\n        </Button>\n      ]}\n    >\n      <Card>\n        <Tabs\n          activeKey={activeTab}\n          onChange={setActiveTab}\n          items={tabItems}\n          size=\"large\"\n        />\n      </Card>\n    </PageContainer>\n  );\n};\n\nexport default FriendManagePage;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BAuPD;;;eAAA;;;;;;wEArPgC;6BAazB;8BAMA;iCACuB;;;;;;;;;;AAG9B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,gBAAU;AAM7C,MAAM,YAAsC,CAAC,EAAE,YAAY,EAAE;;IAC3D,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,eAAQ,EAAgB;IACpD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAY,EAAE;IAChE,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAC;IAEnD;;;;;;;;;;;;GAYC,GACD,MAAM,eAAe,OAAO;QAC1B,MAAM,EAAE,KAAK,EAAE,GAAG;QAClB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,aAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,IAAI;YACF,aAAa;YACb,iBAAiB;YACjB,MAAM,QAAQ,MAAM,uBAAa,CAAC,WAAW,CAAC,MAAM,IAAI;YACxD,iBAAiB;YAEjB,IAAI,MAAM,MAAM,KAAK,GACnB,aAAO,CAAC,IAAI,CAAC;QAEjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,aAAa;QACf;IACF;IAEA;;;;;;;;;;;;GAYC,GACD,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,UAAU,KAAK,EAAE;YACjB,MAAM,UAA4B;gBAChC,OAAO,KAAK,KAAK;YACnB;YACA,MAAM,uBAAa,CAAC,iBAAiB,CAAC;YAEtC,0BAA0B;YAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;YAC1D,aAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC;YAC1C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,UAAU;QACZ;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB;QACxB,iBAAiB,EAAE;QACnB,iBAAiB;QACjB,KAAK,WAAW;IAClB;IAEA,qBACE,2BAAC;;0BAEC,2BAAC,UAAI;gBAAC,OAAM;gBAAO,OAAO;oBAAE,cAAc;gBAAG;;kCAC3C,2BAAC,UAAI;wBACH,MAAM;wBACN,QAAO;wBACP,UAAU;wBACV,OAAO;4BAAE,OAAO;wBAAO;;0CAEvB,2BAAC,UAAI,CAAC,IAAI;gCACR,MAAK;gCACL,OAAO;oCACL;wCAAE,UAAU;wCAAM,SAAS;oCAAW;oCACtC;wCAAE,MAAM;wCAAS,SAAS;oCAAa;iCACxC;gCACD,OAAO;oCAAE,MAAM;oCAAG,aAAa;gCAAG;0CAElC,cAAA,2BAAC,WAAK;oCACJ,sBAAQ,2BAAC,mBAAY;;;;;oCACrB,aAAY;oCACZ,MAAK;oCACL,UAAU;;;;;;;;;;;0CAGd,2BAAC,UAAI,CAAC,IAAI;0CACR,cAAA,2BAAC,WAAK;;sDACJ,2BAAC,YAAM;4CACL,MAAK;4CACL,UAAS;4CACT,oBAAM,2BAAC,qBAAc;;;;;4CACrB,SAAS;4CACT,MAAK;sDACN;;;;;;wCAGA,cAAc,MAAM,GAAG,mBACtB,2BAAC,YAAM;4CACL,SAAS;4CACT,MAAK;sDACN;;;;;;;;;;;;;;;;;;;;;;;kCAQT,2BAAC,aAAO;;;;;kCAER,2BAAC;wBAAU,MAAK;;0CACd,2BAAC;gCAAK,MAAM;0CAAC;;;;;;0CACb,2BAAC;;;;;4BAAK;0CAEN,2BAAC;;;;;4BAAK;0CAEN,2BAAC;;;;;4BAAK;;;;;;;;;;;;;YAMT,+BACC,2BAAC,UAAI;gBACH,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;gBAClC,qBACE,2BAAC;oBAAK,MAAK;;wBAAY;wBACjB,cAAc,MAAM;wBAAC;;;;;;;0BAI5B,cAAc,MAAM,KAAK,kBACxB,2BAAC,WAAK;oBACJ,OAAO,WAAK,CAAC,sBAAsB;oBACnC,aAAY;oBACZ,OAAO;wBAAE,SAAS;oBAAS;8BAE3B,cAAA,2BAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;yCAKzB,2BAAC,UAAI;oBACH,YAAY;oBACZ,YAAY,CAAC,qBACX,2BAAC,UAAI,CAAC,IAAI;4BACR,SAAS;8CACP,2BAAC,YAAM;oCAEL,MAAK;oCACL,oBAAM,2BAAC,sBAAe;;;;;oCACtB,SAAS,WAAW,KAAK,EAAE;oCAC3B,SAAS,IAAM,gBAAgB;8CAChC;mCALK;;;;;6BAQP;sCAED,cAAA,2BAAC,UAAI,CAAC,IAAI,CAAC,IAAI;gCACb,sBACE,2BAAC,YAAM;oCACL,MAAM;oCACN,oBAAM,2BAAC,mBAAY;;;;;oCACnB,OAAO;wCAAE,iBAAiB;oCAAU;8CAEnC,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;gCAGpC,qBACE,2BAAC,WAAK;8CACJ,cAAA,2BAAC;wCAAK,MAAM;kDAAE,KAAK,IAAI;;;;;;;;;;;gCAG3B,2BACE,2BAAC,WAAK;oCAAC,WAAU;oCAAW,MAAM;;sDAChC,2BAAC;4CAAK,MAAK;sDAAa,KAAK,KAAK;;;;;;sDAClC,2BAAC;4CAAK,MAAK;4CAAY,OAAO;gDAAE,UAAU;4CAAG;;gDAAG;gDACvC,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa5E;GAtNM;;QACW,UAAI,CAAC;;;KADhB;IAwNN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzPf;;CAEC;;;;4BA8RD;;;eAAA;;;;;;wEA5RgC;6BAczB;8BAOA;iCACuB;;;;;;;;;;AAG9B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,WAAK;AASxB,MAAM,aAAwC,CAAC,EAC7C,OAAO,EACP,OAAO,EACP,cAAc,EACd,SAAS,EACV;;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAC;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAgB;IACxD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAA0B;IAC5E,MAAM,CAAC,WAAW,GAAG,UAAI,CAAC,OAAO;IAEjC;;;;;;GAMC,GACD,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,SACrC,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,cAAc,WAAW,OAC5D,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,cAAc,WAAW;IAG/D;;;;;;;;;;;GAWC,GACD,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,YAAY,OAAO,EAAE;YACrB,MAAM,uBAAa,CAAC,YAAY,CAAC,OAAO,EAAE;YAC1C,aAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,YAAY;QACd;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,CAAC;QACxB,iBAAiB;QACjB,sBAAsB;QACtB,iBAAiB;QACjB,WAAW,cAAc,CAAC;YAAE,QAAQ,OAAO,MAAM,IAAI;QAAG;IAC1D;IAEA;;GAEC,GACD,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,eAAe;QAEpB,IAAI;YACF,MAAM,uBAAa,CAAC,eAAe,CAAC;gBAClC,UAAU,cAAc,EAAE;gBAC1B,QAAQ,OAAO,MAAM;YACvB;YACA,aAAO,CAAC,OAAO,CAAC;YAChB,sBAAsB;YACtB,iBAAiB;YACjB,WAAW,WAAW;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,IAAI,QAAQ,MAAM,KAAK,KAAK,CAAC,SAC3B,qBACE,2BAAC,WAAK;QACJ,OAAO,WAAK,CAAC,sBAAsB;QACnC,aAAY;QACZ,OAAO;YAAE,SAAS;QAAS;kBAE3B,cAAA,2BAAC;YAAK,MAAK;sBAAY;;;;;;;;;;;IAO7B,qBACE,2BAAC;;0BAEC,2BAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAG;0BAC7B,cAAA,2BAAC;oBACC,aAAY;oBACZ,UAAU;oBACV,2BAAa,2BAAC,qBAAc;;;;;oBAC5B,MAAK;oBACL,OAAO;oBACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oBAChD,OAAO;wBAAE,UAAU;oBAAI;;;;;;;;;;;0BAK3B,2BAAC,UAAI;gBACH,SAAS;gBACT,YAAY;gBACZ,YAAY,CAAC,uBACX,2BAAC,UAAI,CAAC,IAAI;wBACR,SAAS;0CACP,2BAAC,aAAO;gCAAc,OAAM;0CAC1B,cAAA,2BAAC,YAAM;oCACL,MAAK;oCACL,oBAAM,2BAAC,mBAAY;;;;;oCACnB,SAAS,IAAM,iBAAiB;oCAChC,MAAK;8CACN;;;;;;+BANU;;;;;0CAUb,2BAAC,gBAAU;gCAET,OAAM;gCACN,aAAa,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC;gCAC1C,oBAAM,2BAAC,gCAAyB;oCAAC,OAAO;wCAAE,OAAO;oCAAM;;;;;;gCACvD,WAAW,IAAM,mBAAmB;gCACpC,QAAO;gCACP,YAAW;gCACX,QAAO;0CAEP,cAAA,2BAAC,YAAM;oCACL,MAAK;oCACL,MAAM;oCACN,oBAAM,2BAAC,qBAAc;;;;;oCACrB,SAAS,aAAa,OAAO,EAAE;oCAC/B,MAAK;8CACN;;;;;;+BAfG;;;;;yBAmBP;kCAED,cAAA,2BAAC,UAAI,CAAC,IAAI,CAAC,IAAI;4BACb,sBACE,2BAAC,YAAM;gCACL,MAAM;gCACN,oBAAM,2BAAC,mBAAY;;;;;gCACnB,OAAO;oCAAE,iBAAiB;gCAAU;0CAEnC,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;4BAGtC,qBACE,2BAAC,WAAK;0CACJ,cAAA,2BAAC;oCAAK,MAAM;8CAAE,OAAO,IAAI;;;;;;;;;;;4BAG7B,2BACE,2BAAC,WAAK;gCAAC,WAAU;gCAAW,MAAM;;kDAChC,2BAAC;wCAAK,MAAK;kDAAa,OAAO,KAAK;;;;;;oCACnC,OAAO,MAAM,kBACZ,2BAAC;wCAAK,MAAK;wCAAY,OAAO;4CAAE,UAAU;4CAAI,WAAW;wCAAS;;4CAAG;4CAC9D,OAAO,MAAM;;;;;;;kDAGtB,2BAAC;wCAAK,MAAK;wCAAY,OAAO;4CAAE,UAAU;wCAAG;;4CAAG;4CACvC,IAAI,KAAK,OAAO,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;gBAOhE,YAAY;oBACV,UAAU;oBACV,iBAAiB;oBACjB,iBAAiB;oBACjB,WAAW,CAAC,OAAO,QACjB,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC;gBAChD;;;;;;YAGD,gBAAgB,MAAM,KAAK,KAAK,+BAC/B,2BAAC,WAAK;gBACJ,OAAO,WAAK,CAAC,sBAAsB;gBACnC,aAAa,CAAC,QAAQ,EAAE,cAAc,KAAK,CAAC;gBAC5C,OAAO;oBAAE,SAAS;gBAAS;;;;;;0BAK/B,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU;oBACR,sBAAsB;oBACtB,iBAAiB;oBACjB,WAAW,WAAW;gBACxB;gBACA,QAAQ;gBACR,OAAO;0BAEP,cAAA,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;sCAEV,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,KAAK;oCAAI,SAAS;gCAAgB;6BACrC;sCAED,cAAA,2BAAC,WAAK,CAAC,QAAQ;gCACb,aAAY;gCACZ,MAAM;gCACN,WAAW;gCACX,SAAS;;;;;;;;;;;sCAGb,2BAAC,UAAI,CAAC,IAAI;4BAAC,OAAO;gCAAE,cAAc;gCAAG,WAAW;4BAAQ;sCACtD,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCAAC,SAAS;4CACf,sBAAsB;4CACtB,iBAAiB;4CACjB,WAAW,WAAW;wCACxB;kDAAG;;;;;;kDAGH,2BAAC,YAAM;wCAAC,MAAK;wCAAU,UAAS;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvD;GAvPM;;QAUiB,UAAI,CAAC;;;KAVtB;IAyPN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AChSf;;CAEC;;;;4BA6HD;;;eAAA;;;;;;wEA3H2C;6BAWpC;8BAIA;iCACuB;;;;;;;;;;AAG9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,gBAAU;AAiBlC,MAAM,iBAAgD,CAAC,EAAE,gBAAgB,EAAE;;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAkB,EAAE;IAEpE,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,OAAO,MAAM,uBAAa,CAAC,qBAAqB;YACtD,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,oCAAoC;IAEpC,IAAI,aAAa,MAAM,KAAK,KAAK,CAAC,SAChC,qBACE,2BAAC,UAAI;kBACH,cAAA,2BAAC,WAAK;YACJ,OAAO,WAAK,CAAC,sBAAsB;YACnC,aAAY;YACZ,OAAO;gBAAE,SAAS;YAAS;;;;;;;;;;;IAMnC,qBACE,2BAAC,UAAI;QAAC,OAAM;QAAU,qBACpB,2BAAC,YAAM;YAAC,SAAS;YAAe,SAAS;sBAAS;;;;;;kBAIlD,cAAA,2BAAC,UAAI;YACH,SAAS;YACT,YAAY;YACZ,YAAY,CAAC;oBASF,uBAAA,kBAKa,mBAQU;qCArBhC,2BAAC,UAAI,CAAC,IAAI;8BACR,cAAA,2BAAC,UAAI,CAAC,IAAI,CAAC,IAAI;wBACb,sBACE,2BAAC,YAAM;4BACL,MAAM;4BACN,oBAAM,2BAAC,mBAAY;;;;;4BACnB,OAAO;gCAAE,iBAAiB;4BAAU;uCAEnC,mBAAA,QAAQ,OAAO,cAAf,wCAAA,wBAAA,iBAAiB,IAAI,cAArB,4CAAA,sBAAuB,MAAM,CAAC,GAAG,WAAW;;;;;;wBAGjD,qBACE,2BAAC,WAAK;;8CACJ,2BAAC;oCAAK,MAAM;8CAAE,EAAA,oBAAA,QAAQ,OAAO,cAAf,wCAAA,kBAAiB,IAAI,KAAI;;;;;;8CACvC,2BAAC;oCAAK,MAAK;oCAAY,OAAO;wCAAE,UAAU;oCAAG;8CAAG;;;;;;;;;;;;wBAKpD,2BACE,2BAAC,WAAK;4BAAC,WAAU;4BAAW,MAAM;;8CAChC,2BAAC;oCAAK,MAAK;+CAAa,oBAAA,QAAQ,OAAO,cAAf,wCAAA,kBAAiB,KAAK;;;;;;8CAC9C,2BAAC;oCAAK,MAAK;oCAAY,OAAO;wCAAE,UAAU;oCAAG;;wCAAG;wCACvC,IAAI,KAAK,QAAQ,WAAW,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;;YAO/D,YAAY;gBACV,UAAU;gBACV,iBAAiB;gBACjB,iBAAiB;gBACjB,WAAW,CAAC,OAAO,QACjB,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC;YAChD;;;;;;;;;;;AAIR;GArFM;KAAA;IAuFN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/Hf;;CAEC;;;;4BA8JD;;;eAAA;;;;;;;wEA5J2C;6BAQpC;8BAMA;sCACuB;iCACA;4EAIP;2EACD;gFACK;;;;;;;;;;AAE3B,MAAM,EAAE,KAAK,EAAE,GAAG,gBAAU;AAE5B,MAAM,mBAA6B;;IACjC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAqB,EAAE;IAE7D;;;;;;;GAOC,GACD,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,aAAa,MAAM,uBAAa,CAAC,UAAU;YACjD,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL;;;;;;;GAOC,GACD,MAAM,yBAAyB;QAC7B,aAAO,CAAC,OAAO,CAAC;QAChB,gBAAgB,SAAS;QACzB,aAAa,SAAS,UAAU;IAClC;IAEA;;;;;;GAMC,GACD,MAAM,4BAA4B;QAChC,aAAO,CAAC,OAAO,CAAC;QAChB,gBAAgB,SAAS;IAC3B;IAEA,MAAM,WAAW;QACf;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,4BAAqB;;;;;oBAAG;oBAClB,QAAQ,MAAM;oBAAC;;;;;;;YAG1B,wBACE,2BAAC,mBAAU;gBACT,SAAS;gBACT,SAAS;gBACT,gBAAgB;gBAChB,WAAW;;;;;;QAGjB;QACA;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,sBAAe;;;;;oBAAG;;;;;;;YAIvB,wBACE,2BAAC,kBAAS;gBACR,cAAc;;;;;;QAGpB;QACA;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,oBAAa;;;;;oBAAG;;;;;;;YAIrB,wBACE,2BAAC,uBAAc;gBACb,kBAAkB;;;;;;QAGxB;KACD;IAED,qBACE,2BAAC,4BAAa;QACZ,OAAM;QACN,UAAS;QACT,OAAO;0BACL,2BAAC,YAAM;gBAEL,SAAS;gBACT,SAAS;0BACV;eAHK;;;;;SAMP;kBAED,cAAA,2BAAC,UAAI;sBACH,cAAA,2BAAC,UAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,MAAK;;;;;;;;;;;;;;;;AAKf;GAhIM;KAAA;IAkIN,WAAe"}