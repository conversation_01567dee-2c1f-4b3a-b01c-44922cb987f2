/**
 * 团队管理内容组件 - 用于个人中心
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  message,
  Modal,
  Form,
  Input,
  List,
  Avatar,
  Tag,
  Empty
} from 'antd';
import {
  TeamOutlined,
  PlusOutlined,
  UserOutlined,
  CrownOutlined
} from '@ant-design/icons';
import { TeamService } from '@/services';
import type { TeamDetailResponse, CreateTeamRequest } from '@/types/api';
import TeamDetailContent from '@/pages/team/detail/components/TeamDetailContent';

const { TextArea } = Input;

const TeamManageContent: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState<TeamDetailResponse | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'detail'>('list');
  const [createForm] = Form.useForm();

  useEffect(() => {
    fetchTeams();
  }, []);

  /**
   * 获取用户的团队列表
   */
  const fetchTeams = async () => {
    try {
      setLoading(true);
      const teamList = await TeamService.getUserTeams();
      setTeams(teamList);
    } catch (error) {
      console.error('获取团队列表失败:', error);
      message.error('获取团队列表失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 创建团队
   */
  const handleCreateTeam = async (values: CreateTeamRequest) => {
    try {
      await TeamService.createTeam(values);
      message.success('团队创建成功');
      setCreateModalVisible(false);
      createForm.resetFields();
      fetchTeams();
    } catch (error) {
      console.error('创建团队失败:', error);
      message.error('创建团队失败');
    }
  };



  /**
   * 查看团队详情
   */
  const handleViewTeamDetail = (team: TeamDetailResponse) => {
    setSelectedTeam(team);
    setViewMode('detail');
  };

  /**
   * 返回团队列表
   */
  const handleBackToList = () => {
    setSelectedTeam(null);
    setViewMode('list');
  };



  // 如果是详情模式，显示团队详情
  if (viewMode === 'detail' && selectedTeam) {
    return (
      <TeamDetailContent
        teamDetail={selectedTeam}
        loading={false}
        showBackButton={true}
        onBack={handleBackToList}
        onRefresh={() => {
          fetchTeams();
          // 刷新团队列表数据
        }}
      />
    );
  }

  // 默认显示团队列表
  return (
    <Card
      title={
        <Space>
          <TeamOutlined />
          我的团队
        </Space>
      }
      extra={
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            创建团队
          </Button>
          <Button
            onClick={fetchTeams}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      }
    >
      {teams.length === 0 && !loading ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="您还没有创建或加入任何团队"
        >
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            创建第一个团队
          </Button>
        </Empty>
      ) : (
        <List
          loading={loading}
          itemLayout="horizontal"
          dataSource={teams}
          renderItem={(team) => (
            <List.Item
              actions={[
                <Button
                  key="view"
                  type="primary"
                  size="small"
                  onClick={() => handleViewTeamDetail(team)}
                >
                  查看详情
                </Button>
              ]}
            >
              <List.Item.Meta
                avatar={
                  <Avatar 
                    icon={<TeamOutlined />} 
                    style={{ backgroundColor: '#1890ff' }}
                  />
                }
                title={
                  <Space>
                    {team.name}
                    {team.isCreator && (
                      <Tag color="gold" icon={<CrownOutlined />}>
                        创建者
                      </Tag>
                    )}
                  </Space>
                }
                description={
                  <div>
                    <div>{team.description || '暂无描述'}</div>
                    <div style={{ marginTop: 4, color: '#666' }}>
                      <Space>
                        <span>
                          <UserOutlined /> {team.memberCount} 名成员
                        </span>
                        <span>创建于 {new Date(team.createdAt).toLocaleDateString()}</span>
                      </Space>
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      )}

      {/* 创建团队弹窗 */}
      <Modal
        title="创建团队"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          createForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateTeam}
        >
          <Form.Item
            label="团队名称"
            name="name"
            rules={[
              { required: true, message: '请输入团队名称' },
              { max: 100, message: '团队名称不能超过100个字符' }
            ]}
          >
            <Input placeholder="请输入团队名称" />
          </Form.Item>

          <Form.Item
            label="团队描述"
            name="description"
            rules={[
              { max: 500, message: '团队描述不能超过500个字符' }
            ]}
          >
            <TextArea 
              rows={4} 
              placeholder="请输入团队描述（可选）" 
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                创建团队
              </Button>
              <Button onClick={() => {
                setCreateModalVisible(false);
                createForm.resetFields();
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>


    </Card>
  );
};

export default TeamManageContent;
