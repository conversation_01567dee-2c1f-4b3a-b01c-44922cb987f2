/**
 * 团队详情组件 - 增强模式显示
 */

import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Space,
  Typography,
  Avatar,
  Tag,
  Progress,
  Badge,
  message,
  Modal,
  Form,
  Input,
  Dropdown,
  Spin,
  Empty
} from 'antd';
import {
  TeamOutlined,
  UserOutlined,
  CalendarOutlined,
  EditOutlined,
  CrownOutlined,
  ClockCircleOutlined,
  SettingOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons';
import { TeamService } from '@/services';
import type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';
import { history, useModel } from '@umijs/max';
import TeamMemberList from './TeamMemberList';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

interface TeamDetailContentProps {
  teamDetail: TeamDetailResponse | null;
  loading: boolean;
  onRefresh: () => void;
  /** 是否显示返回按钮 */
  showBackButton?: boolean;
  /** 返回按钮点击回调 */
  onBack?: () => void;
}

const TeamDetailContent: React.FC<TeamDetailContentProps> = ({
  teamDetail,
  loading,
  onRefresh,
  showBackButton = false,
  onBack
}) => {
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [form] = Form.useForm();
  const { setInitialState } = useModel('@@initialState');

  // 辅助函数
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getTeamStatusColor = () => {
    if (!teamDetail) return '#1890ff';
    const memberCount = teamDetail.memberCount;
    if (memberCount >= 10) return '#52c41a'; // 绿色 - 活跃
    if (memberCount >= 5) return '#faad14'; // 橙色 - 正常
    return '#1890ff'; // 蓝色 - 小团队
  };

  const getTeamStatusText = () => {
    if (!teamDetail) return '小型团队';
    const memberCount = teamDetail.memberCount;
    if (memberCount >= 10) return '活跃团队';
    if (memberCount >= 5) return '正常团队';
    return '小型团队';
  };

  const handleGoBack = () => {
    if (onBack) {
      onBack();
    } else {
      history.push('/user/team-select');
    }
  };

  /**
   * 处理编辑团队信息操作
   */
  const handleEdit = () => {
    if (!teamDetail) return;
    form.setFieldsValue({
      name: teamDetail.name,
      description: teamDetail.description || '',
    });
    setEditModalVisible(true);
  };

  /**
   * 处理团队信息更新操作
   */
  const handleUpdateTeam = async (values: UpdateTeamRequest) => {
    if (!teamDetail) return;

    try {
      setUpdating(true);
      await TeamService.updateCurrentTeam(values);
      message.success('团队信息更新成功');
      setEditModalVisible(false);
      onRefresh();
    } catch (error) {
      console.error('更新团队失败:', error);
      message.error('更新团队失败');
    } finally {
      setUpdating(false);
    }
  };

  /**
   * 处理删除团队操作
   */
  const handleDeleteTeam = () => {
    if (!teamDetail) return;

    Modal.confirm({
      title: '确认删除团队',
      content: `确定要删除团队 "${teamDetail.name}" 吗？此操作不可恢复。`,
      icon: <ExclamationCircleOutlined />,
      okText: '确认删除',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          setDeleting(true);
          await TeamService.deleteCurrentTeam();
          message.success('团队删除成功');
          // 更新全局状态，清除当前团队
          setInitialState((s) => ({ ...s, currentTeam: undefined }));
          history.push('/user/team-select');
        } catch (error) {
          console.error('删除团队失败:', error);
          message.error('删除团队失败');
        } finally {
          setDeleting(false);
        }
      },
    });
  };

  // 创建下拉菜单项（增强模式使用）
  const createMenuItems = () => [
    {
      key: 'edit',
      icon: <EditOutlined />,
      label: '编辑团队',
      onClick: handleEdit,
    },
    {
      key: 'delete',
      icon: <DeleteOutlined />,
      label: '删除团队',
      danger: true,
      onClick: handleDeleteTeam,
    },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px 0' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!teamDetail) {
    return (
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="请先选择一个团队"
      />
    );
  }

  // 增强模式渲染
  return (
    <div style={{ padding: '0 24px' }}>
      {/* 团队头部信息卡片 */}
      <Card
        style={{
          marginBottom: 24,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          border: 'none',
          borderRadius: 16
        }}
        styles={{ body: { padding: '32px' } }}
      >
        <Row align="middle" justify="space-between">
          {/* 左列：返回按钮 */}
          {showBackButton && (
            <Col>
              <Button
                type="text"
                icon={<ArrowLeftOutlined />}
                onClick={handleGoBack}
                style={{
                  color: 'rgba(255, 255, 255, 0.8)',
                  fontSize: 16,
                  padding: '4px 8px'
                }}
              >
                返回
              </Button>
            </Col>
          )}

          {/* 中间列：团队名称显示 */}
          <Col
            flex="auto"
            style={{
              display: 'flex',
              justifyContent: 'center',
              maxWidth: '60%'
            }}
          >
              <Space size="large" align="center">
                <Avatar
                  size={65}
                  icon={<TeamOutlined />}
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    color: 'white',
                    fontSize: 28
                  }}
                />
                <div>
                  <Space align="center" style={{ marginBottom: 8 }}>
                    <Title level={2} style={{ color: 'white', margin: 0 }}>
                      {teamDetail.name}
                    </Title>
                    {teamDetail.isCreator && (
                      <Tag
                        icon={<CrownOutlined />}
                        color="gold"
                        style={{ fontSize: 12 }}
                      >
                        管理员
                      </Tag>
                    )}
                    <Badge
                      color={getTeamStatusColor()}
                      text={
                        <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                          {getTeamStatusText()}
                        </Text>
                      }
                    />
                  </Space>
                  <Paragraph
                    style={{
                      color: 'rgba(255, 255, 255, 0.8)',
                      margin: 0,
                      textAlign: 'center'
                    }}
                    ellipsis={{ rows: 2 }}
                  >
                    {teamDetail.description || '这个团队还没有描述'}
                  </Paragraph>
                </div>
              </Space>
            </Col>

            {/* 右列：团队操作菜单 */}
            <Col>
              {teamDetail.isCreator && (
                <Dropdown
                  menu={{ items: createMenuItems() }}
                  trigger={['click']}
                  placement="bottomRight"
                >
                  <Button
                    type="text"
                    icon={<SettingOutlined />}
                    style={{
                      color: 'rgba(255, 255, 255, 0.8)',
                      fontSize: 20,
                      width: 50,
                      height: 50
                    }}
                  />
                </Dropdown>
              )}
            </Col>
          </Row>
        </Card>

        {/* 团队统计信息 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="团队成员"
                value={teamDetail.memberCount}
                suffix="人"
                prefix={<UserOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="创建时间"
                value={formatDate(teamDetail.createdAt)}
                prefix={<CalendarOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a', fontSize: 16 }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="最后活动"
                value={formatDate(teamDetail.updatedAt)}
                prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ color: '#faad14', fontSize: 16 }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <Text type="secondary" style={{ fontSize: 14 }}>团队活跃度</Text>
                <div style={{ marginTop: 8 }}>
                  <Progress
                    type="circle"
                    size={60}
                    percent={Math.min(teamDetail.memberCount * 10, 100)}
                    strokeColor={getTeamStatusColor()}
                    format={() => (
                      <Text style={{ fontSize: 12, color: getTeamStatusColor() }}>
                        {teamDetail.memberCount >= 10 ? '高' :
                         teamDetail.memberCount >= 5 ? '中' : '低'}
                      </Text>
                    )}
                  />
                </div>
              </div>
            </Card>
          </Col>
        </Row>

        {/* 团队成员列表 */}
        <TeamMemberList
          teamId={teamDetail.id}
          isCreator={teamDetail.isCreator}
          onMemberChange={onRefresh}
        />

        {/* 编辑团队模态框 */}
        <Modal
          title="编辑团队信息"
          open={editModalVisible}
          onCancel={() => setEditModalVisible(false)}
          footer={null}
          width={600}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleUpdateTeam}
          >
            <Form.Item
              label="团队名称"
              name="name"
              rules={[
                { required: true, message: '请输入团队名称' },
                { max: 50, message: '团队名称不能超过50个字符' }
              ]}
            >
              <Input placeholder="请输入团队名称" />
            </Form.Item>
            <Form.Item
              label="团队描述"
              name="description"
              rules={[
                { max: 200, message: '团队描述不能超过200个字符' }
              ]}
            >
              <TextArea
                rows={4}
                placeholder="请输入团队描述（可选）"
                showCount
                maxLength={200}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
              <Space>
                <Button onClick={() => setEditModalVisible(false)}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit" loading={updating}>
                  保存
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    );
};

export default TeamDetailContent;
