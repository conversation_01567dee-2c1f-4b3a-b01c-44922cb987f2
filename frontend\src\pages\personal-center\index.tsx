/**
 * 个人中心页面 - 整合个人资料、团队管理、订阅管理
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Typography,
  Space,
  message
} from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  CrownOutlined,
  UsergroupAddOutlined
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { SubscriptionService, TeamService } from '@/services';
import type { SubscriptionResponse, TeamDetailResponse } from '@/types/api';

// 导入各个模块的内容组件
import UserProfileContent from '../user/components/UserProfileContent';
import UnifiedSubscriptionContent from '../subscription/components/UnifiedSubscriptionContent';
import TeamManageContent from './components/TeamManageContent';
import FriendManageContent from './components/FriendManageContent';

const { Title } = Typography;

const PersonalCenterPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('profile');
  const [currentSubscription, setCurrentSubscription] = useState<SubscriptionResponse | null>(null);
  const [subscriptionLoading, setSubscriptionLoading] = useState(false);
  const [currentTeamDetail, setCurrentTeamDetail] = useState<TeamDetailResponse | null>(null);
  const [teamLoading, setTeamLoading] = useState(false);

  const { initialState } = useModel('@@initialState');
  const currentTeam = initialState?.currentTeam;

  // 获取当前订阅信息
  const fetchCurrentSubscription = async () => {
    try {
      setSubscriptionLoading(true);
      const subscription = await SubscriptionService.getCurrentSubscription();
      setCurrentSubscription(subscription);
    } catch (error) {
      console.error('获取当前订阅失败:', error);
    } finally {
      setSubscriptionLoading(false);
    }
  };

  // 获取当前团队详情
  const fetchCurrentTeamDetail = async () => {
    if (!currentTeam?.id) return;

    try {
      setTeamLoading(true);
      const teamDetail = await TeamService.getTeamDetail(currentTeam.id);
      setCurrentTeamDetail(teamDetail);
    } catch (error) {
      console.error('获取团队详情失败:', error);
    } finally {
      setTeamLoading(false);
    }
  };

  // 处理订阅变化
  const handleSubscriptionChange = () => {
    fetchCurrentSubscription();
  };

  // 根据当前选中的标签页加载对应数据
  React.useEffect(() => {
    if (activeTab.startsWith('subscription')) {
      fetchCurrentSubscription();
    } else if (activeTab === 'team-detail') {
      fetchCurrentTeamDetail();
    }
  }, [activeTab, currentTeam?.id]);

  const tabItems = [
    {
      key: 'profile',
      label: (
        <Space>
          <UserOutlined />
          个人资料
        </Space>
      ),
      children: <UserProfileContent />
    },
    {
      key: 'teams',
      label: (
        <Space>
          <TeamOutlined />
          我的团队
        </Space>
      ),
      children: <TeamManageContent />
    },
    {
      key: 'friends',
      label: (
        <Space>
          <UsergroupAddOutlined />
          好友管理
        </Space>
      ),
      children: <FriendManageContent />
    },
    {
      key: 'subscription',
      label: (
        <Space>
          <CrownOutlined />
          订阅管理
        </Space>
      ),
      children: (
        <UnifiedSubscriptionContent
          currentSubscription={currentSubscription}
          loading={subscriptionLoading}
          onRefresh={fetchCurrentSubscription}
        />
      )
    }
  ];

  return (
    <PageContainer title="个人中心">
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
          tabPosition="left"
        />
      </Card>
    </PageContainer>
  );
};

export default PersonalCenterPage;
