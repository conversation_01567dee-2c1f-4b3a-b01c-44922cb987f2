import React, { useState } from "react";
import {
  But<PERSON>,
  Card,
  Divider,
  Dropdown,
  Flex,
  Form,
  Input,
  List,
  Modal,
  Progress,
  Select,
  Space,
  Tabs,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import {
  CheckOutlined,
  DeleteOutlined,
  EditOutlined,
  MoreOutlined,
  PlusOutlined,
  SearchOutlined,
  CalendarOutlined,
} from "@ant-design/icons";

const { Text } = Typography;
const { TabPane } = Tabs;

export interface TodoItem {
  id: string;
  name: string;
  createDate: string;
  completed: boolean;
  priority: "high" | "medium" | "low";
}

interface TodoManagementProps {
  onAddTodo?: (todo: TodoItem) => void;
  onUpdateTodo?: (id: string, updatedTodo: Partial<TodoItem>) => void;
  onDeleteTodo?: (id: string) => void;
}

const TodoManagement: React.FC<TodoManagementProps> = (props) => {
  // 初始待办事项数据
  const [personalTasks, setPersonalTasks] = useState<TodoItem[]>([
    {
      id: "1",
      name: "完善个人资料信息",
      createDate: "2024/1/10",
      completed: true,
      priority: "low",
    },
    {
      id: "2",
      name: "驾驶证信息完善",
      createDate: "2024/1/12",
      completed: true,
      priority: "medium",
    },
    {
      id: "3",
      name: "设置提醒收藏",
      createDate: "2024/1/15",
      completed: true,
      priority: "low",
    },
    {
      id: "4",
      name: "车辆保养记录更新",
      createDate: "2024/1/18",
      completed: false,
      priority: "medium",
    },
    {
      id: "5",
      name: "团队会议准备",
      createDate: "2024/1/20",
      completed: false,
      priority: "high",
    },
    {
      id: "6",
      name: "车辆安全检查报告",
      createDate: "2024/1/22",
      completed: false,
      priority: "high",
    },
  ]);

  // 待办事项状态管理
  const [todoModalVisible, setTodoModalVisible] = useState(false);
  const [todoForm] = Form.useForm();
  const [editingTodoId, setEditingTodoId] = useState<string | null>(null);

  // 过滤器状态
  const [activeTab, setActiveTab] = useState<"all" | "pending" | "completed">(
    "pending"
  );
  const [searchText, setSearchText] = useState("");

  // 根据激活的标签和搜索文本过滤任务
  const filteredPersonalTasks = personalTasks.filter((task) => {
    // 根据标签过滤
    if (activeTab === "pending" && task.completed) return false;
    if (activeTab === "completed" && !task.completed) return false;

    // 根据搜索文本过滤
    if (
      searchText &&
      !task.name.toLowerCase().includes(searchText.toLowerCase())
    ) {
      return false;
    }

    return true;
  });

  // 计算各优先级任务数量
  const highPriorityCount = personalTasks.filter(
    (t) => !t.completed && t.priority === "high"
  ).length;
  const mediumPriorityCount = personalTasks.filter(
    (t) => !t.completed && t.priority === "medium"
  ).length;
  const lowPriorityCount = personalTasks.filter(
    (t) => !t.completed && t.priority === "low"
  ).length;

  // 计算完成百分比
  const completionPercentage = Math.round(
    (personalTasks.filter((task) => task.completed).length /
      personalTasks.length) *
      100
  );

  // 处理待办事项操作
  const handleToggleTodoStatus = (id: string) => {
    setPersonalTasks(
      personalTasks.map((task) =>
        task.id === id ? { ...task, completed: !task.completed } : task
      )
    );
  };

  const handleAddOrUpdateTodo = (values: any) => {
    if (editingTodoId) {
      // 更新现有待办事项
      setPersonalTasks(
        personalTasks.map((task) =>
          task.id === editingTodoId
            ? { ...task, name: values.name, priority: values.priority }
            : task
        )
      );
    } else {
      // 添加新待办事项
      const newTodo: TodoItem = {
        id: `${Date.now()}`,
        name: values.name,
        createDate: new Date().toISOString().split("T")[0],
        completed: false,
        priority: values.priority,
      };
      setPersonalTasks([...personalTasks, newTodo]);
    }

    // 重置表单并关闭模态框
    setTodoModalVisible(false);
    setEditingTodoId(null);
    todoForm.resetFields();
  };

  const handleDeleteTodo = (id: string) => {
    setPersonalTasks(personalTasks.filter((task) => task.id !== id));
  };

  return (
    <Card
      className="dashboard-card"
      style={{
        borderRadius: 12,
        boxShadow: "0 4px 12px rgba(0,0,0,0.05)",
        border: "none",
        background: "linear-gradient(145deg, #ffffff, #f5f8ff)",
      }}
      title={
        <Flex justify="space-between" align="center">
          <Text strong>待办事项</Text>
        </Flex>
      }
    >
      {/* 第一行：搜索框、新增任务按钮、优先级状态点和进度条 */}
      <Flex
        justify="space-between"
        align="center"
        style={{ marginBottom: 16, gap: 16, flexWrap: "wrap" }}
      >
        <Space size={16} style={{ flex: 1, minWidth: 300 }}>
          {/* 搜索框 */}
          <Input.Search
            placeholder="搜索任务..."
            allowClear
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ flex: 1 }}
          />

          {/* 新增任务按钮 */}
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingTodoId(null);
              todoForm.resetFields();
              setTodoModalVisible(true);
            }}
            style={{
              background: "#1890ff",
              borderColor: "#1890ff",
              boxShadow: "0 2px 4px rgba(24, 144, 255, 0.3)",
              fontWeight: 500,
            }}
          >
            新增
          </Button>
        </Space>
        
        <Divider type="vertical" style={{ height: 20, backgroundColor: "#e8e8e8" }} />
        
        <Space align="center" size={16}>
          {/* 优先级状态点 */}
          <Space size={8}>
            <Tooltip title="高优先级任务">
              <Flex align="center">
                <div
                  style={{
                    width: 10,
                    height: 10,
                    borderRadius: "50%",
                    background: "#ff4d4f",
                    marginRight: 4,
                  }}
                />
                <Text style={{ fontSize: 13, fontWeight: 500 }}>
                  1000
                </Text>
              </Flex>
            </Tooltip>

            <Tooltip title="中优先级任务">
              <Flex align="center">
                <div
                  style={{
                    width: 10,
                    height: 10,
                    borderRadius: "50%",
                    background: "#faad14",
                    marginRight: 4,
                  }}
                />
                <Text style={{ fontSize: 13, fontWeight: 500 }}>
                  10000
                </Text>
              </Flex>
            </Tooltip>

            <Tooltip title="低优先级任务">
              <Flex align="center">
                <div
                  style={{
                    width: 10,
                    height: 10,
                    borderRadius: "50%",
                    background: "#52c41a",
                    marginRight: 4,
                  }}
                />
                <Text style={{ fontSize: 13, fontWeight: 500 }}>
                  10000
                </Text>
              </Flex>
            </Tooltip>
          </Space>

          <Divider type="vertical" style={{ height: 20, backgroundColor: "#e8e8e8" }} />

          {/* 进度条 */}
          <Tooltip title="任务完成进度">
            <Progress
              percent={completionPercentage}
              size="small"
              style={{ width: 120 }}
              strokeColor="#52c41a"
            />
          </Tooltip>
        </Space>
      </Flex>

      {/* 第二行：标签页 */}
      <Tabs
        activeKey={activeTab}
        onChange={(key) =>
          setActiveTab(key as "all" | "pending" | "completed")
        }
        size="middle"
        style={{ marginBottom: 8 }}
      >
        <TabPane tab="全部" key="all" />
        <TabPane tab="待处理" key="pending" />
        <TabPane tab="已完成" key="completed" />
      </Tabs>

      {/* 待办事项列表 */}
      <List
        dataSource={filteredPersonalTasks}
        renderItem={(item) => {
          return (
            <List.Item
              className="todo-item"
              style={{
                padding: "10px 16px",
                marginBottom: 12,
                borderRadius: 8,
                background: "#fff",
                opacity: item.completed ? 0.7 : 1,
                borderLeft: `3px solid ${
                  item.completed
                    ? "#52c41a"
                    : item.priority === "high"
                    ? "#ff4d4f"
                    : item.priority === "medium"
                    ? "#faad14"
                    : "#8c8c8c"
                }`,
                boxShadow: "0 1px 4px rgba(0,0,0,0.05)",
              }}
            >
              <Flex align="center" gap={12} style={{ width: "100%" }}>
                {/* 左侧状态和优先级指示器 */}
                <Flex vertical align="center">
                  {item.completed ? (
                    <Flex
                      align="center"
                      justify="center"
                      style={{
                        width: 22,
                        height: 22,
                        borderRadius: "50%",
                        background: "#52c41a",
                      }}
                    >
                      <CheckOutlined
                        style={{ color: "#fff", fontSize: 12 }}
                      />
                    </Flex>
                  ) : (
                    <div
                      style={{
                        width: 18,
                        height: 18,
                        borderRadius: "50%",
                        border: `2px solid ${
                          item.priority === "high"
                            ? "#ff4d4f"
                            : item.priority === "medium"
                            ? "#faad14"
                            : "#8c8c8c"
                        }`,
                      }}
                    />
                  )}

                  <div
                    style={{
                      width: 2,
                      height: 24,
                      background: "#f0f0f0",
                      marginTop: 4,
                    }}
                  />
                </Flex>

                {/* 任务信息区 */}
                <Flex vertical style={{ flex: 1 }}>
                  <Text
                    style={{
                      fontSize: 14,
                      fontWeight:
                        item.priority === "high" ? 500 : "normal",
                      textDecoration: item.completed
                        ? "line-through"
                        : "none",
                      color: item.completed ? "#8c8c8c" : "#262626",
                    }}
                  >
                    {item.name}
                  </Text>

                  {/* 显示创建日期 */}
                  <Space align="center" size={6} style={{ marginTop: 4 }}>
                    <CalendarOutlined
                      style={{
                        fontSize: 12,
                        color: "#8c8c8c",
                      }}
                    />
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      创建于: {item.createDate}
                    </Text>
                  </Space>
                </Flex>

                {/* 操作按钮区 */}
                <Dropdown
                  trigger={['click']}
                  menu={{
                    items: [
                      {
                        key: 'complete',
                        label: item.completed ? '标记未完成' : '标记完成',
                        icon: (
                          <CheckOutlined 
                            style={{ 
                              color: item.completed ? '#8c8c8c' : '#52c41a',
                              fontSize: 14 
                            }} 
                          />
                        )
                      },
                      {
                        key: 'edit',
                        label: '编辑任务',
                        icon: <EditOutlined style={{ color: '#8c8c8c' }} />
                      },
                      {
                        key: 'delete',
                        label: '删除任务',
                        icon: <DeleteOutlined style={{ color: '#ff4d4f' }} />,
                        danger: true
                      }
                    ],
                    onClick: ({ key }) => {
                      if (key === "complete") {
                        handleToggleTodoStatus(item.id);
                      } else if (key === "edit") {
                        setEditingTodoId(item.id);
                        todoForm.setFieldsValue({
                          name: item.name,
                          priority: item.priority
                        });
                        setTodoModalVisible(true);
                      } else if (key === "delete") {
                        handleDeleteTodo(item.id);
                      }
                    }
                  }}
                >
                  <Button 
                    type="text" 
                    size="small" 
                    icon={<MoreOutlined />} 
                    style={{ width: 32, height: 32 }} 
                  />
                </Dropdown>
              </Flex>
            </List.Item>
          );
        }}
      />

      {/* 待办事项表单模态框 */}
      <Modal
        title={editingTodoId ? "编辑待办事项" : "新增待办事项"}
        open={todoModalVisible}
        onCancel={() => {
          setTodoModalVisible(false);
          todoForm.resetFields();
        }}
        onOk={() => {
          todoForm.submit();
        }}
        centered
        destroyOnClose
        footer={[
          <Button key="cancel" onClick={() => setTodoModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={() => {
              todoForm.submit();
            }}
            style={{
              background: "#1890ff",
              borderColor: "#1890ff",
              boxShadow: "0 2px 4px rgba(24, 144, 255, 0.3)",
            }}
          >
            {editingTodoId ? "更新任务" : "创建任务"}
          </Button>,
        ]}
      >
        <Form
          form={todoForm}
          layout="vertical"
          onFinish={handleAddOrUpdateTodo}
          autoComplete="off"
        >
          <Form.Item
            name="name"
            label="任务名称"
            rules={[{ required: true, message: "请输入任务名称" }]}
          >
            <Input
              placeholder="请输入任务名称"
              size="large"
              style={{ borderRadius: 6 }}
            />
          </Form.Item>

          <Form.Item
            name="priority"
            label="优先级"
            initialValue="medium"
            rules={[{ required: true, message: "请选择优先级" }]}
          >
            <Select
              size="large"
              options={[
                { value: "high", label: "高优先级" },
                { value: "medium", label: "中优先级" },
                { value: "low", label: "低优先级" },
              ]}
              style={{ borderRadius: 6 }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default TodoManagement;