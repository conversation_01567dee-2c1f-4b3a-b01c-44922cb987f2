{"version": 3, "sources": ["src/pages/team/components/TeamListContent.tsx", "src/pages/team/index.tsx"], "sourcesContent": ["/**\n * 团队列表内容组件\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  List, \n  Avatar, \n  Button, \n  Space, \n  Typography, \n  Tag, \n  Input,\n  message,\n  Empty\n} from 'antd';\nimport { \n  TeamOutlined, \n  UserOutlined, \n  SearchOutlined,\n  CrownOutlined,\n  EyeOutlined\n} from '@ant-design/icons';\nimport { history } from '@umijs/max';\nimport { TeamService, AuthService } from '@/services';\nimport type { TeamDetailResponse } from '@/types/api';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\n\nconst TeamListContent: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\n  const [filteredTeams, setFilteredTeams] = useState<TeamDetailResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n\n  useEffect(() => {\n    fetchTeams();\n  }, []);\n\n  useEffect(() => {\n    // 过滤团队列表\n    const filtered = teams.filter(team =>\n      team.name.toLowerCase().includes(searchText.toLowerCase()) ||\n      (team.description && team.description.toLowerCase().includes(searchText.toLowerCase()))\n    );\n    setFilteredTeams(filtered);\n  }, [teams, searchText]);\n\n  const fetchTeams = async () => {\n    try {\n      setLoading(true);\n      const teamList = await TeamService.getUserTeams();\n      setTeams(teamList);\n    } catch (error) {\n      console.error('获取团队列表失败:', error);\n      message.error('获取团队列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateTeam = () => {\n    history.push('/team/create');\n  };\n\n  const handleViewTeam = async (team: TeamDetailResponse) => {\n    try {\n      // 切换到该团队\n      const response = await AuthService.teamLogin({ teamId: team.id });\n\n      // 检查后端返回的团队选择成功标识\n      if (response.teamSelectionSuccess && response.team && response.team.id === team.id) {\n        message.success(`已切换到团队：${team.name}`);\n        // 不需要刷新页面，让应用自然响应状态变化\n      } else {\n        console.error('团队切换响应异常，未返回正确的团队信息');\n        message.error('团队切换失败，请重试');\n      }\n    } catch (error) {\n      console.error('切换团队失败:', error);\n      message.error('切换团队失败');\n    }\n  };\n\n  const handleSwitchTeam = async (team: TeamDetailResponse) => {\n    try {\n      const response = await AuthService.teamLogin({ teamId: team.id });\n\n      // 检查后端返回的团队选择成功标识\n      if (response.teamSelectionSuccess && response.team && response.team.id === team.id) {\n        message.success(`已切换到团队：${team.name}`);\n\n        // 等待一段时间确保 Token 更新完成后再跳转\n        setTimeout(() => {\n          history.push('/');\n        }, 200);\n      } else {\n        console.error('团队切换响应异常，未返回正确的团队信息');\n        message.error('团队切换失败，请重试');\n      }\n    } catch (error) {\n      console.error('切换团队失败:', error);\n      message.error('切换团队失败');\n    }\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: 16 }}>\n        <Search\n          placeholder=\"搜索团队名称或描述\"\n          allowClear\n          value={searchText}\n          onChange={(e) => setSearchText(e.target.value)}\n          style={{ width: 300 }}\n        />\n      </div>\n\n      {filteredTeams.length === 0 && !loading ? (\n        <Empty\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\n          description={\n            searchText ? '没有找到匹配的团队' : '您还没有加入任何团队'\n          }\n        >\n          {!searchText && (\n            <Button type=\"primary\" onClick={handleCreateTeam}>\n              创建第一个团队\n            </Button>\n          )}\n        </Empty>\n      ) : (\n        <List\n          loading={loading}\n          itemLayout=\"horizontal\"\n          dataSource={filteredTeams}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个团队`,\n          }}\n          renderItem={(team) => (\n            <List.Item\n              actions={[\n                <Button\n                  key=\"view\"\n                  type=\"text\"\n                  icon={<EyeOutlined />}\n                  onClick={() => handleViewTeam(team)}\n                >\n                  查看详情\n                </Button>,\n                <Button\n                  key=\"switch\"\n                  type=\"primary\"\n                  size=\"small\"\n                  onClick={() => handleSwitchTeam(team)}\n                >\n                  进入团队\n                </Button>,\n              ]}\n            >\n              <List.Item.Meta\n                avatar={\n                  <Avatar \n                    size={64} \n                    icon={<TeamOutlined />}\n                    style={{ backgroundColor: '#1890ff' }}\n                  />\n                }\n                title={\n                  <Space>\n                    <Title level={4} style={{ margin: 0 }}>\n                      {team.name}\n                    </Title>\n                    {team.isCreator && (\n                      <Tag color=\"gold\" icon={<CrownOutlined />}>\n                        创建者\n                      </Tag>\n                    )}\n                  </Space>\n                }\n                description={\n                  <Space direction=\"vertical\" size=\"small\">\n                    {team.description && (\n                      <Text type=\"secondary\">{team.description}</Text>\n                    )}\n                    <Space>\n                      <Space size=\"small\">\n                        <UserOutlined />\n                        <Text type=\"secondary\">{team.memberCount} 名成员</Text>\n                      </Space>\n                      <Text type=\"secondary\">\n                        创建于 {new Date(team.createdAt).toLocaleDateString()}\n                      </Text>\n                      <Text type=\"secondary\">\n                        更新于 {new Date(team.updatedAt).toLocaleDateString()}\n                      </Text>\n                    </Space>\n                  </Space>\n                }\n              />\n            </List.Item>\n          )}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default TeamListContent;\n", "/**\n * 我的团队页面 - 简化版，只保留团队列表和切换功能\n */\n\nimport React from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\n\n// 导入团队列表组件\nimport TeamListContent from './components/TeamListContent';\n\nconst MyTeamsPage: React.FC = () => {\n  return (\n    <PageContainer title=\"我的团队\">\n      <TeamListContent />\n    </PageContainer>\n  );\n};\n\nexport default MyTeamsPage;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BAiND;;;eAAA;;;;;;wEA/M2C;6BAWpC;8BAOA;4BACiB;iCACiB;;;;;;;;;;AAGzC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,WAAK;AAExB,MAAM,kBAA4B;;IAChC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC3E,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAE7C,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,IAAA,gBAAS,EAAC;QACR,SAAS;QACT,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA,OAC5B,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACtD,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAErF,iBAAiB;IACnB,GAAG;QAAC;QAAO;KAAW;IAEtB,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,qBAAW,CAAC,YAAY;YAC/C,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,YAAO,CAAC,IAAI,CAAC;IACf;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,SAAS;YACT,MAAM,WAAW,MAAM,qBAAW,CAAC,SAAS,CAAC;gBAAE,QAAQ,KAAK,EAAE;YAAC;YAE/D,kBAAkB;YAClB,IAAI,SAAS,oBAAoB,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,KAAK,EAAE,EAChF,aAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC;iBAEhC;gBACL,QAAQ,KAAK,CAAC;gBACd,aAAO,CAAC,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,qBAAW,CAAC,SAAS,CAAC;gBAAE,QAAQ,KAAK,EAAE;YAAC;YAE/D,kBAAkB;YAClB,IAAI,SAAS,oBAAoB,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,KAAK,EAAE,EAAE;gBAClF,aAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC;gBAErC,0BAA0B;gBAC1B,WAAW;oBACT,YAAO,CAAC,IAAI,CAAC;gBACf,GAAG;YACL,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,aAAO,CAAC,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,qBACE,2BAAC;;0BACC,2BAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAG;0BAC7B,cAAA,2BAAC;oBACC,aAAY;oBACZ,UAAU;oBACV,OAAO;oBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oBAC7C,OAAO;wBAAE,OAAO;oBAAI;;;;;;;;;;;YAIvB,cAAc,MAAM,KAAK,KAAK,CAAC,wBAC9B,2BAAC,WAAK;gBACJ,OAAO,WAAK,CAAC,sBAAsB;gBACnC,aACE,aAAa,cAAc;0BAG5B,CAAC,4BACA,2BAAC,YAAM;oBAAC,MAAK;oBAAU,SAAS;8BAAkB;;;;;;;;;;qCAMtD,2BAAC,UAAI;gBACH,SAAS;gBACT,YAAW;gBACX,YAAY;gBACZ,YAAY;oBACV,iBAAiB;oBACjB,iBAAiB;oBACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;gBACxC;gBACA,YAAY,CAAC,qBACX,2BAAC,UAAI,CAAC,IAAI;wBACR,SAAS;0CACP,2BAAC,YAAM;gCAEL,MAAK;gCACL,oBAAM,2BAAC,kBAAW;;;;;gCAClB,SAAS,IAAM,eAAe;0CAC/B;+BAJK;;;;;0CAON,2BAAC,YAAM;gCAEL,MAAK;gCACL,MAAK;gCACL,SAAS,IAAM,iBAAiB;0CACjC;+BAJK;;;;;yBAOP;kCAED,cAAA,2BAAC,UAAI,CAAC,IAAI,CAAC,IAAI;4BACb,sBACE,2BAAC,YAAM;gCACL,MAAM;gCACN,oBAAM,2BAAC,mBAAY;;;;;gCACnB,OAAO;oCAAE,iBAAiB;gCAAU;;;;;;4BAGxC,qBACE,2BAAC,WAAK;;kDACJ,2BAAC;wCAAM,OAAO;wCAAG,OAAO;4CAAE,QAAQ;wCAAE;kDACjC,KAAK,IAAI;;;;;;oCAEX,KAAK,SAAS,kBACb,2BAAC,SAAG;wCAAC,OAAM;wCAAO,oBAAM,2BAAC,oBAAa;;;;;kDAAK;;;;;;;;;;;;4BAMjD,2BACE,2BAAC,WAAK;gCAAC,WAAU;gCAAW,MAAK;;oCAC9B,KAAK,WAAW,kBACf,2BAAC;wCAAK,MAAK;kDAAa,KAAK,WAAW;;;;;;kDAE1C,2BAAC,WAAK;;0DACJ,2BAAC,WAAK;gDAAC,MAAK;;kEACV,2BAAC,mBAAY;;;;;kEACb,2BAAC;wDAAK,MAAK;;4DAAa,KAAK,WAAW;4DAAC;;;;;;;;;;;;;0DAE3C,2BAAC;gDAAK,MAAK;;oDAAY;oDAChB,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;0DAElD,2BAAC;gDAAK,MAAK;;oDAAY;oDAChB,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxE;GAnLM;KAAA;IAqLN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnNf;;CAEC;;;;4BAgBD;;;eAAA;;;;;;;uEAdkB;sCACY;iFAGF;;;;;;;;;AAE5B,MAAM,cAAwB;IAC5B,qBACE,2BAAC,4BAAa;QAAC,OAAM;kBACnB,cAAA,2BAAC,wBAAe;;;;;;;;;;AAGtB;KANM;IAQN,WAAe"}