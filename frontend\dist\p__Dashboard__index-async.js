((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['p__Dashboard__index'],
{ "src/pages/Dashboard/index.tsx": function (module, exports, __mako_require__){
/**
 * 仪表板页面
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const Dashboard = ()=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(true);
    const [teamStats, setTeamStats] = (0, _react.useState)(null);
    const [userProfile, setUserProfile] = (0, _react.useState)(null);
    const [subscriptionStatus, setSubscriptionStatus] = (0, _react.useState)(null);
    const [usageInfo, setUsageInfo] = (0, _react.useState)(null);
    (0, _react.useEffect)(()=>{
        fetchDashboardData();
    }, []);
    const fetchDashboardData = async ()=>{
        try {
            setLoading(true);
            const [stats, profile, subStatus, usage] = await Promise.all([
                _services.TeamService.getTeamStats().catch(()=>null),
                _services.UserService.getUserProfile().catch(()=>null),
                _services.SubscriptionService.checkSubscriptionStatus().catch(()=>null),
                _services.SubscriptionService.getSubscriptionUsage().catch(()=>null)
            ]);
            setTeamStats(stats);
            setUserProfile(profile);
            setSubscriptionStatus(subStatus);
            setUsageInfo(usage);
        } catch (error) {
            console.error('获取仪表板数据失败:', error);
        } finally{
            setLoading(false);
        }
    };
    const quickActions = [
        {
            title: '邀请成员',
            description: '邀请新成员加入团队',
            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                fileName: "src/pages/Dashboard/index.tsx",
                lineNumber: 70,
                columnNumber: 13
            }, this),
            action: ()=>_max.history.push('/team/detail')
        },
        {
            title: '查看团队',
            description: '查看团队详情和成员',
            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                fileName: "src/pages/Dashboard/index.tsx",
                lineNumber: 76,
                columnNumber: 13
            }, this),
            action: ()=>_max.history.push('/team/detail')
        },
        {
            title: '管理订阅',
            description: '查看和管理订阅套餐',
            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                fileName: "src/pages/Dashboard/index.tsx",
                lineNumber: 82,
                columnNumber: 13
            }, this),
            action: ()=>_max.history.push('/subscription/manage')
        },
        {
            title: '个人设置',
            description: '修改个人资料和偏好',
            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                fileName: "src/pages/Dashboard/index.tsx",
                lineNumber: 88,
                columnNumber: 13
            }, this),
            action: ()=>_max.history.push('/user/profile')
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "仪表板",
        subTitle: "欢迎回来！这里是您的工作概览",
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            direction: "vertical",
            style: {
                width: '100%'
            },
            size: "large",
            children: [
                (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.isExpiringSoon) && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                    message: "订阅即将到期",
                    description: `您的订阅将在 ${subscriptionStatus.daysUntilExpiry} 天后到期，请及时续费。`,
                    type: "warning",
                    showIcon: true,
                    action: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        size: "small",
                        onClick: ()=>_max.history.push('/subscription/manage'),
                        children: "立即续费"
                    }, void 0, false, {
                        fileName: "src/pages/Dashboard/index.tsx",
                        lineNumber: 107,
                        columnNumber: 15
                    }, void 0)
                }, void 0, false, {
                    fileName: "src/pages/Dashboard/index.tsx",
                    lineNumber: 101,
                    columnNumber: 11
                }, this),
                (usageInfo === null || usageInfo === void 0 ? void 0 : usageInfo.usagePercentage) > 80 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                    message: "存储空间不足",
                    description: `您的存储空间使用量已达到 ${usageInfo.usagePercentage.toFixed(1)}%，建议升级套餐。`,
                    type: "warning",
                    showIcon: true,
                    action: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        size: "small",
                        onClick: ()=>_max.history.push('/subscription/plans'),
                        children: "升级套餐"
                    }, void 0, false, {
                        fileName: "src/pages/Dashboard/index.tsx",
                        lineNumber: 121,
                        columnNumber: 15
                    }, void 0)
                }, void 0, false, {
                    fileName: "src/pages/Dashboard/index.tsx",
                    lineNumber: 115,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                    gutter: [
                        16,
                        16
                    ],
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            sm: 12,
                            lg: 6,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                    title: "团队成员",
                                    value: (teamStats === null || teamStats === void 0 ? void 0 : teamStats.memberCount) || 0,
                                    prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                        fileName: "src/pages/Dashboard/index.tsx",
                                        lineNumber: 135,
                                        columnNumber: 25
                                    }, void 0),
                                    suffix: "人"
                                }, void 0, false, {
                                    fileName: "src/pages/Dashboard/index.tsx",
                                    lineNumber: 132,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/Dashboard/index.tsx",
                                lineNumber: 131,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/Dashboard/index.tsx",
                            lineNumber: 130,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            sm: 12,
                            lg: 6,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                    title: "活跃成员",
                                    value: (teamStats === null || teamStats === void 0 ? void 0 : teamStats.activeMembers) || 0,
                                    prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                        fileName: "src/pages/Dashboard/index.tsx",
                                        lineNumber: 145,
                                        columnNumber: 25
                                    }, void 0),
                                    suffix: "人"
                                }, void 0, false, {
                                    fileName: "src/pages/Dashboard/index.tsx",
                                    lineNumber: 142,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/Dashboard/index.tsx",
                                lineNumber: 141,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/Dashboard/index.tsx",
                            lineNumber: 140,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            sm: 12,
                            lg: 6,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                    title: "存储使用",
                                    value: (usageInfo === null || usageInfo === void 0 ? void 0 : usageInfo.usagePercentage) || 0,
                                    prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DatabaseOutlined, {}, void 0, false, {
                                        fileName: "src/pages/Dashboard/index.tsx",
                                        lineNumber: 155,
                                        columnNumber: 25
                                    }, void 0),
                                    suffix: "%"
                                }, void 0, false, {
                                    fileName: "src/pages/Dashboard/index.tsx",
                                    lineNumber: 152,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/Dashboard/index.tsx",
                                lineNumber: 151,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/Dashboard/index.tsx",
                            lineNumber: 150,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            sm: 12,
                            lg: 6,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                    title: "剩余天数",
                                    value: (usageInfo === null || usageInfo === void 0 ? void 0 : usageInfo.remainingDays) || 0,
                                    prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {}, void 0, false, {
                                        fileName: "src/pages/Dashboard/index.tsx",
                                        lineNumber: 165,
                                        columnNumber: 25
                                    }, void 0),
                                    suffix: "天"
                                }, void 0, false, {
                                    fileName: "src/pages/Dashboard/index.tsx",
                                    lineNumber: 162,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/Dashboard/index.tsx",
                                lineNumber: 161,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/Dashboard/index.tsx",
                            lineNumber: 160,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/Dashboard/index.tsx",
                    lineNumber: 129,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                    gutter: [
                        16,
                        16
                    ],
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            lg: 12,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                title: "存储使用情况",
                                loading: loading,
                                children: usageInfo && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                marginBottom: 16
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                children: [
                                                    "已使用 ",
                                                    usageInfo.currentUsage,
                                                    "GB / ",
                                                    usageInfo.maxUsage,
                                                    "GB"
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/Dashboard/index.tsx",
                                                lineNumber: 179,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 178,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                                            percent: usageInfo.usagePercentage,
                                            status: usageInfo.usagePercentage > 80 ? 'exception' : 'normal',
                                            strokeColor: usageInfo.usagePercentage > 80 ? '#ff4d4f' : '#1890ff'
                                        }, void 0, false, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 181,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                marginTop: 8,
                                                textAlign: 'right'
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                type: "link",
                                                size: "small",
                                                onClick: ()=>_max.history.push('/subscription/plans'),
                                                children: "升级存储空间"
                                            }, void 0, false, {
                                                fileName: "src/pages/Dashboard/index.tsx",
                                                lineNumber: 187,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 186,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/Dashboard/index.tsx",
                                    lineNumber: 177,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/Dashboard/index.tsx",
                                lineNumber: 175,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/Dashboard/index.tsx",
                            lineNumber: 174,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            lg: 12,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                title: "快速操作",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                                    dataSource: quickActions,
                                    renderItem: (item)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                            style: {
                                                cursor: 'pointer'
                                            },
                                            onClick: item.action,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                                                    avatar: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                        icon: item.icon
                                                    }, void 0, false, {
                                                        fileName: "src/pages/Dashboard/index.tsx",
                                                        lineNumber: 211,
                                                        columnNumber: 31
                                                    }, void 0),
                                                    title: item.title,
                                                    description: item.description
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 210,
                                                    columnNumber: 21
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.RightOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 215,
                                                    columnNumber: 21
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 206,
                                            columnNumber: 19
                                        }, void 0)
                                }, void 0, false, {
                                    fileName: "src/pages/Dashboard/index.tsx",
                                    lineNumber: 203,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/Dashboard/index.tsx",
                                lineNumber: 202,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/Dashboard/index.tsx",
                            lineNumber: 201,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/Dashboard/index.tsx",
                    lineNumber: 172,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                    gutter: [
                        16,
                        16
                    ],
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            lg: 12,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                title: "个人信息",
                                loading: loading,
                                children: userProfile && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            direction: "vertical",
                                            style: {
                                                width: '100%'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            strong: true,
                                                            children: "用户名："
                                                        }, void 0, false, {
                                                            fileName: "src/pages/Dashboard/index.tsx",
                                                            lineNumber: 231,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            children: userProfile.name
                                                        }, void 0, false, {
                                                            fileName: "src/pages/Dashboard/index.tsx",
                                                            lineNumber: 232,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 230,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            strong: true,
                                                            children: "邮箱："
                                                        }, void 0, false, {
                                                            fileName: "src/pages/Dashboard/index.tsx",
                                                            lineNumber: 235,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            children: userProfile.email
                                                        }, void 0, false, {
                                                            fileName: "src/pages/Dashboard/index.tsx",
                                                            lineNumber: 236,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 234,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            strong: true,
                                                            children: "注册时间："
                                                        }, void 0, false, {
                                                            fileName: "src/pages/Dashboard/index.tsx",
                                                            lineNumber: 239,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            children: new Date(userProfile.createdAt).toLocaleDateString()
                                                        }, void 0, false, {
                                                            fileName: "src/pages/Dashboard/index.tsx",
                                                            lineNumber: 240,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 238,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 229,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                marginTop: 16,
                                                textAlign: 'right'
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                type: "link",
                                                size: "small",
                                                onClick: ()=>_max.history.push('/user/profile'),
                                                children: "编辑资料"
                                            }, void 0, false, {
                                                fileName: "src/pages/Dashboard/index.tsx",
                                                lineNumber: 244,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 243,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/Dashboard/index.tsx",
                                    lineNumber: 228,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/Dashboard/index.tsx",
                                lineNumber: 226,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/Dashboard/index.tsx",
                            lineNumber: 225,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            lg: 12,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                title: "订阅状态",
                                loading: loading,
                                children: subscriptionStatus && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            direction: "vertical",
                                            style: {
                                                width: '100%'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            strong: true,
                                                            children: "订阅状态："
                                                        }, void 0, false, {
                                                            fileName: "src/pages/Dashboard/index.tsx",
                                                            lineNumber: 263,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                            color: subscriptionStatus.hasActiveSubscription ? 'green' : 'red',
                                                            children: subscriptionStatus.hasActiveSubscription ? '已订阅' : '未订阅'
                                                        }, void 0, false, {
                                                            fileName: "src/pages/Dashboard/index.tsx",
                                                            lineNumber: 264,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 262,
                                                    columnNumber: 21
                                                }, this),
                                                subscriptionStatus.hasActiveSubscription && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    strong: true,
                                                                    children: "到期时间："
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                                    lineNumber: 271,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    children: [
                                                                        subscriptionStatus.daysUntilExpiry,
                                                                        " 天后"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                                    lineNumber: 272,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/Dashboard/index.tsx",
                                                            lineNumber: 270,
                                                            columnNumber: 25
                                                        }, this),
                                                        subscriptionStatus.needsUpgrade && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                                    color: "orange",
                                                                    children: "建议升级"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                                    lineNumber: 276,
                                                                    columnNumber: 29
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    type: "secondary",
                                                                    children: "存储使用量较高"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                                    lineNumber: 277,
                                                                    columnNumber: 29
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/Dashboard/index.tsx",
                                                            lineNumber: 275,
                                                            columnNumber: 27
                                                        }, this)
                                                    ]
                                                }, void 0, true)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 261,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                marginTop: 16,
                                                textAlign: 'right'
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                type: "link",
                                                size: "small",
                                                onClick: ()=>_max.history.push('/subscription/manage'),
                                                children: "管理订阅"
                                            }, void 0, false, {
                                                fileName: "src/pages/Dashboard/index.tsx",
                                                lineNumber: 284,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 283,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/Dashboard/index.tsx",
                                    lineNumber: 260,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/Dashboard/index.tsx",
                                lineNumber: 258,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/Dashboard/index.tsx",
                            lineNumber: 257,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/Dashboard/index.tsx",
                    lineNumber: 224,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/Dashboard/index.tsx",
            lineNumber: 98,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/Dashboard/index.tsx",
        lineNumber: 94,
        columnNumber: 5
    }, this);
};
_s(Dashboard, "VDb5a8FIgQ2rF/mSK7dXLpoGNxc=");
_c = Dashboard;
var _default = Dashboard;
var _c;
$RefreshReg$(_c, "Dashboard");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__Dashboard__index-async.js.map