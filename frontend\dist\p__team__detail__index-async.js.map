{"version": 3, "sources": ["src/pages/team/detail/index.tsx"], "sourcesContent": ["/**\n * 团队详情页面\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Typography,\n  message,\n  Spin\n} from 'antd';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse } from '@/types/api';\nimport TeamDetailContent from './components/TeamDetailContent';\n\nconst { Text } = Typography;\n\nconst TeamDetailPage: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);\n\n  useEffect(() => {\n    fetchTeamDetail();\n  }, []);\n\n  const fetchTeamDetail = async () => {\n    try {\n      setLoading(true);\n      const detail = await TeamService.getCurrentTeamDetail();\n      setTeamDetail(detail);\n    } catch (error) {\n      console.error('获取团队详情失败:', error);\n      message.error('获取团队详情失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Spin size=\"large\" />\n        </div>\n      </PageContainer>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Text type=\"secondary\">团队信息加载失败</Text>\n        </div>\n      </PageContainer>\n    );\n  }\n\n  return (\n    <PageContainer style={{ background: '#f5f5f5' }}>\n      <TeamDetailContent\n        teamDetail={teamDetail}\n        loading={loading}\n        onRefresh={fetchTeamDetail}\n        showBackButton={true}\n      />\n    </PageContainer>\n  );\n};\n\nexport default TeamDetailPage;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BAoED;;;eAAA;;;;;;;wEAlE2C;6BAKpC;sCACuB;iCACF;mFAEE;;;;;;;;;;AAE9B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAE3B,MAAM,iBAA2B;;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAA4B;IAExE,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,MAAM,qBAAW,CAAC,oBAAoB;YACrD,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SACF,qBACE,2BAAC,4BAAa;kBACZ,cAAA,2BAAC;YAAI,OAAO;gBAAE,WAAW;gBAAU,SAAS;YAAS;sBACnD,cAAA,2BAAC,UAAI;gBAAC,MAAK;;;;;;;;;;;;;;;;IAMnB,IAAI,CAAC,YACH,qBACE,2BAAC,4BAAa;kBACZ,cAAA,2BAAC;YAAI,OAAO;gBAAE,WAAW;gBAAU,SAAS;YAAS;sBACnD,cAAA,2BAAC;gBAAK,MAAK;0BAAY;;;;;;;;;;;;;;;;IAM/B,qBACE,2BAAC,4BAAa;QAAC,OAAO;YAAE,YAAY;QAAU;kBAC5C,cAAA,2BAAC,0BAAiB;YAChB,YAAY;YACZ,SAAS;YACT,WAAW;YACX,gBAAgB;;;;;;;;;;;AAIxB;GAnDM;KAAA;IAqDN,WAAe"}