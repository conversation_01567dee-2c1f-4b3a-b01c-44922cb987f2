{"version": 3, "sources": ["src/.umi/plugin-openapi/openapi.tsx"], "sourcesContent": ["// @ts-nocheck\n// This file is generated by <PERSON><PERSON> automatically\n// DO NOT CHANGE IT MANUALLY!\n// This file is generated by <PERSON><PERSON> automatically\n      // DO NOT CHANGE IT MANUALLY!\n      import { useEffect, useState } from 'react';\n      import { SwaggerUIBundle } from 'swagger-ui-dist';\n      import 'swagger-ui-dist/swagger-ui.css';\n      const App = () => {\n        const [value, setValue] = useState(\"openapi\" );\n        useEffect(() => {\n          SwaggerUIBundle({\n            url: `/umi-plugins_${value}.json`,\n            dom_id: '#swagger-ui',\n          });\n        }, [value]);\n\n        return (\n          <div\n            style={{\n              padding: 24,\n            }}\n          >\n            <select\n              style={{\n                position: \"fixed\",\n                right: \"16px\",\n                top: \"8px\",\n              }}\n              onChange={(e) => setValue(e.target.value)}\n            >\n              <option value=\"openapi\">openapi</option>\n<option value=\"swagger\">swagger</option>\n            </select>\n            <div id=\"swagger-ui\" />\n          </div>\n        );\n      };\n      export default App;\n"], "names": [], "mappings": ";;;AAAA,cAAc;AACd,8CAA8C;AAC9C,6BAA6B;AAC7B,8CAA8C;AACxC,6BAA6B;;;;;4BAkC7B;;;eAAA;;;;;;8BAjCoC;sCACJ;;;;;;;;;;;AAEhC,MAAM,MAAM;;IACV,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAC;IACnC,IAAA,gBAAS,EAAC;QACR,IAAA,8BAAe,EAAC;YACd,KAAK,CAAC,aAAa,EAAE,MAAM,KAAK,CAAC;YACjC,QAAQ;QACV;IACF,GAAG;QAAC;KAAM;IAEV,qBACE,2BAAC;QACC,OAAO;YACL,SAAS;QACX;;0BAEA,2BAAC;gBACC,OAAO;oBACL,UAAU;oBACV,OAAO;oBACP,KAAK;gBACP;gBACA,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;;kCAExC,2BAAC;wBAAO,OAAM;kCAAU;;;;;;kCACtC,2BAAC;wBAAO,OAAM;kCAAU;;;;;;;;;;;;0BAEZ,2BAAC;gBAAI,IAAG;;;;;;;;;;;;AAGd;GA7BM;KAAA;IA8BN,WAAe"}