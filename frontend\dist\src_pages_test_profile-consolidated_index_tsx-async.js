((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['src/pages/test/profile-consolidated/index.tsx'],
{ "src/pages/test/profile-consolidated/TeamListCard.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _team = __mako_require__("src/services/team.ts");
var _services = __mako_require__("src/services/index.ts");
var _max = __mako_require__("src/.umi/exports.ts");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text, Title } = _antd.Typography;
const TeamListCard = ()=>{
    _s();
    const [teams, setTeams] = (0, _react.useState)([]);
    const [loading, setLoading] = (0, _react.useState)(true);
    const [error, setError] = (0, _react.useState)(null);
    const [switchingTeamId, setSwitchingTeamId] = (0, _react.useState)(null);
    const { initialState, setInitialState } = (0, _max.useModel)('@@initialState');
    const currentTeam = initialState === null || initialState === void 0 ? void 0 : initialState.currentTeam;
    (0, _react.useEffect)(()=>{
        const fetchTeams = async ()=>{
            try {
                setLoading(true);
                setError(null);
                const teamsData = await _team.TeamService.getUserTeamsWithStats();
                setTeams(teamsData);
            } catch (error) {
                console.error('获取团队列表失败:', error);
                setError('获取团队列表失败');
            } finally{
                setLoading(false);
            }
        };
        fetchTeams();
    }, []);
    const handleTeamSwitch = async (teamId, teamName)=>{
        if (teamId === (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id)) {
            _antd.message.info('您已经在当前团队中');
            return;
        }
        try {
            setSwitchingTeamId(teamId);
            const response = await _services.AuthService.selectTeam({
                teamId
            });
            if (response.teamSelectionSuccess && response.team && response.team.id === teamId) {
                _antd.message.success(`已切换到团队：${teamName}`);
                if ((initialState === null || initialState === void 0 ? void 0 : initialState.fetchTeamInfo) && (initialState === null || initialState === void 0 ? void 0 : initialState.fetchUserInfo) && setInitialState) try {
                    const [currentUser, currentTeam] = await Promise.all([
                        initialState.fetchUserInfo(),
                        initialState.fetchTeamInfo()
                    ]);
                    if (currentTeam && currentTeam.id === teamId) {
                        await setInitialState({
                            ...initialState,
                            currentUser,
                            currentTeam
                        });
                        setTimeout(()=>{
                            _max.history.push('/dashboard');
                        }, 100);
                    } else {
                        console.error('获取的团队信息与选择的团队不匹配');
                        _antd.message.error('团队切换失败，请重试');
                    }
                } catch (error) {
                    console.error('更新 initialState 失败:', error);
                    _antd.message.error('团队切换失败，请重试');
                }
                else _max.history.push('/dashboard');
            } else {
                console.error('团队切换响应异常，未返回正确的团队信息');
                _antd.message.error('团队切换失败，请重试');
            }
        } catch (error) {
            console.error('团队切换失败:', error);
            _antd.message.error('团队切换失败');
        } finally{
            setSwitchingTeamId(null);
        }
    };
    return (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
        className: "dashboard-card",
        style: {
            borderRadius: 12,
            boxShadow: "0 4px 12px rgba(0,0,0,0.05)",
            border: "none",
            background: "linear-gradient(145deg, #ffffff, #f5f8ff)"
        },
        title: (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
            justify: "space-between",
            align: "center",
            children: (0, _jsxdevruntime.jsxDEV)(Title, {
                level: 5,
                style: {
                    margin: 0
                },
                children: "团队列表"
            }, void 0, false, {
                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                lineNumber: 134,
                columnNumber: 11
            }, void 0)
        }, void 0, false, {
            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
            lineNumber: 133,
            columnNumber: 9
        }, void 0),
        children: error ? (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
            message: "团队列表加载失败",
            description: error,
            type: "error",
            showIcon: true,
            style: {
                marginBottom: 16
            }
        }, void 0, false, {
            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
            lineNumber: 139,
            columnNumber: 9
        }, this) : (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
            spinning: loading,
            children: (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                dataSource: teams,
                renderItem: (item)=>{
                    var _item_stats, _item_stats1, _item_stats2, _item_stats3;
                    return (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            className: "team-item",
                            style: {
                                background: "#fff",
                                borderRadius: 12,
                                boxShadow: "0 2px 8px rgba(0,0,0,0.06)",
                                width: "100%",
                                borderLeft: `4px solid ${item.isCreator ? "#1890ff" : "#52c41a"}`,
                                transition: "all 0.3s ease",
                                border: "1px solid #f0f0f0",
                                padding: "20px 24px"
                            },
                            hoverable: true,
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                justify: "space-between",
                                align: "top",
                                gutter: [
                                    20,
                                    0
                                ],
                                children: [
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        flex: "1",
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            vertical: true,
                                            gap: 12,
                                            children: [
                                                (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                    align: "center",
                                                    justify: "space-between",
                                                    style: {
                                                        width: '100%'
                                                    },
                                                    children: [
                                                        (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                            align: "center",
                                                            gap: 12,
                                                            children: [
                                                                (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        position: 'relative',
                                                                        cursor: (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) === item.id ? 'default' : 'pointer',
                                                                        padding: '4px 0'
                                                                    },
                                                                    onClick: ()=>handleTeamSwitch(item.id, item.name),
                                                                    children: [
                                                                        (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            strong: true,
                                                                            style: {
                                                                                fontSize: 20,
                                                                                color: (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) === item.id ? '#1890ff' : '#262626',
                                                                                lineHeight: 1.2,
                                                                                transition: 'all 0.2s ease',
                                                                                position: 'relative'
                                                                            },
                                                                            className: (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) !== item.id ? 'team-name-hover' : '',
                                                                            children: item.name
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 183,
                                                                            columnNumber: 31
                                                                        }, void 0),
                                                                        (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) !== item.id && (0, _jsxdevruntime.jsxDEV)(_icons.RightOutlined, {
                                                                            style: {
                                                                                fontSize: 12,
                                                                                color: '#8c8c8c',
                                                                                marginLeft: 8,
                                                                                transition: 'all 0.2s ease'
                                                                            },
                                                                            className: "team-switch-icon"
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 197,
                                                                            columnNumber: 33
                                                                        }, void 0)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                    lineNumber: 175,
                                                                    columnNumber: 29
                                                                }, void 0),
                                                                (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) === item.id && (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        background: 'linear-gradient(135deg, #1890ff, #40a9ff)',
                                                                        color: 'white',
                                                                        padding: '4px 12px',
                                                                        borderRadius: 20,
                                                                        fontSize: 12,
                                                                        fontWeight: 600,
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        gap: 4,
                                                                        boxShadow: '0 2px 6px rgba(24, 144, 255, 0.3)'
                                                                    },
                                                                    children: [
                                                                        (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {
                                                                            style: {
                                                                                fontSize: 12
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 223,
                                                                            columnNumber: 33
                                                                        }, void 0),
                                                                        "当前团队"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                    lineNumber: 211,
                                                                    columnNumber: 31
                                                                }, void 0),
                                                                switchingTeamId === item.id && (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        background: '#f0f0f0',
                                                                        padding: '6px 12px',
                                                                        borderRadius: 20,
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        gap: 6
                                                                    },
                                                                    children: [
                                                                        (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                                                            size: "small"
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 238,
                                                                            columnNumber: 33
                                                                        }, void 0),
                                                                        (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 12,
                                                                                color: '#666'
                                                                            },
                                                                            children: "切换中..."
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 239,
                                                                            columnNumber: 33
                                                                        }, void 0)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                    lineNumber: 230,
                                                                    columnNumber: 31
                                                                }, void 0)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                            lineNumber: 173,
                                                            columnNumber: 27
                                                        }, void 0),
                                                        (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                background: item.isCreator ? 'linear-gradient(135deg, #722ed1, #9254de)' : 'linear-gradient(135deg, #52c41a, #73d13d)',
                                                                color: 'white',
                                                                padding: '6px 14px',
                                                                borderRadius: 16,
                                                                fontSize: 12,
                                                                fontWeight: 600,
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                gap: 6,
                                                                boxShadow: item.isCreator ? '0 2px 8px rgba(114, 46, 209, 0.3)' : '0 2px 8px rgba(82, 196, 26, 0.3)',
                                                                minWidth: 'fit-content'
                                                            },
                                                            children: item.isCreator ? (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                                children: [
                                                                    (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {
                                                                        style: {
                                                                            fontSize: 14
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 264,
                                                                        columnNumber: 33
                                                                    }, void 0),
                                                                    "管理员"
                                                                ]
                                                            }, void 0, true) : (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                                children: [
                                                                    (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                                        style: {
                                                                            fontSize: 14
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 269,
                                                                        columnNumber: 33
                                                                    }, void 0),
                                                                    "成员"
                                                                ]
                                                            }, void 0, true)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                            lineNumber: 245,
                                                            columnNumber: 27
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                    lineNumber: 172,
                                                    columnNumber: 25
                                                }, void 0),
                                                (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                    align: "center",
                                                    gap: 24,
                                                    children: [
                                                        (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                            title: `创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`,
                                                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                align: "center",
                                                                gap: 6,
                                                                children: [
                                                                    (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                                                        style: {
                                                                            color: "#8c8c8c",
                                                                            fontSize: 14
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 280,
                                                                        columnNumber: 31
                                                                    }, void 0),
                                                                    (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        type: "secondary",
                                                                        style: {
                                                                            fontSize: 13
                                                                        },
                                                                        children: [
                                                                            "创建于 ",
                                                                            new Date(item.createdAt).toLocaleDateString('zh-CN')
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 281,
                                                                        columnNumber: 31
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 279,
                                                                columnNumber: 29
                                                            }, void 0)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                            lineNumber: 278,
                                                            columnNumber: 27
                                                        }, void 0),
                                                        (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                            title: `团队成员: ${item.memberCount}人`,
                                                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                align: "center",
                                                                gap: 6,
                                                                children: [
                                                                    (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                                        style: {
                                                                            color: "#8c8c8c",
                                                                            fontSize: 14
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 289,
                                                                        columnNumber: 31
                                                                    }, void 0),
                                                                    (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        type: "secondary",
                                                                        style: {
                                                                            fontSize: 13
                                                                        },
                                                                        children: [
                                                                            item.memberCount,
                                                                            " 名成员"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 290,
                                                                        columnNumber: 31
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 288,
                                                                columnNumber: 29
                                                            }, void 0)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                            lineNumber: 287,
                                                            columnNumber: 27
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                    lineNumber: 277,
                                                    columnNumber: 25
                                                }, void 0),
                                                (0, _jsxdevruntime.jsxDEV)("div", {
                                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                        color: item.isCreator ? "blue" : "green",
                                                        icon: (0, _jsxdevruntime.jsxDEV)(_icons.UserSwitchOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                            lineNumber: 301,
                                                            columnNumber: 35
                                                        }, void 0),
                                                        style: {
                                                            fontSize: 12,
                                                            fontWeight: 500,
                                                            display: 'inline-flex',
                                                            alignItems: 'center',
                                                            height: 26,
                                                            paddingLeft: 10,
                                                            paddingRight: 10,
                                                            borderRadius: 6
                                                        },
                                                        children: item.isCreator ? "团队管理员" : "团队成员"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                        lineNumber: 299,
                                                        columnNumber: 27
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                    lineNumber: 298,
                                                    columnNumber: 25
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                            lineNumber: 170,
                                            columnNumber: 23
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                        lineNumber: 169,
                                        columnNumber: 21
                                    }, void 0),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        flex: "none",
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            gap: 12,
                                            wrap: "wrap",
                                            justify: "end",
                                            children: [
                                                (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                    size: "small",
                                                    style: {
                                                        background: '#f0f7ff',
                                                        borderColor: '#d9e8ff',
                                                        borderRadius: 8,
                                                        minWidth: 90,
                                                        boxShadow: '0 1px 4px rgba(0,0,0,0.06)',
                                                        padding: '8px 12px',
                                                        textAlign: 'center'
                                                    },
                                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        vertical: true,
                                                        align: "center",
                                                        gap: 4,
                                                        children: [
                                                            (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                align: "center",
                                                                gap: 6,
                                                                children: [
                                                                    (0, _jsxdevruntime.jsxDEV)(_icons.CarOutlined, {
                                                                        style: {
                                                                            color: "#1890ff",
                                                                            fontSize: 18
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 337,
                                                                        columnNumber: 31
                                                                    }, void 0),
                                                                    (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        strong: true,
                                                                        style: {
                                                                            fontSize: 16,
                                                                            color: '#1890ff'
                                                                        },
                                                                        children: ((_item_stats = item.stats) === null || _item_stats === void 0 ? void 0 : _item_stats.vehicles) || 0
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 338,
                                                                        columnNumber: 31
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 336,
                                                                columnNumber: 29
                                                            }, void 0),
                                                            (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    fontSize: 12,
                                                                    color: '#666',
                                                                    lineHeight: 1
                                                                },
                                                                children: "车辆资源"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 340,
                                                                columnNumber: 29
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                        lineNumber: 335,
                                                        columnNumber: 27
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                    lineNumber: 323,
                                                    columnNumber: 25
                                                }, void 0),
                                                (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                    size: "small",
                                                    style: {
                                                        background: '#f6ffed',
                                                        borderColor: '#d1f0be',
                                                        borderRadius: 8,
                                                        minWidth: 90,
                                                        boxShadow: '0 1px 4px rgba(0,0,0,0.06)',
                                                        padding: '8px 12px',
                                                        textAlign: 'center'
                                                    },
                                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        vertical: true,
                                                        align: "center",
                                                        gap: 4,
                                                        children: [
                                                            (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                align: "center",
                                                                gap: 6,
                                                                children: [
                                                                    (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                                        style: {
                                                                            color: "#52c41a",
                                                                            fontSize: 18
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 359,
                                                                        columnNumber: 31
                                                                    }, void 0),
                                                                    (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        strong: true,
                                                                        style: {
                                                                            fontSize: 16,
                                                                            color: '#52c41a'
                                                                        },
                                                                        children: ((_item_stats1 = item.stats) === null || _item_stats1 === void 0 ? void 0 : _item_stats1.personnel) || 0
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 360,
                                                                        columnNumber: 31
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 358,
                                                                columnNumber: 29
                                                            }, void 0),
                                                            (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    fontSize: 12,
                                                                    color: '#666',
                                                                    lineHeight: 1
                                                                },
                                                                children: "人员资源"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 362,
                                                                columnNumber: 29
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                        lineNumber: 357,
                                                        columnNumber: 27
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                    lineNumber: 345,
                                                    columnNumber: 25
                                                }, void 0),
                                                (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                    size: "small",
                                                    style: {
                                                        background: '#fff7e6',
                                                        borderColor: '#ffdfa6',
                                                        borderRadius: 8,
                                                        minWidth: 90,
                                                        boxShadow: '0 1px 4px rgba(0,0,0,0.06)',
                                                        padding: '8px 12px',
                                                        textAlign: 'center'
                                                    },
                                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        vertical: true,
                                                        align: "center",
                                                        gap: 4,
                                                        children: [
                                                            (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                align: "center",
                                                                gap: 6,
                                                                children: [
                                                                    (0, _jsxdevruntime.jsxDEV)(_icons.WarningOutlined, {
                                                                        style: {
                                                                            color: "#faad14",
                                                                            fontSize: 18
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 381,
                                                                        columnNumber: 31
                                                                    }, void 0),
                                                                    (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        strong: true,
                                                                        style: {
                                                                            fontSize: 16,
                                                                            color: '#faad14'
                                                                        },
                                                                        children: ((_item_stats2 = item.stats) === null || _item_stats2 === void 0 ? void 0 : _item_stats2.expiring) || 0
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 382,
                                                                        columnNumber: 31
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 380,
                                                                columnNumber: 29
                                                            }, void 0),
                                                            (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    fontSize: 12,
                                                                    color: '#666',
                                                                    lineHeight: 1
                                                                },
                                                                children: "临期事项"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 384,
                                                                columnNumber: 29
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                        lineNumber: 379,
                                                        columnNumber: 27
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                    lineNumber: 367,
                                                    columnNumber: 25
                                                }, void 0),
                                                (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                    size: "small",
                                                    style: {
                                                        background: '#fff1f0',
                                                        borderColor: '#ffccc7',
                                                        borderRadius: 8,
                                                        minWidth: 90,
                                                        boxShadow: '0 1px 4px rgba(0,0,0,0.06)',
                                                        padding: '8px 12px',
                                                        textAlign: 'center'
                                                    },
                                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        vertical: true,
                                                        align: "center",
                                                        gap: 4,
                                                        children: [
                                                            (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                align: "center",
                                                                gap: 6,
                                                                children: [
                                                                    (0, _jsxdevruntime.jsxDEV)(_icons.WarningOutlined, {
                                                                        style: {
                                                                            color: "#ff4d4f",
                                                                            fontSize: 18
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 403,
                                                                        columnNumber: 31
                                                                    }, void 0),
                                                                    (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        strong: true,
                                                                        style: {
                                                                            fontSize: 16,
                                                                            color: '#ff4d4f'
                                                                        },
                                                                        children: ((_item_stats3 = item.stats) === null || _item_stats3 === void 0 ? void 0 : _item_stats3.overdue) || 0
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 404,
                                                                        columnNumber: 31
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 402,
                                                                columnNumber: 29
                                                            }, void 0),
                                                            (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    fontSize: 12,
                                                                    color: '#666',
                                                                    lineHeight: 1
                                                                },
                                                                children: "逾期事项"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 406,
                                                                columnNumber: 29
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                        lineNumber: 401,
                                                        columnNumber: 27
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                    lineNumber: 389,
                                                    columnNumber: 25
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                            lineNumber: 321,
                                            columnNumber: 23
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                        lineNumber: 320,
                                        columnNumber: 21
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                lineNumber: 167,
                                columnNumber: 19
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                            lineNumber: 152,
                            columnNumber: 17
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                        lineNumber: 151,
                        columnNumber: 15
                    }, void 0);
                }
            }, void 0, false, {
                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                lineNumber: 148,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
            lineNumber: 147,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
        lineNumber: 124,
        columnNumber: 5
    }, this);
};
_s(TeamListCard, "4DmFk5I0DN4oNd3BYpKpdN0lgTI=", false, function() {
    return [
        _max.useModel
    ];
});
_c = TeamListCard;
var _default = TeamListCard;
var _c;
$RefreshReg$(_c, "TeamListCard");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/test/profile-consolidated/TodoManagement.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _todo = __mako_require__("src/services/todo.ts");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text } = _antd.Typography;
const { TabPane } = _antd.Tabs;
const TodoManagement = (props)=>{
    _s();
    const [personalTasks, setPersonalTasks] = (0, _react.useState)([]);
    const [todoStats, setTodoStats] = (0, _react.useState)({
        highPriorityCount: 0,
        mediumPriorityCount: 0,
        lowPriorityCount: 0,
        totalCount: 0,
        completedCount: 0,
        completionPercentage: 0
    });
    const [loading, setLoading] = (0, _react.useState)(true);
    const [error, setError] = (0, _react.useState)(null);
    const [todoModalVisible, setTodoModalVisible] = (0, _react.useState)(false);
    const [todoForm] = _antd.Form.useForm();
    const [editingTodoId, setEditingTodoId] = (0, _react.useState)(null);
    const [activeTab, setActiveTab] = (0, _react.useState)("pending");
    const [searchText, setSearchText] = (0, _react.useState)("");
    (0, _react.useEffect)(()=>{
        const fetchTodoData = async ()=>{
            try {
                setLoading(true);
                setError(null);
                console.log('TodoManagement: 开始获取TODO数据');
                const todosPromise = _todo.TodoService.getUserTodos().catch((error)=>{
                    console.error('获取TODO列表失败:', error);
                    return [];
                });
                const statsPromise = _todo.TodoService.getTodoStats().catch((error)=>{
                    console.error('获取TODO统计失败:', error);
                    return {
                        highPriorityCount: 0,
                        mediumPriorityCount: 0,
                        lowPriorityCount: 0,
                        totalCount: 0,
                        completedCount: 0,
                        completionPercentage: 0
                    };
                });
                const [todos, stats] = await Promise.all([
                    todosPromise,
                    statsPromise
                ]);
                console.log('TodoManagement: 获取到TODO列表:', todos);
                console.log('TodoManagement: 获取到统计数据:', stats);
                setPersonalTasks(todos);
                setTodoStats(stats);
            } catch (error) {
                console.error('获取TODO数据时发生未知错误:', error);
                setError('获取TODO数据失败，请刷新页面重试');
            } finally{
                setLoading(false);
            }
        };
        fetchTodoData();
    }, []);
    const filteredPersonalTasks = personalTasks.filter((task)=>{
        if (activeTab === "pending" && task.status === 1) return false;
        if (activeTab === "completed" && task.status === 0) return false;
        if (searchText && !task.title.toLowerCase().includes(searchText.toLowerCase())) return false;
        return true;
    });
    const handleToggleTodoStatus = async (id)=>{
        try {
            const task = personalTasks.find((t)=>t.id === id);
            if (!task) {
                _antd.message.error('任务不存在');
                return;
            }
            const newStatus = task.status === 0 ? 1 : 0;
            console.log(`TodoManagement: 更新任务状态 ${id} -> ${newStatus}`);
            await _todo.TodoService.updateTodo(id, {
                status: newStatus
            });
            setPersonalTasks(personalTasks.map((task)=>task.id === id ? {
                    ...task,
                    status: newStatus
                } : task));
            try {
                const stats = await _todo.TodoService.getTodoStats();
                setTodoStats(stats);
            } catch (statsError) {
                console.error('刷新统计数据失败:', statsError);
            }
            _antd.message.success(newStatus === 1 ? '任务已完成' : '任务已标记为未完成');
        } catch (error) {
            console.error('更新任务状态失败:', error);
            _antd.message.error('更新任务状态失败，请稍后重试');
        }
    };
    const handleAddOrUpdateTodo = async (values)=>{
        try {
            console.log('TodoManagement: 保存任务', {
                editingTodoId,
                values
            });
            if (editingTodoId) {
                const updatedTodo = await _todo.TodoService.updateTodo(editingTodoId, {
                    title: values.name,
                    priority: values.priority
                });
                setPersonalTasks(personalTasks.map((task)=>task.id === editingTodoId ? updatedTodo : task));
                _antd.message.success('任务更新成功');
            } else {
                const newTodo = await _todo.TodoService.createTodo({
                    title: values.name,
                    priority: values.priority
                });
                setPersonalTasks([
                    newTodo,
                    ...personalTasks
                ]);
                _antd.message.success('任务创建成功');
            }
            try {
                const stats = await _todo.TodoService.getTodoStats();
                setTodoStats(stats);
            } catch (statsError) {
                console.error('刷新统计数据失败:', statsError);
            }
            setTodoModalVisible(false);
            setEditingTodoId(null);
            todoForm.resetFields();
        } catch (error) {
            console.error('保存任务失败:', error);
            const action = editingTodoId ? '更新' : '创建';
            _antd.message.error(`${action}任务失败，请检查网络连接后重试`);
        }
    };
    const handleDeleteTodo = async (id)=>{
        try {
            console.log('TodoManagement: 删除任务', id);
            await _todo.TodoService.deleteTodo(id);
            setPersonalTasks(personalTasks.filter((task)=>task.id !== id));
            try {
                const stats = await _todo.TodoService.getTodoStats();
                setTodoStats(stats);
            } catch (statsError) {
                console.error('刷新统计数据失败:', statsError);
            }
            _antd.message.success('任务删除成功');
        } catch (error) {
            console.error('删除任务失败:', error);
            _antd.message.error('删除任务失败，请稍后重试');
        }
    };
    return (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
        className: "dashboard-card",
        style: {
            borderRadius: 12,
            boxShadow: "0 4px 12px rgba(0,0,0,0.05)",
            border: "none",
            background: "linear-gradient(145deg, #ffffff, #f5f8ff)"
        },
        title: (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
            justify: "space-between",
            align: "center",
            children: (0, _jsxdevruntime.jsxDEV)(Text, {
                strong: true,
                children: "待办事项"
            }, void 0, false, {
                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                lineNumber: 251,
                columnNumber: 11
            }, void 0)
        }, void 0, false, {
            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
            lineNumber: 250,
            columnNumber: 9
        }, void 0),
        children: [
            (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                justify: "space-between",
                align: "center",
                style: {
                    marginBottom: 16,
                    gap: 12,
                    flexWrap: "wrap",
                    padding: "12px 16px",
                    background: "#fafbfc",
                    borderRadius: 8,
                    border: "1px solid #f0f0f0"
                },
                children: [
                    (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                        align: "center",
                        gap: 12,
                        style: {
                            flex: 1,
                            minWidth: 280
                        },
                        children: [
                            (0, _jsxdevruntime.jsxDEV)(_antd.Input.Search, {
                                placeholder: "搜索任务...",
                                allowClear: true,
                                prefix: (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 274,
                                    columnNumber: 21
                                }, void 0),
                                value: searchText,
                                onChange: (e)=>setSearchText(e.target.value),
                                style: {
                                    flex: 1,
                                    maxWidth: 300
                                },
                                size: "middle"
                            }, void 0, false, {
                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                lineNumber: 271,
                                columnNumber: 11
                            }, this),
                            (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "primary",
                                icon: (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 286,
                                    columnNumber: 19
                                }, void 0),
                                onClick: ()=>{
                                    setEditingTodoId(null);
                                    todoForm.resetFields();
                                    setTodoModalVisible(true);
                                },
                                style: {
                                    background: "#1890ff",
                                    borderColor: "#1890ff",
                                    boxShadow: "0 2px 4px rgba(24, 144, 255, 0.3)",
                                    fontWeight: 500,
                                    minWidth: 80
                                },
                                size: "middle",
                                children: "新增"
                            }, void 0, false, {
                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                lineNumber: 284,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                        lineNumber: 270,
                        columnNumber: 9
                    }, this),
                    (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                        align: "center",
                        gap: 16,
                        style: {
                            flexShrink: 0
                        },
                        children: [
                            (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                size: 12,
                                children: [
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                        title: `高优先级任务: ${todoStats.highPriorityCount}个`,
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            align: "center",
                                            gap: 6,
                                            children: [
                                                (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        width: 8,
                                                        height: 8,
                                                        borderRadius: "50%",
                                                        background: "#ff4d4f"
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 311,
                                                    columnNumber: 17
                                                }, this),
                                                (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontSize: 13,
                                                        fontWeight: 500,
                                                        color: "#262626"
                                                    },
                                                    children: [
                                                        "高: ",
                                                        todoStats.highPriorityCount
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 319,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 310,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                        lineNumber: 309,
                                        columnNumber: 13
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                        title: `中优先级任务: ${todoStats.mediumPriorityCount}个`,
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            align: "center",
                                            gap: 6,
                                            children: [
                                                (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        width: 8,
                                                        height: 8,
                                                        borderRadius: "50%",
                                                        background: "#faad14"
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 327,
                                                    columnNumber: 17
                                                }, this),
                                                (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontSize: 13,
                                                        fontWeight: 500,
                                                        color: "#262626"
                                                    },
                                                    children: [
                                                        "中: ",
                                                        todoStats.mediumPriorityCount
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 335,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 326,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                        lineNumber: 325,
                                        columnNumber: 13
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                        title: `低优先级任务: ${todoStats.lowPriorityCount}个`,
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            align: "center",
                                            gap: 6,
                                            children: [
                                                (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        width: 8,
                                                        height: 8,
                                                        borderRadius: "50%",
                                                        background: "#52c41a"
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 343,
                                                    columnNumber: 17
                                                }, this),
                                                (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontSize: 13,
                                                        fontWeight: 500,
                                                        color: "#262626"
                                                    },
                                                    children: [
                                                        "低: ",
                                                        todoStats.lowPriorityCount
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 351,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 342,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                        lineNumber: 341,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                lineNumber: 308,
                                columnNumber: 11
                            }, this),
                            (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                type: "vertical",
                                style: {
                                    height: 20,
                                    backgroundColor: "#d9d9d9"
                                }
                            }, void 0, false, {
                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                lineNumber: 358,
                                columnNumber: 11
                            }, this),
                            (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                title: `完成率: ${todoStats.completionPercentage}% (${todoStats.completedCount}/${todoStats.totalCount})`,
                                children: (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                    align: "center",
                                    gap: 8,
                                    children: [
                                        (0, _jsxdevruntime.jsxDEV)(Text, {
                                            style: {
                                                fontSize: 13,
                                                fontWeight: 500,
                                                color: "#595959"
                                            },
                                            children: "完成率:"
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 363,
                                            columnNumber: 15
                                        }, this),
                                        (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                                            percent: todoStats.completionPercentage,
                                            size: "small",
                                            style: {
                                                width: 100
                                            },
                                            strokeColor: "#52c41a",
                                            showInfo: false
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 366,
                                            columnNumber: 15
                                        }, this),
                                        (0, _jsxdevruntime.jsxDEV)(Text, {
                                            style: {
                                                fontSize: 13,
                                                fontWeight: 600,
                                                color: "#262626"
                                            },
                                            children: [
                                                todoStats.completionPercentage,
                                                "%"
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 373,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 362,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                lineNumber: 361,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                        lineNumber: 306,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                lineNumber: 256,
                columnNumber: 7
            }, this),
            (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                activeKey: activeTab,
                onChange: (key)=>setActiveTab(key),
                size: "middle",
                style: {
                    marginBottom: 8
                },
                children: [
                    (0, _jsxdevruntime.jsxDEV)(TabPane, {
                        tab: "全部"
                    }, "all", false, {
                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                        lineNumber: 390,
                        columnNumber: 9
                    }, this),
                    (0, _jsxdevruntime.jsxDEV)(TabPane, {
                        tab: "待处理"
                    }, "pending", false, {
                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                        lineNumber: 391,
                        columnNumber: 9
                    }, this),
                    (0, _jsxdevruntime.jsxDEV)(TabPane, {
                        tab: "已完成"
                    }, "completed", false, {
                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                        lineNumber: 392,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                lineNumber: 382,
                columnNumber: 7
            }, this),
            error ? (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                message: "TODO数据加载失败",
                description: error,
                type: "error",
                showIcon: true,
                style: {
                    marginBottom: 16
                }
            }, void 0, false, {
                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                lineNumber: 397,
                columnNumber: 9
            }, this) : (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                spinning: loading,
                children: [
                    (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                        dataSource: filteredPersonalTasks,
                        renderItem: (item)=>{
                            return (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                className: "todo-item",
                                style: {
                                    padding: "10px 16px",
                                    marginBottom: 12,
                                    borderRadius: 8,
                                    background: "#fff",
                                    opacity: item.status === 1 ? 0.7 : 1,
                                    borderLeft: `3px solid ${item.status === 1 ? "#52c41a" : item.priority === 3 ? "#ff4d4f" : item.priority === 2 ? "#faad14" : "#8c8c8c"}`,
                                    boxShadow: "0 1px 4px rgba(0,0,0,0.05)"
                                },
                                children: (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                    align: "center",
                                    gap: 12,
                                    style: {
                                        width: "100%"
                                    },
                                    children: [
                                        (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            vertical: true,
                                            align: "center",
                                            children: [
                                                item.status === 1 ? (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                    align: "center",
                                                    justify: "center",
                                                    style: {
                                                        width: 22,
                                                        height: 22,
                                                        borderRadius: "50%",
                                                        background: "#52c41a"
                                                    },
                                                    children: (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                        style: {
                                                            color: "#fff",
                                                            fontSize: 12
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 444,
                                                        columnNumber: 23
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 434,
                                                    columnNumber: 21
                                                }, void 0) : (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        width: 18,
                                                        height: 18,
                                                        borderRadius: "50%",
                                                        border: `2px solid ${item.priority === 3 ? "#ff4d4f" : item.priority === 2 ? "#faad14" : "#8c8c8c"}`
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 449,
                                                    columnNumber: 21
                                                }, void 0),
                                                (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        width: 2,
                                                        height: 24,
                                                        background: "#f0f0f0",
                                                        marginTop: 4
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 465,
                                                    columnNumber: 19
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 432,
                                            columnNumber: 17
                                        }, void 0),
                                        (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            vertical: true,
                                            style: {
                                                flex: 1
                                            },
                                            children: [
                                                (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontSize: 14,
                                                        fontWeight: item.priority === 3 ? 500 : "normal",
                                                        textDecoration: item.status === 1 ? "line-through" : "none",
                                                        color: item.status === 1 ? "#8c8c8c" : "#262626"
                                                    },
                                                    children: item.title
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 477,
                                                    columnNumber: 19
                                                }, void 0),
                                                (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    align: "center",
                                                    size: 6,
                                                    style: {
                                                        marginTop: 4
                                                    },
                                                    children: [
                                                        (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                                            style: {
                                                                fontSize: 12,
                                                                color: "#8c8c8c"
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                            lineNumber: 493,
                                                            columnNumber: 21
                                                        }, void 0),
                                                        (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            type: "secondary",
                                                            style: {
                                                                fontSize: 12
                                                            },
                                                            children: [
                                                                "创建于: ",
                                                                new Date(item.createdAt).toLocaleDateString('zh-CN')
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                            lineNumber: 499,
                                                            columnNumber: 21
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 492,
                                                    columnNumber: 19
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 476,
                                            columnNumber: 17
                                        }, void 0),
                                        (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                                            trigger: [
                                                'click'
                                            ],
                                            menu: {
                                                items: [
                                                    {
                                                        key: 'complete',
                                                        label: item.status === 1 ? '标记未完成' : '标记完成',
                                                        icon: (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                            style: {
                                                                color: item.status === 1 ? '#8c8c8c' : '#52c41a',
                                                                fontSize: 14
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                            lineNumber: 514,
                                                            columnNumber: 27
                                                        }, void 0)
                                                    },
                                                    {
                                                        key: 'edit',
                                                        label: '编辑任务',
                                                        icon: (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {
                                                            style: {
                                                                color: '#8c8c8c'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                            lineNumber: 525,
                                                            columnNumber: 31
                                                        }, void 0)
                                                    },
                                                    {
                                                        key: 'delete',
                                                        label: '删除任务',
                                                        icon: (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {
                                                            style: {
                                                                color: '#ff4d4f'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                            lineNumber: 530,
                                                            columnNumber: 31
                                                        }, void 0),
                                                        danger: true
                                                    }
                                                ],
                                                onClick: ({ key })=>{
                                                    if (key === "complete") handleToggleTodoStatus(item.id);
                                                    else if (key === "edit") {
                                                        setEditingTodoId(item.id);
                                                        todoForm.setFieldsValue({
                                                            name: item.title,
                                                            priority: item.priority
                                                        });
                                                        setTodoModalVisible(true);
                                                    } else if (key === "delete") handleDeleteTodo(item.id);
                                                }
                                            },
                                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                type: "text",
                                                size: "small",
                                                icon: (0, _jsxdevruntime.jsxDEV)(_icons.MoreOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 553,
                                                    columnNumber: 27
                                                }, void 0),
                                                style: {
                                                    width: 32,
                                                    height: 32
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                lineNumber: 550,
                                                columnNumber: 19
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 506,
                                            columnNumber: 17
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 430,
                                    columnNumber: 15
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                lineNumber: 410,
                                columnNumber: 13
                            }, void 0);
                        }
                    }, void 0, false, {
                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                        lineNumber: 406,
                        columnNumber: 11
                    }, this),
                    (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                        title: editingTodoId ? "编辑待办事项" : "新增待办事项",
                        open: todoModalVisible,
                        onCancel: ()=>{
                            setTodoModalVisible(false);
                            todoForm.resetFields();
                        },
                        onOk: ()=>{
                            todoForm.submit();
                        },
                        centered: true,
                        destroyOnClose: true,
                        footer: [
                            (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                onClick: ()=>setTodoModalVisible(false),
                                children: "取消"
                            }, "cancel", false, {
                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                lineNumber: 577,
                                columnNumber: 11
                            }, void 0),
                            (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "primary",
                                onClick: ()=>{
                                    todoForm.submit();
                                },
                                style: {
                                    background: "#1890ff",
                                    borderColor: "#1890ff",
                                    boxShadow: "0 2px 4px rgba(24, 144, 255, 0.3)"
                                },
                                children: editingTodoId ? "更新任务" : "创建任务"
                            }, "submit", false, {
                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                lineNumber: 580,
                                columnNumber: 11
                            }, void 0)
                        ],
                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                            form: todoForm,
                            layout: "vertical",
                            onFinish: handleAddOrUpdateTodo,
                            autoComplete: "off",
                            children: [
                                (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    name: "name",
                                    label: "任务名称",
                                    rules: [
                                        {
                                            required: true,
                                            message: "请输入任务名称"
                                        }
                                    ],
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        placeholder: "请输入任务名称",
                                        size: "large",
                                        style: {
                                            borderRadius: 6
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                        lineNumber: 607,
                                        columnNumber: 13
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 602,
                                    columnNumber: 11
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    name: "priority",
                                    label: "优先级",
                                    initialValue: 2,
                                    rules: [
                                        {
                                            required: true,
                                            message: "请选择优先级"
                                        }
                                    ],
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                        size: "large",
                                        options: [
                                            {
                                                value: 3,
                                                label: "高优先级"
                                            },
                                            {
                                                value: 2,
                                                label: "中优先级"
                                            },
                                            {
                                                value: 1,
                                                label: "低优先级"
                                            }
                                        ],
                                        style: {
                                            borderRadius: 6
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                        lineNumber: 620,
                                        columnNumber: 13
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 614,
                                    columnNumber: 11
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                            lineNumber: 596,
                            columnNumber: 9
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                        lineNumber: 564,
                        columnNumber: 7
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                lineNumber: 405,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
        lineNumber: 241,
        columnNumber: 5
    }, this);
};
_s(TodoManagement, "HHPhC8rlUAXUyFFDx+yADUZ6q1M=", false, function() {
    return [
        _antd.Form.useForm
    ];
});
_c = TodoManagement;
var _default = TodoManagement;
var _c;
$RefreshReg$(_c, "TodoManagement");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/test/profile-consolidated/UserProfileCard.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _user = __mako_require__("src/services/user.ts");
var _services = __mako_require__("src/services/index.ts");
var _max = __mako_require__("src/.umi/exports.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const { Step } = _antd.Steps;
const UserProfileCard = ()=>{
    var _subscriptionPlans_find;
    _s();
    const [userInfo, setUserInfo] = (0, _react.useState)({
        name: "",
        position: "",
        email: "",
        phone: "",
        telephone: "",
        registerDate: "",
        lastLoginTime: "",
        lastLoginTeam: "",
        teamCount: 0,
        avatar: ""
    });
    const [userInfoLoading, setUserInfoLoading] = (0, _react.useState)(true);
    const [userInfoError, setUserInfoError] = (0, _react.useState)(null);
    const [personalStats, setPersonalStats] = (0, _react.useState)({
        vehicles: 0,
        personnel: 0,
        warnings: 0,
        alerts: 0
    });
    const [statsLoading, setStatsLoading] = (0, _react.useState)(true);
    const [statsError, setStatsError] = (0, _react.useState)(null);
    const subscriptionPlans = [
        {
            id: "basic",
            name: "基础版",
            price: 0,
            description: "适合小团队使用",
            features: [
                "最多5个团队",
                "最多20辆车辆",
                "基础安全监控",
                "基本报告功能"
            ]
        },
        {
            id: "professional",
            name: "专业版",
            price: 199,
            description: "适合中小型企业",
            features: [
                "最多20个团队",
                "最多100辆车辆",
                "高级安全监控",
                "详细分析报告",
                "设备状态预警",
                "优先技术支持"
            ]
        },
        {
            id: "enterprise",
            name: "企业版",
            price: 499,
            description: "适合大型企业",
            features: [
                "不限团队数量",
                "不限车辆数量",
                "AI安全分析",
                "实时监控告警",
                "定制化报告",
                "专属客户经理",
                "24/7技术支持"
            ]
        }
    ];
    const currentSubscription = {
        planId: "basic",
        expires: "2025-12-31"
    };
    const [editProfileModalVisible, setEditProfileModalVisible] = (0, _react.useState)(false);
    const [subscriptionModalVisible, setSubscriptionModalVisible] = (0, _react.useState)(false);
    const [logoutModalVisible, setLogoutModalVisible] = (0, _react.useState)(false);
    const [logoutLoading, setLogoutLoading] = (0, _react.useState)(false);
    const [currentStep, setCurrentStep] = (0, _react.useState)(0);
    const [editProfileForm] = _antd.Form.useForm();
    const { setInitialState } = (0, _max.useModel)('@@initialState');
    (0, _react.useEffect)(()=>{
        console.log('UserProfileCard: useEffect 开始执行');
        const fetchUserData = async ()=>{
            try {
                console.log('UserProfileCard: 开始获取用户数据');
                const userDetailPromise = _user.UserService.getUserProfileDetail().catch((error)=>{
                    console.error('获取用户详细信息失败:', error);
                    setUserInfoError('获取用户详细信息失败，请稍后重试');
                    return null;
                });
                const statsPromise = _user.UserService.getUserPersonalStats().catch((error)=>{
                    console.error('获取统计数据失败:', error);
                    setStatsError('获取统计数据失败，请稍后重试');
                    return null;
                });
                const [userDetail, stats] = await Promise.all([
                    userDetailPromise,
                    statsPromise
                ]);
                if (userDetail) {
                    console.log('UserProfileCard: 获取到用户详细信息:', userDetail);
                    setUserInfo(userDetail);
                    setUserInfoError(null);
                }
                if (stats) {
                    console.log('UserProfileCard: 获取到统计数据:', stats);
                    setPersonalStats(stats);
                    setStatsError(null);
                }
            } catch (error) {
                console.error('获取用户数据时发生未知错误:', error);
                setUserInfoError('获取用户数据失败，请刷新页面重试');
                setStatsError('获取统计数据失败，请刷新页面重试');
            } finally{
                setUserInfoLoading(false);
                setStatsLoading(false);
            }
        };
        fetchUserData();
    }, []);
    const handleLogout = async ()=>{
        try {
            setLogoutLoading(true);
            await _services.AuthService.logout();
            if (setInitialState) await setInitialState({
                currentUser: undefined,
                currentTeam: undefined
            });
            _max.history.push('/user/login');
        } catch (error) {
            console.error('退出登录失败:', error);
            if (setInitialState) await setInitialState({
                currentUser: undefined,
                currentTeam: undefined
            });
            _max.history.push('/user/login');
        } finally{
            setLogoutLoading(false);
            setLogoutModalVisible(false);
        }
    };
    return (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
        children: [
            (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                className: "dashboard-card",
                styles: {
                    body: {
                        padding: 32
                    }
                },
                children: userInfoError ? (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                    message: "用户信息加载失败",
                    description: userInfoError,
                    type: "error",
                    showIcon: true,
                    style: {
                        marginBottom: 24
                    }
                }, void 0, false, {
                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                    lineNumber: 217,
                    columnNumber: 11
                }, this) : (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                    spinning: userInfoLoading,
                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        style: {
                            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                            borderRadius: 16,
                            color: "white",
                            position: "relative",
                            overflow: "hidden",
                            height: 140,
                            border: "none"
                        },
                        styles: {
                            body: {
                                padding: 24,
                                height: "100%"
                            }
                        },
                        children: [
                            (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                style: {
                                    position: "absolute",
                                    top: 16,
                                    right: 16,
                                    zIndex: 20
                                },
                                size: 8,
                                children: [
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                        title: "设置",
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                                            menu: {
                                                items: [
                                                    {
                                                        key: "editProfile",
                                                        icon: (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 255,
                                                            columnNumber: 33
                                                        }, void 0),
                                                        label: "修改资料",
                                                        onClick: ()=>{
                                                            setEditProfileModalVisible(true);
                                                            setCurrentStep(0);
                                                            editProfileForm.setFieldsValue({
                                                                name: userInfo.name,
                                                                email: userInfo.email,
                                                                telephone: userInfo.phone || userInfo.telephone
                                                            });
                                                        }
                                                    },
                                                    {
                                                        key: "subscription",
                                                        icon: (0, _jsxdevruntime.jsxDEV)(_icons.TagOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 269,
                                                            columnNumber: 33
                                                        }, void 0),
                                                        label: "订阅套餐",
                                                        onClick: ()=>setSubscriptionModalVisible(true)
                                                    }
                                                ]
                                            },
                                            trigger: [
                                                "click"
                                            ],
                                            placement: "bottomRight",
                                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                type: "text",
                                                shape: "circle",
                                                icon: (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 281,
                                                    columnNumber: 29
                                                }, void 0),
                                                style: {
                                                    color: "rgba(255,255,255,0.9)",
                                                    backgroundColor: "rgba(255,255,255,0.15)",
                                                    border: "none",
                                                    transition: "all 0.2s"
                                                },
                                                onMouseEnter: (e)=>{
                                                    e.currentTarget.style.backgroundColor = "rgba(255,255,255,0.25)";
                                                    e.currentTarget.style.color = "white";
                                                },
                                                onMouseLeave: (e)=>{
                                                    e.currentTarget.style.backgroundColor = "rgba(255,255,255,0.15)";
                                                    e.currentTarget.style.color = "rgba(255,255,255,0.9)";
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 278,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 250,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 249,
                                        columnNumber: 17
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                        title: "退出登录",
                                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "text",
                                            shape: "circle",
                                            icon: (0, _jsxdevruntime.jsxDEV)(_icons.LogoutOutlined, {}, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 304,
                                                columnNumber: 27
                                            }, void 0),
                                            onClick: ()=>setLogoutModalVisible(true),
                                            style: {
                                                color: "rgba(255,255,255,0.9)",
                                                backgroundColor: "rgba(255,255,255,0.15)",
                                                border: "none",
                                                transition: "all 0.2s"
                                            },
                                            onMouseEnter: (e)=>{
                                                e.currentTarget.style.backgroundColor = "rgba(255,77,79,0.3)";
                                                e.currentTarget.style.color = "#ff4d4f";
                                            },
                                            onMouseLeave: (e)=>{
                                                e.currentTarget.style.backgroundColor = "rgba(255,255,255,0.15)";
                                                e.currentTarget.style.color = "rgba(255,255,255,0.9)";
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 301,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 300,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                lineNumber: 240,
                                columnNumber: 15
                            }, this),
                            (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    position: "absolute",
                                    top: -25,
                                    right: -25,
                                    width: 100,
                                    height: 100,
                                    background: "rgba(255,255,255,0.1)",
                                    borderRadius: "50%"
                                }
                            }, void 0, false, {
                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                lineNumber: 324,
                                columnNumber: 15
                            }, this),
                            (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    position: "absolute",
                                    bottom: -30,
                                    left: -30,
                                    width: 80,
                                    height: 80,
                                    background: "rgba(255,255,255,0.05)",
                                    borderRadius: "50%"
                                }
                            }, void 0, false, {
                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                lineNumber: 335,
                                columnNumber: 15
                            }, this),
                            (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    position: "absolute",
                                    top: "50%",
                                    right: "20%",
                                    width: 60,
                                    height: 60,
                                    background: "rgba(255,255,255,0.03)",
                                    borderRadius: "50%",
                                    transform: "translateY(-50%)"
                                }
                            }, void 0, false, {
                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                lineNumber: 346,
                                columnNumber: 15
                            }, this),
                            (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                align: "center",
                                gap: 32,
                                style: {
                                    position: "relative",
                                    zIndex: 1,
                                    width: "100%"
                                },
                                children: [
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        align: "center",
                                        style: {
                                            flex: "0 0 320px",
                                            minWidth: 320
                                        },
                                        children: [
                                            (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                size: 64,
                                                shape: "square",
                                                style: {
                                                    backgroundColor: "rgba(255,255,255,0.2)",
                                                    marginRight: 20,
                                                    fontSize: 24,
                                                    fontWeight: 600,
                                                    border: "2px solid rgba(255,255,255,0.3)"
                                                },
                                                children: userInfo.name ? userInfo.name.charAt(0).toUpperCase() : (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 379,
                                                    columnNumber: 78
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 368,
                                                columnNumber: 19
                                            }, this),
                                            (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                direction: "vertical",
                                                size: 4,
                                                children: [
                                                    (0, _jsxdevruntime.jsxDEV)(Title, {
                                                        level: 3,
                                                        style: {
                                                            margin: 0,
                                                            color: "white",
                                                            fontSize: 22,
                                                            fontWeight: 600
                                                        },
                                                        children: userInfo.name || "加载中..."
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 384,
                                                        columnNumber: 21
                                                    }, this),
                                                    (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                        direction: "vertical",
                                                        size: 4,
                                                        children: [
                                                            userInfo.email && (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                size: 6,
                                                                align: "center",
                                                                children: [
                                                                    (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {
                                                                        style: {
                                                                            fontSize: 13,
                                                                            color: "rgba(255,255,255,0.9)"
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 400,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        style: {
                                                                            color: "rgba(255,255,255,0.9)",
                                                                            fontSize: 12
                                                                        },
                                                                        children: userInfo.email
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 401,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 399,
                                                                columnNumber: 25
                                                            }, this),
                                                            (userInfo.phone || userInfo.telephone) && (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                size: 6,
                                                                align: "center",
                                                                children: [
                                                                    (0, _jsxdevruntime.jsxDEV)(_icons.PhoneOutlined, {
                                                                        style: {
                                                                            fontSize: 13,
                                                                            color: "rgba(255,255,255,0.9)"
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 408,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        style: {
                                                                            color: "rgba(255,255,255,0.9)",
                                                                            fontSize: 12
                                                                        },
                                                                        children: userInfo.phone || userInfo.telephone
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                        lineNumber: 409,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                lineNumber: 407,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 397,
                                                        columnNumber: 21
                                                    }, this),
                                                    userInfo.registerDate && (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 13,
                                                            color: "rgba(255,255,255,0.8)",
                                                            fontWeight: 500
                                                        },
                                                        children: [
                                                            "注册于 ",
                                                            userInfo.registerDate
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 418,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 383,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 366,
                                        columnNumber: 17
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                        type: "vertical",
                                        style: {
                                            height: "80px",
                                            borderColor: "rgba(255,255,255,0.3)",
                                            margin: "0 16px"
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 426,
                                        columnNumber: 17
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        vertical: true,
                                        style: {
                                            flex: "1 1 auto"
                                        },
                                        children: [
                                            (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                align: "center",
                                                style: {
                                                    justifyContent: "center",
                                                    marginBottom: 30,
                                                    height: 20
                                                },
                                                children: [
                                                    (0, _jsxdevruntime.jsxDEV)(_icons.BarChartOutlined, {
                                                        style: {
                                                            fontSize: 16,
                                                            color: "rgba(255,255,255,0.9)"
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 451,
                                                        columnNumber: 21
                                                    }, this),
                                                    (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            color: "rgba(255,255,255,0.9)",
                                                            fontSize: 14,
                                                            fontWeight: 600,
                                                            lineHeight: 1
                                                        },
                                                        children: "数据概览"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 457,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 443,
                                                columnNumber: 19
                                            }, this),
                                            statsError ? (0, _jsxdevruntime.jsxDEV)(Text, {
                                                style: {
                                                    fontSize: 12,
                                                    color: "rgba(255,255,255,0.8)"
                                                },
                                                children: "数据加载失败"
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 463,
                                                columnNumber: 21
                                            }, this) : (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                                spinning: statsLoading,
                                                children: (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                    justify: "space-around",
                                                    align: "center",
                                                    children: [
                                                        (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                textAlign: 'center'
                                                            },
                                                            children: [
                                                                (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 20,
                                                                        fontWeight: 700,
                                                                        color: "white",
                                                                        lineHeight: 1
                                                                    },
                                                                    children: personalStats.vehicles
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 471,
                                                                    columnNumber: 27
                                                                }, this),
                                                                (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 13,
                                                                        color: "rgba(255,255,255,0.8)",
                                                                        marginTop: 3
                                                                    },
                                                                    children: "车辆"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 479,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 470,
                                                            columnNumber: 25
                                                        }, this),
                                                        (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                textAlign: 'center'
                                                            },
                                                            children: [
                                                                (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 20,
                                                                        fontWeight: 700,
                                                                        color: "white",
                                                                        lineHeight: 1
                                                                    },
                                                                    children: personalStats.personnel
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 488,
                                                                    columnNumber: 27
                                                                }, this),
                                                                (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 13,
                                                                        color: "rgba(255,255,255,0.8)",
                                                                        marginTop: 3
                                                                    },
                                                                    children: "人员"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 496,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 487,
                                                            columnNumber: 25
                                                        }, this),
                                                        (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                textAlign: 'center'
                                                            },
                                                            children: [
                                                                (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 20,
                                                                        fontWeight: 700,
                                                                        color: "white",
                                                                        lineHeight: 1
                                                                    },
                                                                    children: personalStats.warnings
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 505,
                                                                    columnNumber: 27
                                                                }, this),
                                                                (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 13,
                                                                        color: "rgba(255,255,255,0.8)",
                                                                        marginTop: 3
                                                                    },
                                                                    children: "预警"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 513,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 504,
                                                            columnNumber: 25
                                                        }, this),
                                                        (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                textAlign: 'center'
                                                            },
                                                            children: [
                                                                (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 20,
                                                                        fontWeight: 700,
                                                                        color: "white",
                                                                        lineHeight: 1
                                                                    },
                                                                    children: personalStats.alerts
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 522,
                                                                    columnNumber: 27
                                                                }, this),
                                                                (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 13,
                                                                        color: "rgba(255,255,255,0.8)",
                                                                        marginTop: 3
                                                                    },
                                                                    children: "告警"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                                    lineNumber: 530,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 521,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 469,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 467,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 436,
                                        columnNumber: 17
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                        type: "vertical",
                                        style: {
                                            height: "80px",
                                            borderColor: "rgba(255,255,255,0.3)",
                                            margin: "0 16px"
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 544,
                                        columnNumber: 17
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        direction: "vertical",
                                        size: 10,
                                        style: {
                                            flex: "0 0 300px",
                                            paddingLeft: 20,
                                            width: 300,
                                            minWidth: 300
                                        },
                                        children: [
                                            (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                direction: "vertical",
                                                size: 4,
                                                children: [
                                                    (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 12,
                                                            color: "rgba(255,255,255,0.8)",
                                                            fontWeight: 500
                                                        },
                                                        children: "最后登录时间"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 565,
                                                        columnNumber: 21
                                                    }, this),
                                                    (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 14,
                                                            color: "white",
                                                            fontWeight: 600,
                                                            lineHeight: 1.3
                                                        },
                                                        children: userInfo.lastLoginTime || "暂无记录"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 568,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 564,
                                                columnNumber: 19
                                            }, this),
                                            (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                direction: "vertical",
                                                size: 4,
                                                children: [
                                                    (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 12,
                                                            color: "rgba(255,255,255,0.8)",
                                                            fontWeight: 500
                                                        },
                                                        children: "最后登录团队"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 573,
                                                        columnNumber: 21
                                                    }, this),
                                                    (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 14,
                                                            color: "white",
                                                            fontWeight: 600,
                                                            lineHeight: 1.3
                                                        },
                                                        children: userInfo.lastLoginTeam || "暂无记录"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                        lineNumber: 576,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                lineNumber: 572,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 554,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                lineNumber: 360,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                        lineNumber: 227,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                    lineNumber: 225,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                lineNumber: 210,
                columnNumber: 7
            }, this),
            (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "修改个人资料",
                open: editProfileModalVisible,
                onCancel: ()=>{
                    setEditProfileModalVisible(false);
                    setCurrentStep(0);
                },
                footer: [
                    currentStep === 1 && (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        onClick: ()=>setCurrentStep(0),
                        children: "上一步"
                    }, "back", false, {
                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                        lineNumber: 597,
                        columnNumber: 13
                    }, void 0),
                    (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "primary",
                        onClick: ()=>{
                            if (currentStep === 0) editProfileForm.validateFields().then(()=>{
                                setCurrentStep(1);
                            });
                            else editProfileForm.validateFields().then((values)=>{
                                console.log("个人资料表单值:", values);
                                setEditProfileModalVisible(false);
                                setCurrentStep(0);
                            });
                        },
                        children: currentStep === 0 ? "下一步" : "确定"
                    }, "submit", false, {
                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                        lineNumber: 601,
                        columnNumber: 11
                    }, void 0)
                ],
                children: [
                    (0, _jsxdevruntime.jsxDEV)(_antd.Steps, {
                        current: currentStep,
                        style: {
                            marginBottom: 16
                        },
                        children: [
                            (0, _jsxdevruntime.jsxDEV)(Step, {
                                title: "填写信息"
                            }, void 0, false, {
                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                lineNumber: 624,
                                columnNumber: 11
                            }, this),
                            (0, _jsxdevruntime.jsxDEV)(Step, {
                                title: "安全验证"
                            }, void 0, false, {
                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                lineNumber: 625,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                        lineNumber: 623,
                        columnNumber: 9
                    }, this),
                    (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                        form: editProfileForm,
                        layout: "vertical",
                        requiredMark: false,
                        children: currentStep === 0 ? (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                            children: [
                                (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    name: "name",
                                    label: "用户名",
                                    rules: [
                                        {
                                            required: true,
                                            message: "请输入用户名"
                                        }
                                    ],
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        placeholder: "请输入用户名"
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 636,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 631,
                                    columnNumber: 15
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    name: "email",
                                    label: "邮箱",
                                    rules: [
                                        {
                                            required: true,
                                            message: "请输入邮箱地址"
                                        },
                                        {
                                            type: "email",
                                            message: "请输入有效的邮箱地址"
                                        }
                                    ],
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        placeholder: "请输入邮箱地址"
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 646,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 638,
                                    columnNumber: 15
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    name: "telephone",
                                    label: "手机号",
                                    rules: [
                                        {
                                            required: true,
                                            message: "请输入手机号"
                                        },
                                        {
                                            pattern: /^1\d{10}$/,
                                            message: "请输入有效的手机号"
                                        }
                                    ],
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        placeholder: "请输入手机号"
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 656,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 648,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true) : (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            direction: "vertical",
                            align: "center",
                            style: {
                                width: "100%"
                            },
                            children: [
                                (0, _jsxdevruntime.jsxDEV)(Text, {
                                    style: {
                                        margin: "12px 0"
                                    },
                                    children: [
                                        "验证码已发送至您的手机号",
                                        " ",
                                        (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: editProfileForm.getFieldValue("telephone")
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 664,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 662,
                                    columnNumber: 15
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    name: "verificationCode",
                                    label: "验证码",
                                    rules: [
                                        {
                                            required: true,
                                            message: "请输入验证码"
                                        }
                                    ],
                                    style: {
                                        textAlign: "center"
                                    },
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        placeholder: "请输入6位验证码",
                                        maxLength: 6,
                                        style: {
                                            width: "50%",
                                            textAlign: "center"
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                        lineNumber: 672,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 666,
                                    columnNumber: 15
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "link",
                                    style: {
                                        padding: 0
                                    },
                                    children: "重新发送验证码"
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 678,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 661,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                        lineNumber: 628,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                lineNumber: 588,
                columnNumber: 7
            }, this),
            (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "订阅套餐",
                open: subscriptionModalVisible,
                onCancel: ()=>setSubscriptionModalVisible(false),
                footer: null,
                width: 800,
                children: [
                    (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        size: "small",
                        style: {
                            background: "#f9f9f9",
                            marginBottom: 16
                        },
                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                            justify: "space-between",
                            align: "center",
                            children: [
                                (0, _jsxdevruntime.jsxDEV)(Text, {
                                    strong: true,
                                    children: "当前套餐: "
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 703,
                                    columnNumber: 13
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                    color: "green",
                                    style: {
                                        marginLeft: 8,
                                        fontSize: 13
                                    },
                                    children: (_subscriptionPlans_find = subscriptionPlans.find((p)=>p.id === currentSubscription.planId)) === null || _subscriptionPlans_find === void 0 ? void 0 : _subscriptionPlans_find.name
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 704,
                                    columnNumber: 13
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    children: [
                                        "到期时间: ",
                                        currentSubscription.expires
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 711,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 702,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                        lineNumber: 695,
                        columnNumber: 9
                    }, this),
                    (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                        gutter: 24,
                        children: subscriptionPlans.map((plan)=>(0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 8,
                                children: (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                    style: {
                                        height: "100%",
                                        border: `1px solid ${plan.id === currentSubscription.planId ? "#52c41a" : "#d9d9d9"}`,
                                        position: "relative"
                                    },
                                    styles: {
                                        body: {
                                            padding: 16
                                        }
                                    },
                                    children: [
                                        plan.id === currentSubscription.planId && (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                            color: "green",
                                            style: {
                                                position: "absolute",
                                                top: -10,
                                                right: -10,
                                                borderRadius: 2,
                                                boxShadow: "0 2px 8px rgba(0,0,0,0.1)"
                                            },
                                            children: "当前套餐"
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 734,
                                            columnNumber: 19
                                        }, this),
                                        (0, _jsxdevruntime.jsxDEV)(Title, {
                                            level: 4,
                                            style: {
                                                textAlign: "center",
                                                margin: "12px 0 8px"
                                            },
                                            children: plan.name
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 747,
                                            columnNumber: 17
                                        }, this),
                                        (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            vertical: true,
                                            align: "center",
                                            style: {
                                                marginBottom: 12
                                            },
                                            children: [
                                                plan.price > 0 ? (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                    children: [
                                                        (0, _jsxdevruntime.jsxDEV)(Title, {
                                                            level: 2,
                                                            style: {
                                                                marginBottom: 0
                                                            },
                                                            children: [
                                                                "¥",
                                                                plan.price
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 756,
                                                            columnNumber: 23
                                                        }, this),
                                                        (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            type: "secondary",
                                                            children: "/月"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 759,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true) : (0, _jsxdevruntime.jsxDEV)(Title, {
                                                    level: 2,
                                                    style: {
                                                        color: "#52c41a",
                                                        marginBottom: 0
                                                    },
                                                    children: "免费"
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 762,
                                                    columnNumber: 21
                                                }, this),
                                                (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    type: "secondary",
                                                    style: {
                                                        marginTop: 4
                                                    },
                                                    children: plan.description
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 769,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 753,
                                            columnNumber: 17
                                        }, this),
                                        (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            direction: "vertical",
                                            size: 6,
                                            style: {
                                                minHeight: 170,
                                                width: "100%"
                                            },
                                            children: plan.features.map((feature, index)=>(0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    align: "start",
                                                    children: [
                                                        (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                            style: {
                                                                color: "#52c41a",
                                                                marginTop: 4
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 780,
                                                            columnNumber: 23
                                                        }, this),
                                                        (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            children: feature
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                            lineNumber: 786,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, index, true, {
                                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                                    lineNumber: 779,
                                                    columnNumber: 21
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 777,
                                            columnNumber: 17
                                        }, this),
                                        plan.id !== currentSubscription.planId ? (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            block: true,
                                            style: {
                                                marginTop: 12,
                                                boxShadow: "0 2px 8px rgba(24, 144, 255, 0.3)"
                                            },
                                            onClick: ()=>{
                                                console.log("选择套餐:", plan);
                                                setSubscriptionModalVisible(false);
                                            },
                                            children: "立即订阅"
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 792,
                                            columnNumber: 19
                                        }, this) : (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            block: true,
                                            style: {
                                                marginTop: 12,
                                                background: "#f6ffed",
                                                borderColor: "#b7eb8f",
                                                color: "#389e0d"
                                            },
                                            disabled: true,
                                            children: "当前套餐"
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                            lineNumber: 807,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                    lineNumber: 721,
                                    columnNumber: 15
                                }, this)
                            }, plan.id, false, {
                                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                                lineNumber: 719,
                                columnNumber: 13
                            }, this))
                    }, void 0, false, {
                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                        lineNumber: 717,
                        columnNumber: 9
                    }, this),
                    (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                        justify: "center",
                        style: {
                            marginTop: 20
                        },
                        children: (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            children: "订阅服务自动续费，可随时取消"
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 826,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                        lineNumber: 825,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                lineNumber: 687,
                columnNumber: 7
            }, this),
            (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "确认退出登录",
                open: logoutModalVisible,
                onCancel: ()=>setLogoutModalVisible(false),
                footer: [
                    (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        onClick: ()=>setLogoutModalVisible(false),
                        children: "取消"
                    }, "cancel", false, {
                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                        lineNumber: 836,
                        columnNumber: 11
                    }, void 0),
                    (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "primary",
                        danger: true,
                        loading: logoutLoading,
                        onClick: handleLogout,
                        children: "确认退出"
                    }, "confirm", false, {
                        fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                        lineNumber: 839,
                        columnNumber: 11
                    }, void 0)
                ],
                width: 400,
                children: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    direction: "vertical",
                    align: "center",
                    style: {
                        width: "100%",
                        padding: "20px 0"
                    },
                    children: [
                        (0, _jsxdevruntime.jsxDEV)(_icons.LogoutOutlined, {
                            style: {
                                fontSize: 48,
                                color: '#ff4d4f'
                            }
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 853,
                            columnNumber: 11
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(Text, {
                            strong: true,
                            style: {
                                fontSize: 16
                            },
                            children: "您确定要退出登录吗？"
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 854,
                            columnNumber: 11
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            style: {
                                textAlign: "center"
                            },
                            children: "退出后您需要重新登录才能继续使用系统"
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                            lineNumber: 855,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                    lineNumber: 852,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/test/profile-consolidated/UserProfileCard.tsx",
                lineNumber: 831,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
_s(UserProfileCard, "2csI36bGbcIUYJyMcENjxkdXqJY=", false, function() {
    return [
        _antd.Form.useForm,
        _max.useModel
    ];
});
_c = UserProfileCard;
var _default = UserProfileCard;
var _c;
$RefreshReg$(_c, "UserProfileCard");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/test/profile-consolidated/index.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _TodoManagement = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/test/profile-consolidated/TodoManagement.tsx"));
var _TeamListCard = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/test/profile-consolidated/TeamListCard.tsx"));
var _UserProfileCard = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/test/profile-consolidated/UserProfileCard.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
const FleetManagementDashboard = ()=>{
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        style: {
            minHeight: "100vh",
            background: "#f5f8ff"
        },
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                gutter: [
                    24,
                    24
                ],
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                    xs: 24,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UserProfileCard.default, {}, void 0, false, {
                        fileName: "src/pages/test/profile-consolidated/index.tsx",
                        lineNumber: 14,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/test/profile-consolidated/index.tsx",
                    lineNumber: 13,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/test/profile-consolidated/index.tsx",
                lineNumber: 11,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                gutter: [
                    24,
                    24
                ],
                style: {
                    marginTop: 16
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        lg: 12,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TodoManagement.default, {}, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/index.tsx",
                            lineNumber: 21,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/test/profile-consolidated/index.tsx",
                        lineNumber: 20,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        lg: 12,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamListCard.default, {}, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/index.tsx",
                            lineNumber: 26,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/test/profile-consolidated/index.tsx",
                        lineNumber: 25,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/test/profile-consolidated/index.tsx",
                lineNumber: 18,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/test/profile-consolidated/index.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, this);
};
_c = FleetManagementDashboard;
var _default = FleetManagementDashboard;
var _c;
$RefreshReg$(_c, "FleetManagementDashboard");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/services/todo.ts": function (module, exports, __mako_require__){
/**
 * TODO服务
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "TodoService", {
    enumerable: true,
    get: function() {
        return TodoService;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _request = __mako_require__("src/utils/request.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
class TodoService {
    /**
   * 获取用户的TODO列表
   */ static async getUserTodos() {
        const response = await _request.apiRequest.get('/todos');
        return response.data;
    }
    /**
   * 创建TODO
   */ static async createTodo(request) {
        const response = await _request.apiRequest.post('/todos', request);
        return response.data;
    }
    /**
   * 更新TODO
   */ static async updateTodo(id, request) {
        const response = await _request.apiRequest.put(`/todos/${id}`, request);
        return response.data;
    }
    /**
   * 删除TODO
   */ static async deleteTodo(id) {
        await _request.apiRequest.delete(`/todos/${id}`);
    }
    /**
   * 获取TODO统计信息
   */ static async getTodoStats() {
        const response = await _request.apiRequest.get('/todos/stats');
        return response.data;
    }
}
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src_pages_test_profile-consolidated_index_tsx-async.js.map