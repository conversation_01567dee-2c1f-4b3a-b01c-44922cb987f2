/**
 * 服务层统一入口
 */

// 导出所有服务
export { AuthService } from './auth';
export { TeamService } from './team';
export { UserService } from './user';
export { SubscriptionService } from './subscription';
export { FriendService } from './friend';

// 导出默认服务实例
export { default as authService } from './auth';
export { default as teamService } from './team';
export { default as userService } from './user';
export { default as subscriptionService } from './subscription';
export { default as friendService } from './friend';

// 导出请求工具
export { apiRequest, TokenManager } from '@/utils/request';

// 导出类型定义
export * from '@/types/api';
