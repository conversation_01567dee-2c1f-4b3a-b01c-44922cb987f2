{"version": 3, "sources": ["src/pages/user/index.tsx"], "sourcesContent": ["/**\n * 用户管理页面 - 个人资料管理\n */\n\nimport React from 'react';\nimport {\n  Card,\n  Typography\n} from 'antd';\nimport { PageContainer } from '@ant-design/pro-components';\n\n// 导入原有的组件内容\nimport UserProfileContent from './components/UserProfileContent';\n\nconst { Title } = Typography;\n\nconst UserManagePage: React.FC = () => {\n  return (\n    <PageContainer title=\"用户管理\">\n      <Card>\n        <UserProfileContent />\n      </Card>\n    </PageContainer>\n  );\n};\n\nexport default UserManagePage;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BAwBD;;;eAAA;;;;;;;uEAtBkB;6BAIX;sCACuB;oFAGC;;;;;;;;;AAE/B,MAAM,EAAE,KAAK,EAAE,GAAG,gBAAU;AAE5B,MAAM,iBAA2B;IAC/B,qBACE,2BAAC,4BAAa;QAAC,OAAM;kBACnB,cAAA,2BAAC,UAAI;sBACH,cAAA,2BAAC,2BAAkB;;;;;;;;;;;;;;;AAI3B;KARM;IAUN,WAAe"}