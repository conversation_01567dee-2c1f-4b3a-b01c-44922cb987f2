/**
 * 团队详情页面
 */

import React, { useState, useEffect } from 'react';
import {
  Typography,
  message,
  Spin
} from 'antd';
import { PageContainer } from '@ant-design/pro-components';
import { TeamService } from '@/services';
import type { TeamDetailResponse } from '@/types/api';
import TeamDetailContent from './components/TeamDetailContent';

const { Text } = Typography;

const TeamDetailPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);

  useEffect(() => {
    fetchTeamDetail();
  }, []);

  const fetchTeamDetail = async () => {
    try {
      setLoading(true);
      const detail = await TeamService.getCurrentTeamDetail();
      setTeamDetail(detail);
    } catch (error) {
      console.error('获取团队详情失败:', error);
      message.error('获取团队详情失败');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <PageContainer>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
        </div>
      </PageContainer>
    );
  }

  if (!teamDetail) {
    return (
      <PageContainer>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Text type="secondary">团队信息加载失败</Text>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer style={{ background: '#f5f5f5' }}>
      <TeamDetailContent
        teamDetail={teamDetail}
        loading={loading}
        onRefresh={fetchTeamDetail}
        showBackButton={true}
      />
    </PageContainer>
  );
};

export default TeamDetailPage;
