/**
 * 用户资料内容组件
 */

import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Space,
  Typography,
  message,
  Avatar,
  Upload
} from 'antd';
import {
  UserOutlined,
  EditOutlined,
  MailOutlined,
  SaveOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { UserService } from '@/services';
import type { UserProfileResponse, UpdateUserProfileRequest } from '@/types/api';

const { Title, Text } = Typography;

const UserProfileContent: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editing, setEditing] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfileResponse | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchUserProfile();
  }, []);

  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      const profile = await UserService.getUserProfile();
      setUserProfile(profile);
      form.setFieldsValue({
        name: profile.name,
        email: profile.email,
      });
    } catch (error) {
      console.error('获取用户资料失败:', error);
      message.error('获取用户资料失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveProfile = async (values: any) => {
    try {
      setSaving(true);
      const updateData: UpdateUserProfileRequest = {
        name: values.name,
      };

      const updatedProfile = await UserService.updateUserProfile(updateData);
      setUserProfile(updatedProfile);
      setEditing(false);
      message.success('个人资料更新成功');
    } catch (error) {
      console.error('更新个人资料失败:', error);
      message.error('更新个人资料失败');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setEditing(false);
    if (userProfile) {
      form.setFieldsValue({
        name: userProfile.name,
        email: userProfile.email,
      });
    }
  };

  if (loading || !userProfile) {
    return <div>加载中...</div>;
  }

  return (
    <div>
      <div style={{ display: 'flex', alignItems: 'flex-start', gap: 24 }}>
        {/* 头像部分 */}
        <div style={{ textAlign: 'center' }}>
          <Avatar size={120} icon={<UserOutlined />} />
          <div style={{ marginTop: 16 }}>
            <Upload
              showUploadList={false}
              beforeUpload={() => {
                message.info('头像上传功能暂未实现');
                return false;
              }}
            >
              <Button icon={<UploadOutlined />} size="small">
                更换头像
              </Button>
            </Upload>
          </div>
        </div>

        {/* 表单部分 */}
        <div style={{ flex: 1 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
            <Title level={4} style={{ margin: 0 }}>
              <UserOutlined /> 基本信息
            </Title>
            {!editing && (
              <Button
                type="primary"
                icon={<EditOutlined />}
                onClick={() => setEditing(true)}
              >
                编辑资料
              </Button>
            )}
          </div>

          <Form
            form={form}
            layout="vertical"
            onFinish={handleSaveProfile}
            disabled={!editing}
          >
            <Form.Item
              label="用户名"
              name="name"
              rules={[
                { required: true, message: '请输入用户名' },
                { max: 100, message: '用户名不能超过100个字符' }
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="请输入用户名"
              />
            </Form.Item>

            <Form.Item
              label="邮箱地址"
              name="email"
            >
              <Input
                prefix={<MailOutlined />}
                disabled
                placeholder="邮箱地址不可修改"
              />
            </Form.Item>

            <Form.Item>
              <Space>
                {editing ? (
                  <>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={saving}
                      icon={<SaveOutlined />}
                    >
                      保存修改
                    </Button>
                    <Button onClick={handleCancel}>
                      取消
                    </Button>
                  </>
                ) : (
                  <Button
                    type="primary"
                    icon={<EditOutlined />}
                    onClick={() => setEditing(true)}
                  >
                    编辑资料
                  </Button>
                )}
              </Space>
            </Form.Item>
          </Form>
        </div>
      </div>


    </div>
  );
};

export default UserProfileContent;
