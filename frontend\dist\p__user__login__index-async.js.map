{"version": 3, "sources": ["src/pages/user/login/index.tsx"], "sourcesContent": ["/**\r\n * 登录页面\r\n * 实现双阶段认证的第一阶段：账号登录\r\n */\r\n\r\nimport React, { useState } from 'react';\r\nimport { Button, Form, Input, message, Tabs, Card, Space, Typography } from 'antd';\r\nimport { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';\r\nimport { history, Helmet } from '@umijs/max';\r\nimport { createStyles } from 'antd-style';\r\nimport { AuthService } from '@/services';\r\nimport type { LoginRequest, RegisterRequest } from '@/types/api';\r\nimport { Footer } from '@/components';\r\nimport Settings from '../../../../config/defaultSettings';\r\n\r\nconst { Title, Text } = Typography;\r\n\r\nconst useStyles = createStyles(({ token }) => {\r\n  return {\r\n    container: {\r\n      display: 'flex',\r\n      flexDirection: 'column',\r\n      height: '100vh',\r\n      overflow: 'auto',\r\n      backgroundImage:\r\n        \"url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')\",\r\n      backgroundSize: '100% 100%',\r\n    },\r\n    content: {\r\n      flex: 1,\r\n      display: 'flex',\r\n      flexDirection: 'column',\r\n      justifyContent: 'center',\r\n      alignItems: 'center',\r\n      padding: '32px 16px',\r\n    },\r\n    header: {\r\n      marginBottom: 40,\r\n      textAlign: 'center',\r\n    },\r\n    logo: {\r\n      marginBottom: 16,\r\n    },\r\n    title: {\r\n      marginBottom: 0,\r\n    },\r\n    loginCard: {\r\n      width: '100%',\r\n      maxWidth: 400,\r\n      boxShadow: token.boxShadowTertiary,\r\n    },\r\n    footer: {\r\n      marginTop: 40,\r\n      textAlign: 'center',\r\n    },\r\n    lang: {\r\n      width: 42,\r\n      height: 42,\r\n      lineHeight: '42px',\r\n      position: 'fixed',\r\n      right: 16,\r\n      top: 16,\r\n      borderRadius: token.borderRadius,\r\n      ':hover': {\r\n        backgroundColor: token.colorBgTextHover,\r\n      },\r\n    },\r\n  };\r\n});\r\n\r\n\r\n\r\nconst LoginPage: React.FC = () => {\r\n  const [loading, setLoading] = useState(false);\r\n  const [activeTab, setActiveTab] = useState('login');\r\n  const { styles } = useStyles();\r\n\r\n  // 自定义邮箱验证函数\r\n  const validateEmail = (email: string) => {\r\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    return emailRegex.test(email);\r\n  };\r\n\r\n  // 处理登录\r\n  const handleLogin = async (values: LoginRequest) => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await AuthService.login(values);\r\n      message.success('登录成功！');\r\n\r\n      // 根据团队数量进行不同的跳转处理\r\n      if (response.teams.length === 0) {\r\n        // 没有团队，跳转到创建团队页面\r\n        history.push('/team/create');\r\n      } else {\r\n        // 有团队（无论一个还是多个），都跳转到个人中心整合页面\r\n        history.push('/test/profile-consolidated', { teams: response.teams });\r\n      }\r\n    } catch (error) {\r\n      console.error('登录失败:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 处理注册\r\n  const handleRegister = async (values: RegisterRequest) => {\r\n    setLoading(true);\r\n    try {\r\n      await AuthService.register(values);\r\n      message.success('注册成功！请登录');\r\n      setActiveTab('login');\r\n    } catch (error) {\r\n      console.error('注册失败:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 登录表单\r\n  const LoginForm = () => (\r\n    <Form\r\n      name=\"login\"\r\n      size=\"large\"\r\n      onFinish={handleLogin}\r\n      autoComplete=\"off\"\r\n    >\r\n      <Form.Item\r\n        name=\"email\"\r\n        rules={[\r\n          { required: true, message: '请输入邮箱！' },\r\n          {\r\n            validator: (_, value) => {\r\n              if (!value || validateEmail(value)) {\r\n                return Promise.resolve();\r\n              }\r\n              return Promise.reject(new Error('请输入有效的邮箱地址！'));\r\n            },\r\n          },\r\n        ]}\r\n      >\r\n        <Input\r\n          prefix={<MailOutlined />}\r\n          placeholder=\"邮箱\"\r\n          autoComplete=\"email\"\r\n        />\r\n      </Form.Item>\r\n\r\n      <Form.Item\r\n        name=\"password\"\r\n        rules={[\r\n          { required: true, message: '请输入密码！' },\r\n        ]}\r\n      >\r\n        <Input.Password\r\n          prefix={<LockOutlined />}\r\n          placeholder=\"密码\"\r\n          autoComplete=\"current-password\"\r\n        />\r\n      </Form.Item>\r\n\r\n      <Form.Item>\r\n        <Button\r\n          type=\"primary\"\r\n          htmlType=\"submit\"\r\n          loading={loading}\r\n          block\r\n        >\r\n          登录\r\n        </Button>\r\n      </Form.Item>\r\n    </Form>\r\n  );\r\n\r\n  // 注册表单\r\n  const RegisterForm = () => (\r\n    <Form\r\n      name=\"register\"\r\n      size=\"large\"\r\n      onFinish={handleRegister}\r\n      autoComplete=\"off\"\r\n    >\r\n      <Form.Item\r\n        name=\"name\"\r\n        rules={[\r\n          { required: true, message: '请输入用户名！' },\r\n          { max: 100, message: '用户名长度不能超过100字符！' },\r\n        ]}\r\n      >\r\n        <Input\r\n          prefix={<UserOutlined />}\r\n          placeholder=\"用户名\"\r\n          autoComplete=\"name\"\r\n        />\r\n      </Form.Item>\r\n\r\n      <Form.Item\r\n        name=\"email\"\r\n        rules={[\r\n          { required: true, message: '请输入邮箱！' },\r\n          {\r\n            validator: (_, value) => {\r\n              if (!value || validateEmail(value)) {\r\n                return Promise.resolve();\r\n              }\r\n              return Promise.reject(new Error('请输入有效的邮箱地址！'));\r\n            },\r\n          },\r\n        ]}\r\n      >\r\n        <Input\r\n          prefix={<MailOutlined />}\r\n          placeholder=\"邮箱\"\r\n          autoComplete=\"email\"\r\n        />\r\n      </Form.Item>\r\n\r\n      <Form.Item\r\n        name=\"password\"\r\n        rules={[\r\n          { required: true, message: '请输入密码！' },\r\n          { min: 8, message: '密码长度至少8位！' },\r\n        ]}\r\n      >\r\n        <Input.Password\r\n          prefix={<LockOutlined />}\r\n          placeholder=\"密码（至少8位）\"\r\n          autoComplete=\"new-password\"\r\n        />\r\n      </Form.Item>\r\n\r\n      <Form.Item\r\n        name=\"confirmPassword\"\r\n        dependencies={['password']}\r\n        rules={[\r\n          { required: true, message: '请确认密码！' },\r\n          ({ getFieldValue }) => ({\r\n            validator(_, value) {\r\n              if (!value || getFieldValue('password') === value) {\r\n                return Promise.resolve();\r\n              }\r\n              return Promise.reject(new Error('两次输入的密码不一致！'));\r\n            },\r\n          }),\r\n        ]}\r\n      >\r\n        <Input.Password\r\n          prefix={<LockOutlined />}\r\n          placeholder=\"确认密码\"\r\n          autoComplete=\"new-password\"\r\n        />\r\n      </Form.Item>\r\n\r\n      <Form.Item>\r\n        <Button\r\n          type=\"primary\"\r\n          htmlType=\"submit\"\r\n          loading={loading}\r\n          block\r\n        >\r\n          注册\r\n        </Button>\r\n      </Form.Item>\r\n    </Form>\r\n  );\r\n\r\n  const tabItems = [\r\n    {\r\n      key: 'login',\r\n      label: '登录',\r\n      children: <LoginForm />,\r\n    },\r\n    {\r\n      key: 'register',\r\n      label: '注册',\r\n      children: <RegisterForm />,\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className={styles.container}>\r\n      <Helmet>\r\n        <title>\r\n          登录页\r\n          {Settings.title && ` - ${Settings.title}`}\r\n        </title>\r\n      </Helmet>\r\n      <div className={styles.content}>\r\n        <div className={styles.header}>\r\n          <Space direction=\"vertical\" align=\"center\" size=\"large\">\r\n            <div className={styles.logo}>\r\n              <img src=\"/logo.svg\" alt=\"TeamAuth\" height={48} />\r\n            </div>\r\n            <div className={styles.title}>\r\n              <Title level={2}>团队管理系统</Title>\r\n              <Text type=\"secondary\">现代化的团队协作与管理平台</Text>\r\n            </div>\r\n          </Space>\r\n        </div>\r\n\r\n        <Card className={styles.loginCard}>\r\n          <Tabs\r\n            activeKey={activeTab}\r\n            onChange={setActiveTab}\r\n            centered\r\n            items={tabItems}\r\n          />\r\n        </Card>\r\n\r\n        <div className={styles.footer}>\r\n          <Text type=\"secondary\">\r\n            © 2025 TeamAuth. All rights reserved.\r\n          </Text>\r\n        </div>\r\n      </div>\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LoginPage;\r\n"], "names": [], "mappings": ";;;AAAA;;;CAGC;;;;4BA6TD;;;eAAA;;;;;;;wEA3TgC;6BAC4C;8BACnB;4BACzB;kCACH;iCACD;mCAEL;iFACF;;;;;;;;;;AAErB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAElC,MAAM,YAAY,IAAA,uBAAY,EAAC,CAAC,EAAE,KAAK,EAAE;IACvC,OAAO;QACL,WAAW;YACT,SAAS;YACT,eAAe;YACf,QAAQ;YACR,UAAU;YACV,iBACE;YACF,gBAAgB;QAClB;QACA,SAAS;YACP,MAAM;YACN,SAAS;YACT,eAAe;YACf,gBAAgB;YAChB,YAAY;YACZ,SAAS;QACX;QACA,QAAQ;YACN,cAAc;YACd,WAAW;QACb;QACA,MAAM;YACJ,cAAc;QAChB;QACA,OAAO;YACL,cAAc;QAChB;QACA,WAAW;YACT,OAAO;YACP,UAAU;YACV,WAAW,MAAM,iBAAiB;QACpC;QACA,QAAQ;YACN,WAAW;YACX,WAAW;QACb;QACA,MAAM;YACJ,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,UAAU;YACV,OAAO;YACP,KAAK;YACL,cAAc,MAAM,YAAY;YAChC,UAAU;gBACR,iBAAiB,MAAM,gBAAgB;YACzC;QACF;IACF;AACF;AAIA,MAAM,YAAsB;;IAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;IAC3C,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,YAAY;IACZ,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC;IACzB;IAEA,OAAO;IACP,MAAM,cAAc,OAAO;QACzB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,qBAAW,CAAC,KAAK,CAAC;YACzC,aAAO,CAAC,OAAO,CAAC;YAEhB,kBAAkB;YAClB,IAAI,SAAS,KAAK,CAAC,MAAM,KAAK,GAC5B,iBAAiB;YACjB,YAAO,CAAC,IAAI,CAAC;iBAEb,6BAA6B;YAC7B,YAAO,CAAC,IAAI,CAAC,8BAA8B;gBAAE,OAAO,SAAS,KAAK;YAAC;QAEvE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;IACP,MAAM,iBAAiB,OAAO;QAC5B,WAAW;QACX,IAAI;YACF,MAAM,qBAAW,CAAC,QAAQ,CAAC;YAC3B,aAAO,CAAC,OAAO,CAAC;YAChB,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;IACP,MAAM,YAAY,kBAChB,2BAAC,UAAI;YACH,MAAK;YACL,MAAK;YACL,UAAU;YACV,cAAa;;8BAEb,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,OAAO;wBACL;4BAAE,UAAU;4BAAM,SAAS;wBAAS;wBACpC;4BACE,WAAW,CAAC,GAAG;gCACb,IAAI,CAAC,SAAS,cAAc,QAC1B,OAAO,QAAQ,OAAO;gCAExB,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;4BAClC;wBACF;qBACD;8BAED,cAAA,2BAAC,WAAK;wBACJ,sBAAQ,2BAAC,mBAAY;;;;;wBACrB,aAAY;wBACZ,cAAa;;;;;;;;;;;8BAIjB,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,OAAO;wBACL;4BAAE,UAAU;4BAAM,SAAS;wBAAS;qBACrC;8BAED,cAAA,2BAAC,WAAK,CAAC,QAAQ;wBACb,sBAAQ,2BAAC,mBAAY;;;;;wBACrB,aAAY;wBACZ,cAAa;;;;;;;;;;;8BAIjB,2BAAC,UAAI,CAAC,IAAI;8BACR,cAAA,2BAAC,YAAM;wBACL,MAAK;wBACL,UAAS;wBACT,SAAS;wBACT,KAAK;kCACN;;;;;;;;;;;;;;;;;IAOP,OAAO;IACP,MAAM,eAAe,kBACnB,2BAAC,UAAI;YACH,MAAK;YACL,MAAK;YACL,UAAU;YACV,cAAa;;8BAEb,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,OAAO;wBACL;4BAAE,UAAU;4BAAM,SAAS;wBAAU;wBACrC;4BAAE,KAAK;4BAAK,SAAS;wBAAkB;qBACxC;8BAED,cAAA,2BAAC,WAAK;wBACJ,sBAAQ,2BAAC,mBAAY;;;;;wBACrB,aAAY;wBACZ,cAAa;;;;;;;;;;;8BAIjB,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,OAAO;wBACL;4BAAE,UAAU;4BAAM,SAAS;wBAAS;wBACpC;4BACE,WAAW,CAAC,GAAG;gCACb,IAAI,CAAC,SAAS,cAAc,QAC1B,OAAO,QAAQ,OAAO;gCAExB,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;4BAClC;wBACF;qBACD;8BAED,cAAA,2BAAC,WAAK;wBACJ,sBAAQ,2BAAC,mBAAY;;;;;wBACrB,aAAY;wBACZ,cAAa;;;;;;;;;;;8BAIjB,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,OAAO;wBACL;4BAAE,UAAU;4BAAM,SAAS;wBAAS;wBACpC;4BAAE,KAAK;4BAAG,SAAS;wBAAY;qBAChC;8BAED,cAAA,2BAAC,WAAK,CAAC,QAAQ;wBACb,sBAAQ,2BAAC,mBAAY;;;;;wBACrB,aAAY;wBACZ,cAAa;;;;;;;;;;;8BAIjB,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,cAAc;wBAAC;qBAAW;oBAC1B,OAAO;wBACL;4BAAE,UAAU;4BAAM,SAAS;wBAAS;wBACpC,CAAC,EAAE,aAAa,EAAE,GAAM,CAAA;gCACtB,WAAU,CAAC,EAAE,KAAK;oCAChB,IAAI,CAAC,SAAS,cAAc,gBAAgB,OAC1C,OAAO,QAAQ,OAAO;oCAExB,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;gCAClC;4BACF,CAAA;qBACD;8BAED,cAAA,2BAAC,WAAK,CAAC,QAAQ;wBACb,sBAAQ,2BAAC,mBAAY;;;;;wBACrB,aAAY;wBACZ,cAAa;;;;;;;;;;;8BAIjB,2BAAC,UAAI,CAAC,IAAI;8BACR,cAAA,2BAAC,YAAM;wBACL,MAAK;wBACL,UAAS;wBACT,SAAS;wBACT,KAAK;kCACN;;;;;;;;;;;;;;;;;IAOP,MAAM,WAAW;QACf;YACE,KAAK;YACL,OAAO;YACP,wBAAU,2BAAC;;;;;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,wBAAU,2BAAC;;;;;QACb;KACD;IAED,qBACE,2BAAC;QAAI,WAAW,OAAO,SAAS;;0BAC9B,2BAAC,WAAM;0BACL,cAAA,2BAAC;;wBAAM;wBAEJ,wBAAQ,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,wBAAQ,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;0BAG7C,2BAAC;gBAAI,WAAW,OAAO,OAAO;;kCAC5B,2BAAC;wBAAI,WAAW,OAAO,MAAM;kCAC3B,cAAA,2BAAC,WAAK;4BAAC,WAAU;4BAAW,OAAM;4BAAS,MAAK;;8CAC9C,2BAAC;oCAAI,WAAW,OAAO,IAAI;8CACzB,cAAA,2BAAC;wCAAI,KAAI;wCAAY,KAAI;wCAAW,QAAQ;;;;;;;;;;;8CAE9C,2BAAC;oCAAI,WAAW,OAAO,KAAK;;sDAC1B,2BAAC;4CAAM,OAAO;sDAAG;;;;;;sDACjB,2BAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;;;;;;;;kCAK7B,2BAAC,UAAI;wBAAC,WAAW,OAAO,SAAS;kCAC/B,cAAA,2BAAC,UAAI;4BACH,WAAW;4BACX,UAAU;4BACV,QAAQ;4BACR,OAAO;;;;;;;;;;;kCAIX,2BAAC;wBAAI,WAAW,OAAO,MAAM;kCAC3B,cAAA,2BAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;;;;;;;;0BAK3B,2BAAC,kBAAM;;;;;;;;;;;AAGb;GAtPM;;QAGe;;;KAHf;IAwPN,WAAe"}