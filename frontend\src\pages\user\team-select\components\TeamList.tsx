/**
 * 团队列表组件
 * 用于显示用户的团队列表并支持选择
 */

import React from 'react';
import { List, Avatar, Typography, Space } from 'antd';
import { TeamOutlined, UserOutlined } from '@ant-design/icons';
import { createStyles } from 'antd-style';
import type { TeamInfo } from '@/types/api';

const { Text } = Typography;

const useStyles = createStyles(({ token }) => {
  return {
    teamList: {
      width: '100%',
    },
    teamItem: {
      padding: '16px 24px',
      cursor: 'pointer',
      borderRadius: token.borderRadius,
      transition: 'all 0.3s',
      '&:hover': {
        backgroundColor: token.colorBgTextHover,
      },
    },
    teamItemSelected: {
      backgroundColor: token.colorPrimaryBg,
      borderColor: token.colorPrimary,
    },
  };
});

interface TeamListProps {
  teams: TeamInfo[];
  selectedTeamId: number | null;
  onTeamSelect: (teamId: number) => void;
}

const TeamList: React.FC<TeamListProps> = ({
  teams,
  selectedTeamId,
  onTeamSelect,
}) => {
  const { styles } = useStyles();

  if (teams.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: '40px 0' }}>
        <TeamOutlined style={{ fontSize: 64, color: '#d9d9d9', marginBottom: 16 }} />
        <Typography.Title level={4} type="secondary">
          您还没有加入任何团队
        </Typography.Title>
        <Text type="secondary">
          创建一个新团队或等待其他人邀请您加入
        </Text>
      </div>
    );
  }

  return (
    <List
      className={styles.teamList}
      dataSource={teams}
      renderItem={(team) => (
        <List.Item
          className={`${styles.teamItem} ${
            selectedTeamId === team.id ? styles.teamItemSelected : ''
          }`}
          onClick={() => onTeamSelect(team.id)}
        >
          <List.Item.Meta
            avatar={
              <Avatar size="large" icon={<TeamOutlined />} />
            }
            title={
              <Space>
                {team.name}
                {team.isCreator && (
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    (创建者)
                  </Text>
                )}
              </Space>
            }
            description={
              <Space>
                <UserOutlined />
                <Text type="secondary">{team.memberCount} 名成员</Text>
                <Text type="secondary">
                  最后访问: {new Date(team.lastAccessTime).toLocaleDateString()}
                </Text>
              </Space>
            }
          />
        </List.Item>
      )}
    />
  );
};

export default TeamList;
