((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['p__subscription__index'],
{ "src/pages/subscription/index.tsx": function (module, exports, __mako_require__){
/**
 * 订阅管理页面 - 统一的订阅管理界面
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _services = __mako_require__("src/services/index.ts");
var _UnifiedSubscriptionContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/subscription/components/UnifiedSubscriptionContent.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const SubscriptionPage = ()=>{
    _s();
    const [currentSubscription, setCurrentSubscription] = (0, _react.useState)(null);
    const [loading, setLoading] = (0, _react.useState)(true);
    (0, _react.useEffect)(()=>{
        fetchCurrentSubscription();
    }, []);
    const fetchCurrentSubscription = async ()=>{
        try {
            setLoading(true);
            const subscription = await _services.SubscriptionService.getCurrentSubscription();
            setCurrentSubscription(subscription);
        } catch (error) {
            console.error('获取当前订阅失败:', error);
            _antd.message.error('获取订阅信息失败');
        } finally{
            setLoading(false);
        }
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "订阅管理",
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UnifiedSubscriptionContent.default, {
            currentSubscription: currentSubscription,
            loading: loading,
            onRefresh: fetchCurrentSubscription
        }, void 0, false, {
            fileName: "src/pages/subscription/index.tsx",
            lineNumber: 37,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/subscription/index.tsx",
        lineNumber: 36,
        columnNumber: 5
    }, this);
};
_s(SubscriptionPage, "XLrtBtOa7IP1iypwWvRQWDiKewU=");
_c = SubscriptionPage;
var _default = SubscriptionPage;
var _c;
$RefreshReg$(_c, "SubscriptionPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__subscription__index-async.js.map