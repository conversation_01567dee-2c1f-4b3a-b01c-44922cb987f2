{"version": 3, "sources": ["src_pages_test_profile-consolidated_index_tsx-async.2373880087926243838.hot-update.js", "src/pages/test/profile-consolidated/TeamListCard.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/test/profile-consolidated/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='11595200147652825746';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/detail/index.tsx\":[\"common\",\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/test/profile-consolidated/index.tsx\":[\"src/pages/test/profile-consolidated/index.tsx\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Card,\r\n  Typography,\r\n  Tooltip,\r\n  List,\r\n  Flex,\r\n  Spin,\r\n  Alert,\r\n  Tag,\r\n  Row,\r\n  Col,\r\n  message\r\n} from \"antd\";\r\nimport { TeamService } from \"@/services/team\";\r\nimport { AuthService } from \"@/services\";\r\nimport { useModel, history } from '@umijs/max';\r\nimport type { TeamDetailResponse } from \"@/types/api\";\r\nimport {\r\n  CarOutlined,\r\n  TeamOutlined,\r\n  UserOutlined,\r\n  WarningOutlined,\r\n  ClockCircleOutlined,\r\n  UserSwitchOutlined,\r\n  CrownOutlined,\r\n  StarFilled,\r\n  RightOutlined,\r\n  ExclamationCircleOutlined,\r\n  CheckCircleOutlined\r\n} from \"@ant-design/icons\";\r\n\r\nconst { Text, Title } = Typography;\r\n\r\nconst TeamListCard: React.FC = () => {\r\n  // 团队列表状态管理\r\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [switchingTeamId, setSwitchingTeamId] = useState<number | null>(null);\r\n\r\n  const { initialState, setInitialState } = useModel('@@initialState');\r\n  const currentTeam = initialState?.currentTeam;\r\n\r\n  // 获取团队列表数据\r\n  useEffect(() => {\r\n    const fetchTeams = async () => {\r\n      try {\r\n        setLoading(true);\r\n        setError(null);\r\n        const teamsData = await TeamService.getUserTeamsWithStats();\r\n        setTeams(teamsData);\r\n      } catch (error) {\r\n        console.error('获取团队列表失败:', error);\r\n        setError('获取团队列表失败');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchTeams();\r\n  }, []);\r\n\r\n  // 团队切换处理函数\r\n  const handleTeamSwitch = async (teamId: number, teamName: string) => {\r\n    if (teamId === currentTeam?.id) {\r\n      message.info('您已经在当前团队中');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setSwitchingTeamId(teamId);\r\n      const response = await AuthService.selectTeam({ teamId });\r\n\r\n      // 检查后端返回的团队选择成功标识\r\n      if (response.teamSelectionSuccess && response.team && response.team.id === teamId) {\r\n        message.success(`已切换到团队：${teamName}`);\r\n\r\n        // 同步更新 initialState，等待更新完成后再跳转\r\n        if (initialState?.fetchTeamInfo && initialState?.fetchUserInfo && setInitialState) {\r\n          try {\r\n            const [currentUser, currentTeam] = await Promise.all([\r\n              initialState.fetchUserInfo(),\r\n              initialState.fetchTeamInfo()\r\n            ]);\r\n\r\n            // 确保团队信息已正确获取\r\n            if (currentTeam && currentTeam.id === teamId) {\r\n              await setInitialState({\r\n                ...initialState,\r\n                currentUser,\r\n                currentTeam,\r\n              });\r\n\r\n              // 等待 initialState 更新完成后再跳转到仪表盘\r\n              setTimeout(() => {\r\n                history.push('/dashboard');\r\n              }, 100);\r\n            } else {\r\n              console.error('获取的团队信息与选择的团队不匹配');\r\n              message.error('团队切换失败，请重试');\r\n            }\r\n          } catch (error) {\r\n            console.error('更新 initialState 失败:', error);\r\n            message.error('团队切换失败，请重试');\r\n          }\r\n        } else {\r\n          // 如果没有 initialState 相关方法，直接跳转\r\n          history.push('/dashboard');\r\n        }\r\n      } else {\r\n        console.error('团队切换响应异常，未返回正确的团队信息');\r\n        message.error('团队切换失败，请重试');\r\n      }\r\n    } catch (error) {\r\n      console.error('团队切换失败:', error);\r\n      message.error('团队切换失败');\r\n    } finally {\r\n      setSwitchingTeamId(null);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card\r\n      className=\"dashboard-card\"\r\n      style={{\r\n        borderRadius: 12,\r\n        boxShadow: \"0 4px 12px rgba(0,0,0,0.05)\",\r\n        border: \"none\",\r\n        background: \"linear-gradient(145deg, #ffffff, #f5f8ff)\",\r\n      }}\r\n      title={\r\n        <Flex justify=\"space-between\" align=\"center\">\r\n          <Title level={5} style={{ margin: 0 }}>团队列表</Title>\r\n        </Flex>\r\n      }\r\n    >\r\n      {error ? (\r\n        <Alert\r\n          message=\"团队列表加载失败\"\r\n          description={error}\r\n          type=\"error\"\r\n          showIcon\r\n          style={{ marginBottom: 16 }}\r\n        />\r\n      ) : (\r\n        <Spin spinning={loading}>\r\n          <List\r\n            dataSource={teams}\r\n            renderItem={(item) => (\r\n              <List.Item>\r\n                <Card\r\n                  className=\"team-item\"\r\n                  style={{\r\n                    background: currentTeam?.id === item.id\r\n                      ? \"linear-gradient(135deg, #f0f9ff, #e6f4ff)\"\r\n                      : \"#fff\",\r\n                    borderRadius: 16,\r\n                    boxShadow: currentTeam?.id === item.id\r\n                      ? \"0 4px 16px rgba(24, 144, 255, 0.12)\"\r\n                      : \"0 2px 8px rgba(0,0,0,0.06)\",\r\n                    width: \"100%\",\r\n                    borderLeft: `5px solid ${item.isCreator ? \"#722ed1\" : \"#52c41a\"}`,\r\n                    transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\r\n                    border: currentTeam?.id === item.id\r\n                      ? \"1px solid #91caff\"\r\n                      : \"1px solid #f0f0f0\",\r\n                    padding: \"24px 28px\",\r\n                    position: 'relative',\r\n                    overflow: 'hidden'\r\n                  }}\r\n                  hoverable\r\n                  onMouseEnter={(e) => {\r\n                    if (currentTeam?.id !== item.id) {\r\n                      e.currentTarget.style.transform = 'translateY(-2px)';\r\n                      e.currentTarget.style.boxShadow = '0 8px 24px rgba(0,0,0,0.12)';\r\n                    }\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    if (currentTeam?.id !== item.id) {\r\n                      e.currentTarget.style.transform = 'translateY(0)';\r\n                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';\r\n                    }\r\n                  }}\r\n                >\r\n                  {/* 当前团队的装饰性背景 */}\r\n                  {currentTeam?.id === item.id && (\r\n                    <div style={{\r\n                      position: 'absolute',\r\n                      top: 0,\r\n                      right: 0,\r\n                      width: '100px',\r\n                      height: '100px',\r\n                      background: 'linear-gradient(135deg, rgba(24, 144, 255, 0.05), rgba(64, 169, 255, 0.02))',\r\n                      borderRadius: '0 16px 0 100px',\r\n                      pointerEvents: 'none'\r\n                    }} />\r\n                  )}\r\n                  {/* 优化的卡片布局 */}\r\n                  <Row justify=\"space-between\" align=\"top\" gutter={[20, 0]}>\r\n                    {/* 左侧：团队基本信息 */}\r\n                    <Col flex=\"1\">\r\n                      <Flex vertical gap={12}>\r\n                        {/* 团队名称和状态 */}\r\n                        <Flex align=\"center\" justify=\"space-between\" style={{ width: '100%' }}>\r\n                          <Flex align=\"center\" gap={12}>\r\n                            {/* 团队名称 - 增强交互设计 */}\r\n                            <div\r\n                              style={{\r\n                                position: 'relative',\r\n                                cursor: currentTeam?.id === item.id ? 'default' : 'pointer',\r\n                                padding: '4px 0'\r\n                              }}\r\n                              onClick={() => handleTeamSwitch(item.id, item.name)}\r\n                            >\r\n                              <Text\r\n                                strong\r\n                                style={{\r\n                                  fontSize: 20,\r\n                                  color: currentTeam?.id === item.id ? '#1890ff' : '#262626',\r\n                                  lineHeight: 1.2,\r\n                                  transition: 'all 0.2s ease',\r\n                                  position: 'relative'\r\n                                }}\r\n                                className={currentTeam?.id !== item.id ? 'team-name-hover' : ''}\r\n                              >\r\n                                {item.name}\r\n                              </Text>\r\n                              {currentTeam?.id !== item.id && (\r\n                                <RightOutlined\r\n                                  style={{\r\n                                    fontSize: 12,\r\n                                    color: '#8c8c8c',\r\n                                    marginLeft: 8,\r\n                                    transition: 'all 0.2s ease'\r\n                                  }}\r\n                                  className=\"team-switch-icon\"\r\n                                />\r\n                              )}\r\n                            </div>\r\n\r\n                            {/* 当前团队标识 - 重新设计 */}\r\n                            {currentTeam?.id === item.id && (\r\n                              <div style={{\r\n                                background: 'linear-gradient(135deg, #1890ff, #40a9ff)',\r\n                                color: 'white',\r\n                                padding: '4px 12px',\r\n                                borderRadius: 20,\r\n                                fontSize: 12,\r\n                                fontWeight: 600,\r\n                                display: 'flex',\r\n                                alignItems: 'center',\r\n                                gap: 4,\r\n                                boxShadow: '0 2px 6px rgba(24, 144, 255, 0.3)'\r\n                              }}>\r\n                                <CheckCircleOutlined style={{ fontSize: 12 }} />\r\n                                当前团队\r\n                              </div>\r\n                            )}\r\n\r\n                            {/* 加载状态 */}\r\n                            {switchingTeamId === item.id && (\r\n                              <div style={{\r\n                                background: '#f0f0f0',\r\n                                padding: '6px 12px',\r\n                                borderRadius: 20,\r\n                                display: 'flex',\r\n                                alignItems: 'center',\r\n                                gap: 6\r\n                              }}>\r\n                                <Spin size=\"small\" />\r\n                                <Text style={{ fontSize: 12, color: '#666' }}>切换中...</Text>\r\n                              </div>\r\n                            )}\r\n                          </Flex>\r\n\r\n                          {/* 团队角色标识 - 重新设计 */}\r\n                          <div style={{\r\n                            background: item.isCreator\r\n                              ? 'linear-gradient(135deg, #722ed1, #9254de)'\r\n                              : 'linear-gradient(135deg, #52c41a, #73d13d)',\r\n                            color: 'white',\r\n                            padding: '6px 14px',\r\n                            borderRadius: 16,\r\n                            fontSize: 12,\r\n                            fontWeight: 600,\r\n                            display: 'flex',\r\n                            alignItems: 'center',\r\n                            gap: 6,\r\n                            boxShadow: item.isCreator\r\n                              ? '0 2px 8px rgba(114, 46, 209, 0.3)'\r\n                              : '0 2px 8px rgba(82, 196, 26, 0.3)',\r\n                            minWidth: 'fit-content'\r\n                          }}>\r\n                            {item.isCreator ? (\r\n                              <>\r\n                                <CrownOutlined style={{ fontSize: 14 }} />\r\n                                管理员\r\n                              </>\r\n                            ) : (\r\n                              <>\r\n                                <UserOutlined style={{ fontSize: 14 }} />\r\n                                成员\r\n                              </>\r\n                            )}\r\n                          </div>\r\n                        </Flex>\r\n                        \r\n                        {/* 团队基本信息行 - 优化设计 */}\r\n                        <Flex align=\"center\" gap={32} style={{ marginTop: 8 }}>\r\n                          <Tooltip title={`创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`}>\r\n                            <Flex align=\"center\" gap={8}>\r\n                              <div style={{\r\n                                background: 'linear-gradient(135deg, #f0f9ff, #e0f2fe)',\r\n                                padding: '4px',\r\n                                borderRadius: '6px',\r\n                                display: 'flex',\r\n                                alignItems: 'center'\r\n                              }}>\r\n                                <ClockCircleOutlined style={{ color: \"#0ea5e9\", fontSize: 14 }} />\r\n                              </div>\r\n                              <Text style={{ fontSize: 13, color: '#595959', fontWeight: 500 }}>\r\n                                创建于 {new Date(item.createdAt).toLocaleDateString('zh-CN')}\r\n                              </Text>\r\n                            </Flex>\r\n                          </Tooltip>\r\n\r\n                          <Tooltip title={`团队成员: ${item.memberCount}人`}>\r\n                            <Flex align=\"center\" gap={8}>\r\n                              <div style={{\r\n                                background: 'linear-gradient(135deg, #f0f9ff, #e0f2fe)',\r\n                                padding: '4px',\r\n                                borderRadius: '6px',\r\n                                display: 'flex',\r\n                                alignItems: 'center'\r\n                              }}>\r\n                                <TeamOutlined style={{ color: \"#0ea5e9\", fontSize: 14 }} />\r\n                              </div>\r\n                              <Text style={{ fontSize: 13, color: '#595959', fontWeight: 500 }}>\r\n                                {item.memberCount} 名成员\r\n                              </Text>\r\n                            </Flex>\r\n                          </Tooltip>\r\n                        </Flex>\r\n                      </Flex>\r\n                    </Col>\r\n\r\n                    {/* 右侧：核心指标卡片 - 重新设计 */}\r\n                    <Col flex=\"none\">\r\n                      <div style={{\r\n                        display: 'grid',\r\n                        gridTemplateColumns: 'repeat(2, 1fr)',\r\n                        gap: '12px',\r\n                        minWidth: '200px'\r\n                      }}>\r\n                        {/* 车辆资源卡片 */}\r\n                        <div style={{\r\n                          background: 'linear-gradient(135deg, #e6f4ff, #f0f9ff)',\r\n                          border: '1px solid #91caff',\r\n                          borderRadius: 12,\r\n                          padding: '12px',\r\n                          textAlign: 'center',\r\n                          transition: 'all 0.3s ease',\r\n                          cursor: 'pointer',\r\n                          position: 'relative',\r\n                          overflow: 'hidden'\r\n                        }}\r\n                        onMouseEnter={(e) => {\r\n                          e.currentTarget.style.transform = 'translateY(-2px)';\r\n                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(24, 144, 255, 0.15)';\r\n                        }}\r\n                        onMouseLeave={(e) => {\r\n                          e.currentTarget.style.transform = 'translateY(0)';\r\n                          e.currentTarget.style.boxShadow = 'none';\r\n                        }}>\r\n                          <div style={{\r\n                            position: 'absolute',\r\n                            top: 0,\r\n                            right: 0,\r\n                            width: '30px',\r\n                            height: '30px',\r\n                            background: 'linear-gradient(135deg, #1890ff, #40a9ff)',\r\n                            borderRadius: '0 12px 0 20px',\r\n                            opacity: 0.1\r\n                          }} />\r\n                          <Flex vertical align=\"center\" gap={6}>\r\n                            <div style={{\r\n                              background: 'linear-gradient(135deg, #1890ff, #40a9ff)',\r\n                              borderRadius: '50%',\r\n                              width: '32px',\r\n                              height: '32px',\r\n                              display: 'flex',\r\n                              alignItems: 'center',\r\n                              justifyContent: 'center',\r\n                              marginBottom: '4px'\r\n                            }}>\r\n                              <CarOutlined style={{ color: \"white\", fontSize: 16 }} />\r\n                            </div>\r\n                            <Text strong style={{ fontSize: 18, color: '#1890ff', lineHeight: 1 }}>\r\n                              {item.stats?.vehicles || 0}\r\n                            </Text>\r\n                            <Text style={{ fontSize: 11, color: '#666', fontWeight: 500 }}>车辆资源</Text>\r\n                          </Flex>\r\n                        </div>\r\n\r\n                        {/* 人员资源卡片 */}\r\n                        <div style={{\r\n                          background: 'linear-gradient(135deg, #f6ffed, #f0f9ff)',\r\n                          border: '1px solid #95de64',\r\n                          borderRadius: 12,\r\n                          padding: '12px',\r\n                          textAlign: 'center',\r\n                          transition: 'all 0.3s ease',\r\n                          cursor: 'pointer',\r\n                          position: 'relative',\r\n                          overflow: 'hidden'\r\n                        }}\r\n                        onMouseEnter={(e) => {\r\n                          e.currentTarget.style.transform = 'translateY(-2px)';\r\n                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(82, 196, 26, 0.15)';\r\n                        }}\r\n                        onMouseLeave={(e) => {\r\n                          e.currentTarget.style.transform = 'translateY(0)';\r\n                          e.currentTarget.style.boxShadow = 'none';\r\n                        }}>\r\n                          <div style={{\r\n                            position: 'absolute',\r\n                            top: 0,\r\n                            right: 0,\r\n                            width: '30px',\r\n                            height: '30px',\r\n                            background: 'linear-gradient(135deg, #52c41a, #73d13d)',\r\n                            borderRadius: '0 12px 0 20px',\r\n                            opacity: 0.1\r\n                          }} />\r\n                          <Flex vertical align=\"center\" gap={6}>\r\n                            <div style={{\r\n                              background: 'linear-gradient(135deg, #52c41a, #73d13d)',\r\n                              borderRadius: '50%',\r\n                              width: '32px',\r\n                              height: '32px',\r\n                              display: 'flex',\r\n                              alignItems: 'center',\r\n                              justifyContent: 'center',\r\n                              marginBottom: '4px'\r\n                            }}>\r\n                              <UserOutlined style={{ color: \"white\", fontSize: 16 }} />\r\n                            </div>\r\n                            <Text strong style={{ fontSize: 18, color: '#52c41a', lineHeight: 1 }}>\r\n                              {item.stats?.personnel || 0}\r\n                            </Text>\r\n                            <Text style={{ fontSize: 11, color: '#666', fontWeight: 500 }}>人员资源</Text>\r\n                          </Flex>\r\n                        </div>\r\n\r\n                        {/* 临期事项卡片 */}\r\n                        <div style={{\r\n                          background: 'linear-gradient(135deg, #fff7e6, #fffbe6)',\r\n                          border: '1px solid #ffd666',\r\n                          borderRadius: 12,\r\n                          padding: '12px',\r\n                          textAlign: 'center',\r\n                          transition: 'all 0.3s ease',\r\n                          cursor: 'pointer',\r\n                          position: 'relative',\r\n                          overflow: 'hidden'\r\n                        }}\r\n                        onMouseEnter={(e) => {\r\n                          e.currentTarget.style.transform = 'translateY(-2px)';\r\n                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(250, 173, 20, 0.15)';\r\n                        }}\r\n                        onMouseLeave={(e) => {\r\n                          e.currentTarget.style.transform = 'translateY(0)';\r\n                          e.currentTarget.style.boxShadow = 'none';\r\n                        }}>\r\n                          <div style={{\r\n                            position: 'absolute',\r\n                            top: 0,\r\n                            right: 0,\r\n                            width: '30px',\r\n                            height: '30px',\r\n                            background: 'linear-gradient(135deg, #faad14, #ffc53d)',\r\n                            borderRadius: '0 12px 0 20px',\r\n                            opacity: 0.1\r\n                          }} />\r\n                          <Flex vertical align=\"center\" gap={6}>\r\n                            <div style={{\r\n                              background: 'linear-gradient(135deg, #faad14, #ffc53d)',\r\n                              borderRadius: '50%',\r\n                              width: '32px',\r\n                              height: '32px',\r\n                              display: 'flex',\r\n                              alignItems: 'center',\r\n                              justifyContent: 'center',\r\n                              marginBottom: '4px'\r\n                            }}>\r\n                              <ExclamationCircleOutlined style={{ color: \"white\", fontSize: 16 }} />\r\n                            </div>\r\n                            <Text strong style={{ fontSize: 18, color: '#faad14', lineHeight: 1 }}>\r\n                              {item.stats?.expiring || 0}\r\n                            </Text>\r\n                            <Text style={{ fontSize: 11, color: '#666', fontWeight: 500 }}>临期事项</Text>\r\n                          </Flex>\r\n                        </div>\r\n\r\n                        {/* 逾期事项卡片 */}\r\n                        <div style={{\r\n                          background: 'linear-gradient(135deg, #fff1f0, #fff2f0)',\r\n                          border: '1px solid #ffadd2',\r\n                          borderRadius: 12,\r\n                          padding: '12px',\r\n                          textAlign: 'center',\r\n                          transition: 'all 0.3s ease',\r\n                          cursor: 'pointer',\r\n                          position: 'relative',\r\n                          overflow: 'hidden'\r\n                        }}\r\n                        onMouseEnter={(e) => {\r\n                          e.currentTarget.style.transform = 'translateY(-2px)';\r\n                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(255, 77, 79, 0.15)';\r\n                        }}\r\n                        onMouseLeave={(e) => {\r\n                          e.currentTarget.style.transform = 'translateY(0)';\r\n                          e.currentTarget.style.boxShadow = 'none';\r\n                        }}>\r\n                          <div style={{\r\n                            position: 'absolute',\r\n                            top: 0,\r\n                            right: 0,\r\n                            width: '30px',\r\n                            height: '30px',\r\n                            background: 'linear-gradient(135deg, #ff4d4f, #ff7875)',\r\n                            borderRadius: '0 12px 0 20px',\r\n                            opacity: 0.1\r\n                          }} />\r\n                          <Flex vertical align=\"center\" gap={6}>\r\n                            <div style={{\r\n                              background: 'linear-gradient(135deg, #ff4d4f, #ff7875)',\r\n                              borderRadius: '50%',\r\n                              width: '32px',\r\n                              height: '32px',\r\n                              display: 'flex',\r\n                              alignItems: 'center',\r\n                              justifyContent: 'center',\r\n                              marginBottom: '4px'\r\n                            }}>\r\n                              <ExclamationCircleOutlined style={{ color: \"white\", fontSize: 16 }} />\r\n                            </div>\r\n                            <Text strong style={{ fontSize: 18, color: '#ff4d4f', lineHeight: 1 }}>\r\n                              {item.stats?.overdue || 0}\r\n                            </Text>\r\n                            <Text style={{ fontSize: 11, color: '#666', fontWeight: 500 }}>逾期事项</Text>\r\n                          </Flex>\r\n                        </div>\r\n                      </div>\r\n                    </Col>\r\n                  </Row>\r\n                </Card>\r\n              </List.Item>\r\n            )}\r\n          />\r\n        </Spin>\r\n      )}\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default TeamListCard;"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,iDACA;IACE,SAAS;;;;;;wCCojBb;;;2BAAA;;;;;;oFAvjB2C;yCAapC;yCACqB;6CACA;wCACM;0CAc3B;;;;;;;;;;YAEP,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,gBAAU;YAElC,MAAM,eAAyB;;gBAC7B,WAAW;gBACX,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAuB,EAAE;gBAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAgB;gBAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAgB;gBAEtE,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;gBACnD,MAAM,cAAc,yBAAA,mCAAA,aAAc,WAAW;gBAE7C,WAAW;gBACX,IAAA,gBAAS,EAAC;oBACR,MAAM,aAAa;wBACjB,IAAI;4BACF,WAAW;4BACX,SAAS;4BACT,MAAM,YAAY,MAAM,iBAAW,CAAC,qBAAqB;4BACzD,SAAS;wBACX,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,aAAa;4BAC3B,SAAS;wBACX,SAAU;4BACR,WAAW;wBACb;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,WAAW;gBACX,MAAM,mBAAmB,OAAO,QAAgB;oBAC9C,IAAI,YAAW,wBAAA,kCAAA,YAAa,EAAE,GAAE;wBAC9B,aAAO,CAAC,IAAI,CAAC;wBACb;oBACF;oBAEA,IAAI;wBACF,mBAAmB;wBACnB,MAAM,WAAW,MAAM,qBAAW,CAAC,UAAU,CAAC;4BAAE;wBAAO;wBAEvD,kBAAkB;wBAClB,IAAI,SAAS,oBAAoB,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,QAAQ;4BACjF,aAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC;4BAEpC,+BAA+B;4BAC/B,IAAI,CAAA,yBAAA,mCAAA,aAAc,aAAa,MAAI,yBAAA,mCAAA,aAAc,aAAa,KAAI,iBAChE,IAAI;gCACF,MAAM,CAAC,aAAa,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;oCACnD,aAAa,aAAa;oCAC1B,aAAa,aAAa;iCAC3B;gCAED,cAAc;gCACd,IAAI,eAAe,YAAY,EAAE,KAAK,QAAQ;oCAC5C,MAAM,gBAAgB;wCACpB,GAAG,YAAY;wCACf;wCACA;oCACF;oCAEA,+BAA+B;oCAC/B,WAAW;wCACT,YAAO,CAAC,IAAI,CAAC;oCACf,GAAG;gCACL,OAAO;oCACL,QAAQ,KAAK,CAAC;oCACd,aAAO,CAAC,KAAK,CAAC;gCAChB;4BACF,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,uBAAuB;gCACrC,aAAO,CAAC,KAAK,CAAC;4BAChB;iCAEA,8BAA8B;4BAC9B,YAAO,CAAC,IAAI,CAAC;wBAEjB,OAAO;4BACL,QAAQ,KAAK,CAAC;4BACd,aAAO,CAAC,KAAK,CAAC;wBAChB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB,SAAU;wBACR,mBAAmB;oBACrB;gBACF;gBAEA,qBACE,2BAAC,UAAI;oBACH,WAAU;oBACV,OAAO;wBACL,cAAc;wBACd,WAAW;wBACX,QAAQ;wBACR,YAAY;oBACd;oBACA,qBACE,2BAAC,UAAI;wBAAC,SAAQ;wBAAgB,OAAM;kCAClC,cAAA,2BAAC;4BAAM,OAAO;4BAAG,OAAO;gCAAE,QAAQ;4BAAE;sCAAG;;;;;;;;;;;8BAI1C,sBACC,2BAAC,WAAK;wBACJ,SAAQ;wBACR,aAAa;wBACb,MAAK;wBACL,QAAQ;wBACR,OAAO;4BAAE,cAAc;wBAAG;;;;;6CAG5B,2BAAC,UAAI;wBAAC,UAAU;kCACd,cAAA,2BAAC,UAAI;4BACH,YAAY;4BACZ,YAAY,CAAC;oCA0PM,aAkDA,cAkDA,cAkDA;qDA/YjB,2BAAC,UAAI,CAAC,IAAI;8CACR,cAAA,2BAAC,UAAI;wCACH,WAAU;wCACV,OAAO;4CACL,YAAY,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GACnC,8CACA;4CACJ,cAAc;4CACd,WAAW,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GAClC,wCACA;4CACJ,OAAO;4CACP,YAAY,CAAC,UAAU,EAAE,KAAK,SAAS,GAAG,YAAY,UAAU,CAAC;4CACjE,YAAY;4CACZ,QAAQ,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GAC/B,sBACA;4CACJ,SAAS;4CACT,UAAU;4CACV,UAAU;wCACZ;wCACA,SAAS;wCACT,cAAc,CAAC;4CACb,IAAI,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,EAAE;gDAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gDAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4CACpC;wCACF;wCACA,cAAc,CAAC;4CACb,IAAI,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,EAAE;gDAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gDAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4CACpC;wCACF;;4CAGC,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,kBAC1B,2BAAC;gDAAI,OAAO;oDACV,UAAU;oDACV,KAAK;oDACL,OAAO;oDACP,OAAO;oDACP,QAAQ;oDACR,YAAY;oDACZ,cAAc;oDACd,eAAe;gDACjB;;;;;;0DAGF,2BAAC,SAAG;gDAAC,SAAQ;gDAAgB,OAAM;gDAAM,QAAQ;oDAAC;oDAAI;iDAAE;;kEAEtD,2BAAC,SAAG;wDAAC,MAAK;kEACR,cAAA,2BAAC,UAAI;4DAAC,QAAQ;4DAAC,KAAK;;8EAElB,2BAAC,UAAI;oEAAC,OAAM;oEAAS,SAAQ;oEAAgB,OAAO;wEAAE,OAAO;oEAAO;;sFAClE,2BAAC,UAAI;4EAAC,OAAM;4EAAS,KAAK;;8FAExB,2BAAC;oFACC,OAAO;wFACL,UAAU;wFACV,QAAQ,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GAAG,YAAY;wFAClD,SAAS;oFACX;oFACA,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE,KAAK,IAAI;;sGAElD,2BAAC;4FACC,MAAM;4FACN,OAAO;gGACL,UAAU;gGACV,OAAO,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GAAG,YAAY;gGACjD,YAAY;gGACZ,YAAY;gGACZ,UAAU;4FACZ;4FACA,WAAW,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GAAG,oBAAoB;sGAE5D,KAAK,IAAI;;;;;;wFAEX,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,kBAC1B,2BAAC,oBAAa;4FACZ,OAAO;gGACL,UAAU;gGACV,OAAO;gGACP,YAAY;gGACZ,YAAY;4FACd;4FACA,WAAU;;;;;;;;;;;;gFAMf,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,kBAC1B,2BAAC;oFAAI,OAAO;wFACV,YAAY;wFACZ,OAAO;wFACP,SAAS;wFACT,cAAc;wFACd,UAAU;wFACV,YAAY;wFACZ,SAAS;wFACT,YAAY;wFACZ,KAAK;wFACL,WAAW;oFACb;;sGACE,2BAAC,0BAAmB;4FAAC,OAAO;gGAAE,UAAU;4FAAG;;;;;;wFAAK;;;;;;;gFAMnD,oBAAoB,KAAK,EAAE,kBAC1B,2BAAC;oFAAI,OAAO;wFACV,YAAY;wFACZ,SAAS;wFACT,cAAc;wFACd,SAAS;wFACT,YAAY;wFACZ,KAAK;oFACP;;sGACE,2BAAC,UAAI;4FAAC,MAAK;;;;;;sGACX,2BAAC;4FAAK,OAAO;gGAAE,UAAU;gGAAI,OAAO;4FAAO;sGAAG;;;;;;;;;;;;;;;;;;sFAMpD,2BAAC;4EAAI,OAAO;gFACV,YAAY,KAAK,SAAS,GACtB,8CACA;gFACJ,OAAO;gFACP,SAAS;gFACT,cAAc;gFACd,UAAU;gFACV,YAAY;gFACZ,SAAS;gFACT,YAAY;gFACZ,KAAK;gFACL,WAAW,KAAK,SAAS,GACrB,sCACA;gFACJ,UAAU;4EACZ;sFACG,KAAK,SAAS,iBACb;;kGACE,2BAAC,oBAAa;wFAAC,OAAO;4FAAE,UAAU;wFAAG;;;;;;oFAAK;;6GAI5C;;kGACE,2BAAC,mBAAY;wFAAC,OAAO;4FAAE,UAAU;wFAAG;;;;;;oFAAK;;;;;;;;;;;;;;8EAQjD,2BAAC,UAAI;oEAAC,OAAM;oEAAS,KAAK;oEAAI,OAAO;wEAAE,WAAW;oEAAE;;sFAClD,2BAAC,aAAO;4EAAC,OAAO,CAAC,MAAM,EAAE,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc,CAAC,SAAS,CAAC;sFACzE,cAAA,2BAAC,UAAI;gFAAC,OAAM;gFAAS,KAAK;;kGACxB,2BAAC;wFAAI,OAAO;4FACV,YAAY;4FACZ,SAAS;4FACT,cAAc;4FACd,SAAS;4FACT,YAAY;wFACd;kGACE,cAAA,2BAAC,0BAAmB;4FAAC,OAAO;gGAAE,OAAO;gGAAW,UAAU;4FAAG;;;;;;;;;;;kGAE/D,2BAAC;wFAAK,OAAO;4FAAE,UAAU;4FAAI,OAAO;4FAAW,YAAY;wFAAI;;4FAAG;4FAC3D,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;sFAKvD,2BAAC,aAAO;4EAAC,OAAO,CAAC,MAAM,EAAE,KAAK,WAAW,CAAC,CAAC,CAAC;sFAC1C,cAAA,2BAAC,UAAI;gFAAC,OAAM;gFAAS,KAAK;;kGACxB,2BAAC;wFAAI,OAAO;4FACV,YAAY;4FACZ,SAAS;4FACT,cAAc;4FACd,SAAS;4FACT,YAAY;wFACd;kGACE,cAAA,2BAAC,mBAAY;4FAAC,OAAO;gGAAE,OAAO;gGAAW,UAAU;4FAAG;;;;;;;;;;;kGAExD,2BAAC;wFAAK,OAAO;4FAAE,UAAU;4FAAI,OAAO;4FAAW,YAAY;wFAAI;;4FAC5D,KAAK,WAAW;4FAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAS9B,2BAAC,SAAG;wDAAC,MAAK;kEACR,cAAA,2BAAC;4DAAI,OAAO;gEACV,SAAS;gEACT,qBAAqB;gEACrB,KAAK;gEACL,UAAU;4DACZ;;8EAEE,2BAAC;oEAAI,OAAO;wEACV,YAAY;wEACZ,QAAQ;wEACR,cAAc;wEACd,SAAS;wEACT,WAAW;wEACX,YAAY;wEACZ,QAAQ;wEACR,UAAU;wEACV,UAAU;oEACZ;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wEAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oEACpC;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wEAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oEACpC;;sFACE,2BAAC;4EAAI,OAAO;gFACV,UAAU;gFACV,KAAK;gFACL,OAAO;gFACP,OAAO;gFACP,QAAQ;gFACR,YAAY;gFACZ,cAAc;gFACd,SAAS;4EACX;;;;;;sFACA,2BAAC,UAAI;4EAAC,QAAQ;4EAAC,OAAM;4EAAS,KAAK;;8FACjC,2BAAC;oFAAI,OAAO;wFACV,YAAY;wFACZ,cAAc;wFACd,OAAO;wFACP,QAAQ;wFACR,SAAS;wFACT,YAAY;wFACZ,gBAAgB;wFAChB,cAAc;oFAChB;8FACE,cAAA,2BAAC,kBAAW;wFAAC,OAAO;4FAAE,OAAO;4FAAS,UAAU;wFAAG;;;;;;;;;;;8FAErD,2BAAC;oFAAK,MAAM;oFAAC,OAAO;wFAAE,UAAU;wFAAI,OAAO;wFAAW,YAAY;oFAAE;8FACjE,EAAA,cAAA,KAAK,KAAK,cAAV,kCAAA,YAAY,QAAQ,KAAI;;;;;;8FAE3B,2BAAC;oFAAK,OAAO;wFAAE,UAAU;wFAAI,OAAO;wFAAQ,YAAY;oFAAI;8FAAG;;;;;;;;;;;;;;;;;;8EAKnE,2BAAC;oEAAI,OAAO;wEACV,YAAY;wEACZ,QAAQ;wEACR,cAAc;wEACd,SAAS;wEACT,WAAW;wEACX,YAAY;wEACZ,QAAQ;wEACR,UAAU;wEACV,UAAU;oEACZ;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wEAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oEACpC;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wEAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oEACpC;;sFACE,2BAAC;4EAAI,OAAO;gFACV,UAAU;gFACV,KAAK;gFACL,OAAO;gFACP,OAAO;gFACP,QAAQ;gFACR,YAAY;gFACZ,cAAc;gFACd,SAAS;4EACX;;;;;;sFACA,2BAAC,UAAI;4EAAC,QAAQ;4EAAC,OAAM;4EAAS,KAAK;;8FACjC,2BAAC;oFAAI,OAAO;wFACV,YAAY;wFACZ,cAAc;wFACd,OAAO;wFACP,QAAQ;wFACR,SAAS;wFACT,YAAY;wFACZ,gBAAgB;wFAChB,cAAc;oFAChB;8FACE,cAAA,2BAAC,mBAAY;wFAAC,OAAO;4FAAE,OAAO;4FAAS,UAAU;wFAAG;;;;;;;;;;;8FAEtD,2BAAC;oFAAK,MAAM;oFAAC,OAAO;wFAAE,UAAU;wFAAI,OAAO;wFAAW,YAAY;oFAAE;8FACjE,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,SAAS,KAAI;;;;;;8FAE5B,2BAAC;oFAAK,OAAO;wFAAE,UAAU;wFAAI,OAAO;wFAAQ,YAAY;oFAAI;8FAAG;;;;;;;;;;;;;;;;;;8EAKnE,2BAAC;oEAAI,OAAO;wEACV,YAAY;wEACZ,QAAQ;wEACR,cAAc;wEACd,SAAS;wEACT,WAAW;wEACX,YAAY;wEACZ,QAAQ;wEACR,UAAU;wEACV,UAAU;oEACZ;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wEAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oEACpC;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wEAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oEACpC;;sFACE,2BAAC;4EAAI,OAAO;gFACV,UAAU;gFACV,KAAK;gFACL,OAAO;gFACP,OAAO;gFACP,QAAQ;gFACR,YAAY;gFACZ,cAAc;gFACd,SAAS;4EACX;;;;;;sFACA,2BAAC,UAAI;4EAAC,QAAQ;4EAAC,OAAM;4EAAS,KAAK;;8FACjC,2BAAC;oFAAI,OAAO;wFACV,YAAY;wFACZ,cAAc;wFACd,OAAO;wFACP,QAAQ;wFACR,SAAS;wFACT,YAAY;wFACZ,gBAAgB;wFAChB,cAAc;oFAChB;8FACE,cAAA,2BAAC,gCAAyB;wFAAC,OAAO;4FAAE,OAAO;4FAAS,UAAU;wFAAG;;;;;;;;;;;8FAEnE,2BAAC;oFAAK,MAAM;oFAAC,OAAO;wFAAE,UAAU;wFAAI,OAAO;wFAAW,YAAY;oFAAE;8FACjE,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,QAAQ,KAAI;;;;;;8FAE3B,2BAAC;oFAAK,OAAO;wFAAE,UAAU;wFAAI,OAAO;wFAAQ,YAAY;oFAAI;8FAAG;;;;;;;;;;;;;;;;;;8EAKnE,2BAAC;oEAAI,OAAO;wEACV,YAAY;wEACZ,QAAQ;wEACR,cAAc;wEACd,SAAS;wEACT,WAAW;wEACX,YAAY;wEACZ,QAAQ;wEACR,UAAU;wEACV,UAAU;oEACZ;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wEAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oEACpC;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wEAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oEACpC;;sFACE,2BAAC;4EAAI,OAAO;gFACV,UAAU;gFACV,KAAK;gFACL,OAAO;gFACP,OAAO;gFACP,QAAQ;gFACR,YAAY;gFACZ,cAAc;gFACd,SAAS;4EACX;;;;;;sFACA,2BAAC,UAAI;4EAAC,QAAQ;4EAAC,OAAM;4EAAS,KAAK;;8FACjC,2BAAC;oFAAI,OAAO;wFACV,YAAY;wFACZ,cAAc;wFACd,OAAO;wFACP,QAAQ;wFACR,SAAS;wFACT,YAAY;wFACZ,gBAAgB;wFAChB,cAAc;oFAChB;8FACE,cAAA,2BAAC,gCAAyB;wFAAC,OAAO;4FAAE,OAAO;4FAAS,UAAU;wFAAG;;;;;;;;;;;8FAEnE,2BAAC;oFAAK,MAAM;oFAAC,OAAO;wFAAE,UAAU;wFAAI,OAAO;wFAAW,YAAY;oFAAE;8FACjE,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,OAAO,KAAI;;;;;;8FAE1B,2BAAC;oFAAK,OAAO;wFAAE,UAAU;wFAAI,OAAO;wFAAQ,YAAY;oFAAI;8FAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAc3F;eAnhBM;;oBAOsC,aAAQ;;;iBAP9C;gBAqhBN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDpjBD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;YAAS;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,iDAAgD;YAAC;SAAgD;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;IAAA;;AACh6B"}