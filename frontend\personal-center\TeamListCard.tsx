import React from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON>, 
  Row, 
  Col, 
  Tag, 
  Typography, 
  Tooltip, 
  List,
  Flex,
  Space
} from "antd";
import {
  CalendarOutlined,
  CarOutlined,
  TeamOutlined,
  UserOutlined,
  WarningOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";

const { Text } = Typography;

interface TeamStats {
  vehicles: number;
  personnel: number;
  expiring: number;
  overdue: number;
}

interface Team {
  id: string;
  name: string;
  lastLogin: string;
  createdAt: string; 
  role: string;
  memberCount: number;
  stats: TeamStats;
}

const TeamListCard: React.FC = () => {
  const teams: Team[] = [
    {
      id: "1",
      name: "运输车队管理员",
      lastLogin: "2025/7/25 18:30:45",
      createdAt: "2025/1/15 09:00:00",
      role: "管理员",
      memberCount: 15,
      stats: {
        vehicles: 120000,
        personnel: 120000,
        expiring: 120000,
        overdue: 120000,
      },
    },
    {
      id: "2",
      name: "维修团队",
      lastLogin: "2025/7/24 10:20:30",
      createdAt: "2025/2/20 14:30:00",
      role: "成员",
      memberCount: 8,
      stats: {
        vehicles: 8,
        personnel: 3,
        expiring: 0,
        overdue: 0,
      },
    },
    {
      id: "3",
      name: "安全检查组",
      lastLogin: "2025/7/20 09:15:00",
      createdAt: "2025/3/10 10:00:00",
      role: "成员",
      memberCount: 6,
      stats: {
        vehicles: 0,
        personnel: 6,
        expiring: 3,
        overdue: 2,
      },
    },
    {
      id: "4",
      name: "物流调度组",
      lastLogin: "2025/7/18 14:45:22",
      createdAt: "2025/4/5 11:30:00",
      role: "成员",
      memberCount: 12,
      stats: {
        vehicles: 20,
        personnel: 5,
        expiring: 1,
        overdue: 1,
      },
    },
  ];

  return (
    <Card
      className="dashboard-card"
      style={{
        borderRadius: 12,
        boxShadow: "0 4px 12px rgba(0,0,0,0.05)",
        border: "none",
        background: "linear-gradient(145deg, #ffffff, #f5f8ff)",
      }}
      title={
        <Flex justify="space-between" align="center">
          <Text strong>团队列表</Text>
        </Flex>
      }
    >
      <List
        grid={{ gutter: 16, column: 2 }}
        dataSource={teams}
        renderItem={item => (
          <List.Item>
            <Card
              className="team-item"
              style={{
                background: "#fff",
                borderRadius: 8,
                borderLeft: `4px solid ${item.id === "1" ? "#1890ff" : "#52c41a"}`,
                boxShadow: "0 1px 3px rgba(0,0,0,0.03)",
                height: "100%",
              }}
              bodyStyle={{
                padding: "16px",
              }}
            >
              <Card.Meta
                avatar={
                  <div
                    style={{
                      width: 48,
                      height: 48,
                      borderRadius: "50%",
                      background: item.id === "1" ? "#1890ff" : "#52c41a",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      color: "white",
                      fontWeight: "bold",
                      fontSize: 20,
                    }}
                  >
                    {item.name.substring(0, 1)}
                  </div>
                }
                title={
                  <Flex align="center" wrap="wrap" gap={8}>
                    <Text strong>{item.name}</Text>
                  </Flex>
                }
                description={
                  <Space size={16} wrap>
                    <Tag
                      color={item.role === "管理员" ? "blue" : "green"}
                      style={{ fontSize: 12 }}
                    >
                      {item.role}
                    </Tag>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      <TeamOutlined style={{ marginRight: 4, fontSize: 12 }} />
                      {item.memberCount}人
                    </Text>
                  </Space>
                }
              />

              <Card
                style={{
                  background: "#f9f9f9",
                  borderRadius: 6,
                  margin: "16px 0",
                }}
                bodyStyle={{ padding: "12px" }}
              >
                <Space direction="vertical" size={8} style={{ width: "100%" }}>
                  <Flex justify="space-between">
                    <Space size={8}>
                      <ClockCircleOutlined style={{ fontSize: 14, color: "#1890ff" }} />
                      <Text type="secondary" style={{ fontSize: 13 }}>创建时间</Text>
                    </Space>
                    <Text style={{ fontSize: 13 }}>{item.createdAt}</Text>
                  </Flex>
                  <Flex justify="space-between">
                    <Space size={8}>
                      <CalendarOutlined style={{ fontSize: 14, color: "#52c41a" }} />
                      <Text type="secondary" style={{ fontSize: 13 }}>最后登录</Text>
                    </Space>
                    <Text style={{ fontSize: 13 }}>{item.lastLogin}</Text>
                  </Flex>
                </Space>
              </Card>

              <Flex justify="space-between" gap={8} style={{ marginBottom: 16 }}>
                <Tooltip title="车辆资源">
                  <Card
                    style={{
                      background: "#f9f9f9",
                      borderRadius: 6,
                      flex: 1,
                    }}
                    bodyStyle={{ padding: "8px", textAlign: "center" }}
                  >
                    <CarOutlined style={{ color: "#1890ff", fontSize: 16 }} />
                    <Text strong style={{ color: "#1890ff", fontSize: 16, display: "block" }}>
                      {item.stats.vehicles}
                    </Text>
                  </Card>
                </Tooltip>

                <Tooltip title="人员资源">
                  <Card
                    style={{
                      background: "#f9f9f9",
                      borderRadius: 6,
                      flex: 1,
                    }}
                    bodyStyle={{ padding: "8px", textAlign: "center" }}
                  >
                    <UserOutlined style={{ color: "#52c41a", fontSize: 16 }} />
                    <Text strong style={{ color: "#52c41a", fontSize: 16, display: "block" }}>
                      {item.stats.personnel}
                    </Text>
                  </Card>
                </Tooltip>

                <Tooltip title="临期事项">
                  <Card
                    style={{
                      background: "#fff9e6",
                      borderRadius: 6,
                      flex: 1,
                    }}
                    bodyStyle={{ padding: "8px", textAlign: "center" }}
                  >
                    <WarningOutlined style={{ color: "#faad14", fontSize: 16 }} />
                    <Text strong style={{ color: "#faad14", fontSize: 16, display: "block" }}>
                      {item.stats.expiring}
                    </Text>
                  </Card>
                </Tooltip>

                <Tooltip title="逾期事项">
                  <Card
                    style={{
                      background: "#fff2f0",
                      borderRadius: 6,
                      flex: 1,
                    }}
                    bodyStyle={{ padding: "8px", textAlign: "center" }}
                  >
                    <WarningOutlined style={{ color: "#ff4d4f", fontSize: 16 }} />
                    <Text strong style={{ color: "#ff4d4f", fontSize: 16, display: "block" }}>
                      {item.stats.overdue}
                    </Text>
                  </Card>
                </Tooltip>
              </Flex>

              <Space.Compact block>
                <Button block style={{ fontWeight: 500 }}>
                  进入
                </Button>
              </Space.Compact>
            </Card>
          </List.Item>
        )}
      />
    </Card>
  );
};

export default TeamListCard;