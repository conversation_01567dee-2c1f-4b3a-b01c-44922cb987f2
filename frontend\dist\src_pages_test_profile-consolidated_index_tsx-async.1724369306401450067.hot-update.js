globalThis.makoModuleHotUpdate('src/pages/test/profile-consolidated/index.tsx', {
    modules: {
        "src/pages/test/profile-consolidated/TeamListCard.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _team = __mako_require__("src/services/team.ts");
            var _services = __mako_require__("src/services/index.ts");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text, Title } = _antd.Typography;
            const TeamListCard = ()=>{
                _s();
                // 团队列表状态管理
                const [teams, setTeams] = (0, _react.useState)([]);
                const [loading, setLoading] = (0, _react.useState)(true);
                const [error, setError] = (0, _react.useState)(null);
                const [switchingTeamId, setSwitchingTeamId] = (0, _react.useState)(null);
                const { initialState, setInitialState } = (0, _max.useModel)('@@initialState');
                const currentTeam = initialState === null || initialState === void 0 ? void 0 : initialState.currentTeam;
                // 获取团队列表数据
                (0, _react.useEffect)(()=>{
                    const fetchTeams = async ()=>{
                        try {
                            setLoading(true);
                            setError(null);
                            const teamsData = await _team.TeamService.getUserTeamsWithStats();
                            setTeams(teamsData);
                        } catch (error) {
                            console.error('获取团队列表失败:', error);
                            setError('获取团队列表失败');
                        } finally{
                            setLoading(false);
                        }
                    };
                    fetchTeams();
                }, []);
                // 团队切换处理函数
                const handleTeamSwitch = async (teamId, teamName)=>{
                    if (teamId === (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id)) {
                        _antd.message.info('您已经在当前团队中');
                        return;
                    }
                    try {
                        setSwitchingTeamId(teamId);
                        const response = await _services.AuthService.selectTeam({
                            teamId
                        });
                        // 检查后端返回的团队选择成功标识
                        if (response.teamSelectionSuccess && response.team && response.team.id === teamId) {
                            _antd.message.success(`已切换到团队：${teamName}`);
                            // 同步更新 initialState，等待更新完成后再跳转
                            if ((initialState === null || initialState === void 0 ? void 0 : initialState.fetchTeamInfo) && (initialState === null || initialState === void 0 ? void 0 : initialState.fetchUserInfo) && setInitialState) try {
                                const [currentUser, currentTeam] = await Promise.all([
                                    initialState.fetchUserInfo(),
                                    initialState.fetchTeamInfo()
                                ]);
                                // 确保团队信息已正确获取
                                if (currentTeam && currentTeam.id === teamId) {
                                    await setInitialState({
                                        ...initialState,
                                        currentUser,
                                        currentTeam
                                    });
                                    // 等待 initialState 更新完成后再跳转到仪表盘
                                    setTimeout(()=>{
                                        _max.history.push('/dashboard');
                                    }, 100);
                                } else {
                                    console.error('获取的团队信息与选择的团队不匹配');
                                    _antd.message.error('团队切换失败，请重试');
                                }
                            } catch (error) {
                                console.error('更新 initialState 失败:', error);
                                _antd.message.error('团队切换失败，请重试');
                            }
                            else // 如果没有 initialState 相关方法，直接跳转
                            _max.history.push('/dashboard');
                        } else {
                            console.error('团队切换响应异常，未返回正确的团队信息');
                            _antd.message.error('团队切换失败，请重试');
                        }
                    } catch (error) {
                        console.error('团队切换失败:', error);
                        _antd.message.error('团队切换失败');
                    } finally{
                        setSwitchingTeamId(null);
                    }
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    className: "dashboard-card",
                    style: {
                        borderRadius: 12,
                        boxShadow: "0 4px 12px rgba(0,0,0,0.05)",
                        border: "none",
                        background: "linear-gradient(145deg, #ffffff, #f5f8ff)"
                    },
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                        justify: "space-between",
                        align: "center",
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                            level: 5,
                            style: {
                                margin: 0
                            },
                            children: "团队列表"
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                            lineNumber: 129,
                            columnNumber: 11
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                        lineNumber: 128,
                        columnNumber: 9
                    }, void 0),
                    children: error ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                        message: "团队列表加载失败",
                        description: error,
                        type: "error",
                        showIcon: true,
                        style: {
                            marginBottom: 16
                        }
                    }, void 0, false, {
                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                        lineNumber: 134,
                        columnNumber: 9
                    }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                        spinning: loading,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                            dataSource: teams,
                            renderItem: (item)=>{
                                var _item_stats, _item_stats1, _item_stats2, _item_stats3;
                                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        className: "team-item",
                                        style: {
                                            background: "#fff",
                                            borderRadius: 12,
                                            boxShadow: "0 2px 8px rgba(0,0,0,0.06)",
                                            width: "100%",
                                            borderLeft: `4px solid ${item.isCreator ? "#1890ff" : "#52c41a"}`,
                                            transition: "all 0.3s ease",
                                            border: "1px solid #f0f0f0"
                                        },
                                        bodyStyle: {
                                            padding: "20px 24px"
                                        },
                                        hoverable: true,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                            justify: "space-between",
                                            align: "top",
                                            gutter: [
                                                20,
                                                0
                                            ],
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                    flex: "1",
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        vertical: true,
                                                        gap: 12,
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                align: "center",
                                                                gap: 12,
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        strong: true,
                                                                        style: {
                                                                            fontSize: 18,
                                                                            cursor: 'pointer',
                                                                            color: (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) === item.id ? '#1890ff' : '#262626',
                                                                            textDecoration: (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) === item.id ? 'none' : 'underline',
                                                                            lineHeight: 1.2
                                                                        },
                                                                        onClick: ()=>handleTeamSwitch(item.id, item.name),
                                                                        children: item.name
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 168,
                                                                        columnNumber: 27
                                                                    }, void 0),
                                                                    (currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id) === item.id && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                                        color: "blue",
                                                                        style: {
                                                                            fontSize: 12,
                                                                            fontWeight: 500
                                                                        },
                                                                        children: "当前团队"
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 182,
                                                                        columnNumber: 29
                                                                    }, void 0),
                                                                    switchingTeamId === item.id && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                                                        size: "small"
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 187,
                                                                        columnNumber: 29
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 167,
                                                                columnNumber: 25
                                                            }, void 0),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                align: "center",
                                                                gap: 24,
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                                        title: `创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`,
                                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                            align: "center",
                                                                            gap: 6,
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                                                                    style: {
                                                                                        color: "#8c8c8c",
                                                                                        fontSize: 14
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 195,
                                                                                    columnNumber: 31
                                                                                }, void 0),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                    type: "secondary",
                                                                                    style: {
                                                                                        fontSize: 13
                                                                                    },
                                                                                    children: [
                                                                                        "创建于 ",
                                                                                        new Date(item.createdAt).toLocaleDateString('zh-CN')
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 196,
                                                                                    columnNumber: 31
                                                                                }, void 0)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 194,
                                                                            columnNumber: 29
                                                                        }, void 0)
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 193,
                                                                        columnNumber: 27
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                                        title: `团队成员: ${item.memberCount}人`,
                                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                            align: "center",
                                                                            gap: 6,
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                                                    style: {
                                                                                        color: "#8c8c8c",
                                                                                        fontSize: 14
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 204,
                                                                                    columnNumber: 31
                                                                                }, void 0),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                    type: "secondary",
                                                                                    style: {
                                                                                        fontSize: 13
                                                                                    },
                                                                                    children: [
                                                                                        item.memberCount,
                                                                                        " 名成员"
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 205,
                                                                                    columnNumber: 31
                                                                                }, void 0)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 203,
                                                                            columnNumber: 29
                                                                        }, void 0)
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 202,
                                                                        columnNumber: 27
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 192,
                                                                columnNumber: 25
                                                            }, void 0),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                                    color: item.isCreator ? "blue" : "green",
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserSwitchOutlined, {}, void 0, false, {
                                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                        lineNumber: 216,
                                                                        columnNumber: 35
                                                                    }, void 0),
                                                                    style: {
                                                                        fontSize: 12,
                                                                        fontWeight: 500,
                                                                        display: 'inline-flex',
                                                                        alignItems: 'center',
                                                                        height: 26,
                                                                        paddingLeft: 10,
                                                                        paddingRight: 10,
                                                                        borderRadius: 6
                                                                    },
                                                                    children: item.isCreator ? "团队管理员" : "团队成员"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                    lineNumber: 214,
                                                                    columnNumber: 27
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 213,
                                                                columnNumber: 25
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                        lineNumber: 165,
                                                        columnNumber: 23
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                    lineNumber: 164,
                                                    columnNumber: 21
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                    flex: "none",
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        gap: 12,
                                                        wrap: "wrap",
                                                        justify: "end",
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                                size: "small",
                                                                style: {
                                                                    background: '#f0f7ff',
                                                                    borderColor: '#d9e8ff',
                                                                    borderRadius: 8,
                                                                    minWidth: 90,
                                                                    boxShadow: '0 1px 4px rgba(0,0,0,0.06)'
                                                                },
                                                                bodyStyle: {
                                                                    padding: '8px 12px',
                                                                    textAlign: 'center'
                                                                },
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                    vertical: true,
                                                                    align: "center",
                                                                    gap: 4,
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                            align: "center",
                                                                            gap: 6,
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CarOutlined, {
                                                                                    style: {
                                                                                        color: "#1890ff",
                                                                                        fontSize: 18
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 251,
                                                                                    columnNumber: 31
                                                                                }, void 0),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                    strong: true,
                                                                                    style: {
                                                                                        fontSize: 16,
                                                                                        color: '#1890ff'
                                                                                    },
                                                                                    children: ((_item_stats = item.stats) === null || _item_stats === void 0 ? void 0 : _item_stats.vehicles) || 0
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 252,
                                                                                    columnNumber: 31
                                                                                }, void 0)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 250,
                                                                            columnNumber: 29
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 12,
                                                                                color: '#666',
                                                                                lineHeight: 1
                                                                            },
                                                                            children: "车辆资源"
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 254,
                                                                            columnNumber: 29
                                                                        }, void 0)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                    lineNumber: 249,
                                                                    columnNumber: 27
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 238,
                                                                columnNumber: 25
                                                            }, void 0),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                                size: "small",
                                                                style: {
                                                                    background: '#f6ffed',
                                                                    borderColor: '#d1f0be',
                                                                    borderRadius: 8,
                                                                    minWidth: 90,
                                                                    boxShadow: '0 1px 4px rgba(0,0,0,0.06)'
                                                                },
                                                                bodyStyle: {
                                                                    padding: '8px 12px',
                                                                    textAlign: 'center'
                                                                },
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                    vertical: true,
                                                                    align: "center",
                                                                    gap: 4,
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                            align: "center",
                                                                            gap: 6,
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                                                    style: {
                                                                                        color: "#52c41a",
                                                                                        fontSize: 18
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 272,
                                                                                    columnNumber: 31
                                                                                }, void 0),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                    strong: true,
                                                                                    style: {
                                                                                        fontSize: 16,
                                                                                        color: '#52c41a'
                                                                                    },
                                                                                    children: ((_item_stats1 = item.stats) === null || _item_stats1 === void 0 ? void 0 : _item_stats1.personnel) || 0
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 273,
                                                                                    columnNumber: 31
                                                                                }, void 0)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 271,
                                                                            columnNumber: 29
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 12,
                                                                                color: '#666',
                                                                                lineHeight: 1
                                                                            },
                                                                            children: "人员资源"
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 275,
                                                                            columnNumber: 29
                                                                        }, void 0)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                    lineNumber: 270,
                                                                    columnNumber: 27
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 259,
                                                                columnNumber: 25
                                                            }, void 0),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                                size: "small",
                                                                style: {
                                                                    background: '#fff7e6',
                                                                    borderColor: '#ffdfa6',
                                                                    borderRadius: 8,
                                                                    minWidth: 90,
                                                                    boxShadow: '0 1px 4px rgba(0,0,0,0.06)'
                                                                },
                                                                bodyStyle: {
                                                                    padding: '8px 12px',
                                                                    textAlign: 'center'
                                                                },
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                    vertical: true,
                                                                    align: "center",
                                                                    gap: 4,
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                            align: "center",
                                                                            gap: 6,
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.WarningOutlined, {
                                                                                    style: {
                                                                                        color: "#faad14",
                                                                                        fontSize: 18
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 293,
                                                                                    columnNumber: 31
                                                                                }, void 0),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                    strong: true,
                                                                                    style: {
                                                                                        fontSize: 16,
                                                                                        color: '#faad14'
                                                                                    },
                                                                                    children: ((_item_stats2 = item.stats) === null || _item_stats2 === void 0 ? void 0 : _item_stats2.expiring) || 0
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 294,
                                                                                    columnNumber: 31
                                                                                }, void 0)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 292,
                                                                            columnNumber: 29
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 12,
                                                                                color: '#666',
                                                                                lineHeight: 1
                                                                            },
                                                                            children: "临期事项"
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 296,
                                                                            columnNumber: 29
                                                                        }, void 0)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                    lineNumber: 291,
                                                                    columnNumber: 27
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 280,
                                                                columnNumber: 25
                                                            }, void 0),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                                size: "small",
                                                                style: {
                                                                    background: '#fff1f0',
                                                                    borderColor: '#ffccc7',
                                                                    borderRadius: 8,
                                                                    minWidth: 90,
                                                                    boxShadow: '0 1px 4px rgba(0,0,0,0.06)'
                                                                },
                                                                bodyStyle: {
                                                                    padding: '8px 12px',
                                                                    textAlign: 'center'
                                                                },
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                    vertical: true,
                                                                    align: "center",
                                                                    gap: 4,
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                            align: "center",
                                                                            gap: 6,
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.WarningOutlined, {
                                                                                    style: {
                                                                                        color: "#ff4d4f",
                                                                                        fontSize: 18
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 314,
                                                                                    columnNumber: 31
                                                                                }, void 0),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                    strong: true,
                                                                                    style: {
                                                                                        fontSize: 16,
                                                                                        color: '#ff4d4f'
                                                                                    },
                                                                                    children: ((_item_stats3 = item.stats) === null || _item_stats3 === void 0 ? void 0 : _item_stats3.overdue) || 0
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                                    lineNumber: 315,
                                                                                    columnNumber: 31
                                                                                }, void 0)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 313,
                                                                            columnNumber: 29
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 12,
                                                                                color: '#666',
                                                                                lineHeight: 1
                                                                            },
                                                                            children: "逾期事项"
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                            lineNumber: 317,
                                                                            columnNumber: 29
                                                                        }, void 0)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                    lineNumber: 312,
                                                                    columnNumber: 27
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                                lineNumber: 301,
                                                                columnNumber: 25
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                        lineNumber: 236,
                                                        columnNumber: 23
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                                    lineNumber: 235,
                                                    columnNumber: 21
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                            lineNumber: 162,
                                            columnNumber: 19
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                        lineNumber: 147,
                                        columnNumber: 17
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                                    lineNumber: 146,
                                    columnNumber: 15
                                }, void 0);
                            }
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                            lineNumber: 143,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                        lineNumber: 142,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/test/profile-consolidated/TeamListCard.tsx",
                    lineNumber: 119,
                    columnNumber: 5
                }, this);
            };
            _s(TeamListCard, "4DmFk5I0DN4oNd3BYpKpdN0lgTI=", false, function() {
                return [
                    _max.useModel
                ];
            });
            _c = TeamListCard;
            var _default = TeamListCard;
            var _c;
            $RefreshReg$(_c, "TeamListCard");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '12641783137451556753';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "common",
            "p__subscription__index"
        ],
        "src/pages/team/detail/index.tsx": [
            "common",
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "p__team__index"
        ],
        "src/pages/test/profile-consolidated/index.tsx": [
            "src/pages/test/profile-consolidated/index.tsx"
        ],
        "src/pages/user/index.tsx": [
            "common",
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_test_profile-consolidated_index_tsx-async.1724369306401450067.hot-update.js.map