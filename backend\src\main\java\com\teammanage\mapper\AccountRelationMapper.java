package com.teammanage.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teammanage.entity.AccountRelation;

/**
 * 账号邀请关系Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface AccountRelationMapper extends BaseMapper<AccountRelation> {

    /**
     * 根据邀请人ID查询邀请关系
     * 
     * @param invitedBy 邀请人ID
     * @return 邀请关系列表
     */
    @Select("SELECT * FROM account_relation WHERE invited_by = #{invitedBy} AND is_active = 1 AND is_deleted = 0")
    List<AccountRelation> findByInvitedBy(@Param("invitedBy") Long invitedBy);

    /**
     * 根据被邀请人ID查询邀请关系
     * 
     * @param accountId 被邀请人ID
     * @return 邀请关系列表
     */
    @Select("SELECT * FROM account_relation WHERE account_id = #{accountId} AND is_active = 1 AND is_deleted = 0")
    List<AccountRelation> findByAccountId(@Param("accountId") Long accountId);

    /**
     * 检查邀请关系是否存在
     * 
     * @param accountId 被邀请人ID
     * @param invitedBy 邀请人ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(1) FROM account_relation WHERE account_id = #{accountId} AND invited_by = #{invitedBy} AND is_deleted = 0")
    boolean existsRelation(@Param("accountId") Long accountId, @Param("invitedBy") Long invitedBy);

}
