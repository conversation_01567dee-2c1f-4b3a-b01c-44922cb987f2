package com.teammanage.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teammanage.entity.Account;

/**
 * 用户账户Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface AccountMapper extends BaseMapper<Account> {

    /**
     * 根据邮箱查询用户
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    @Select("SELECT * FROM account WHERE email = #{email}")
    Account findByEmail(@Param("email") String email);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @return 是否存在
     */
    @Select("SELECT COUNT(1) FROM account WHERE email = #{email}")
    boolean existsByEmail(@Param("email") String email);

    /**
     * 根据邮箱模糊搜索用户（排除指定用户）
     *
     * @param email 邮箱关键词
     * @param excludeUserId 要排除的用户ID
     * @return 用户列表
     */
    @Select("SELECT id, email, name, created_at, updated_at FROM account WHERE email LIKE CONCAT('%', #{email}, '%') AND id != #{excludeUserId} LIMIT 20")
    List<Account> searchByEmail(@Param("email") String email, @Param("excludeUserId") Long excludeUserId);

}
