
import type { Settings as LayoutSettings } from '@ant-design/pro-components';
import { SettingDrawer } from '@ant-design/pro-components';
import type { RequestConfig, RunTimeLayoutConfig } from '@umijs/max';
import { history } from '@umijs/max';
import {
  AvatarDropdown,
  AvatarName,
  Footer,
  Question,
  ErrorBoundary,
  GlobalLoading,
} from '@/components';
import { AuthService, UserService, TeamService } from '@/services';
import type { UserProfileResponse, TeamDetailResponse } from '@/types/api';
import defaultSettings from '../config/defaultSettings';
import { errorConfig } from './requestErrorConfig';
import '@ant-design/v5-patch-for-react-19';
import { CrownTwoTone } from '@ant-design/icons';

const isDev = process.env.NODE_ENV === 'development';
const loginPath = '/user/login';
const teamSelectPath = '/test/profile-consolidated';
const teamCreatePath = '/team/create';

// 不需要认证的路径
const noAuthPaths = [
  loginPath,
  teamSelectPath,
  '/user/team-select', // 保持兼容性，因为有重定向
  teamCreatePath,
  '/404',
];

/**
 * 全局初始状态
 */
export interface InitialState {
  settings?: Partial<LayoutSettings>;
  currentUser?: UserProfileResponse;
  currentTeam?: TeamDetailResponse;
  loading?: boolean;
  fetchUserInfo?: () => Promise<UserProfileResponse | undefined>;
  fetchTeamInfo?: () => Promise<TeamDetailResponse | undefined>;
}

/**
 * @see https://umijs.org/docs/api/runtime-config#getinitialstate
 * */
export async function getInitialState(): Promise<InitialState> {
  const fetchUserInfo = async (): Promise<UserProfileResponse | undefined> => {
    try {
      if (!AuthService.isLoggedIn()) {
        return undefined;
      }

      const userProfile = await UserService.getUserProfile();
      return userProfile;
    } catch (error: any) {
      console.error('获取用户信息失败:', error);
      // 只有在确实是认证错误时才清除 Token
      if (error?.message?.includes('401') || error?.message?.includes('未认证')) {
        AuthService.clearToken();
      }
      return undefined;
    }
  };

  const fetchTeamInfo = async (): Promise<TeamDetailResponse | undefined> => {
    try {
      const teamDetail = await TeamService.getCurrentTeamDetail();
      return teamDetail;
    } catch (error) {
      console.error('获取团队信息失败:', error);
      return undefined;
    }
  };

  const { location } = history;
  const currentPath = location.pathname;

  // 如果是不需要认证的页面，直接返回基础状态
  if (noAuthPaths.some(path => currentPath.startsWith(path))) {
    return {
      fetchUserInfo,
      fetchTeamInfo,
      settings: defaultSettings as Partial<LayoutSettings>,
    };
  }

  // 检查认证状态
  if (!AuthService.isLoggedIn()) {
    history.push(loginPath);
    return {
      fetchUserInfo,
      fetchTeamInfo,
      settings: defaultSettings as Partial<LayoutSettings>,
    };
  }

  // 获取用户信息
  const currentUser = await fetchUserInfo();
  if (!currentUser) {
    history.push(loginPath);
    return {
      fetchUserInfo,
      fetchTeamInfo,
      settings: defaultSettings as Partial<LayoutSettings>,
    };
  }

  // 获取团队信息
  const currentTeam = await fetchTeamInfo();

  // 如果没有团队信息且不在团队相关页面，跳转到团队选择页面
  if (!currentTeam && ![teamSelectPath, teamCreatePath].includes(currentPath)) {
    history.push(teamSelectPath);
    return {
      fetchUserInfo,
      fetchTeamInfo,
      currentUser,
      settings: defaultSettings as Partial<LayoutSettings>,
    };
  }

  return {
    fetchUserInfo,
    fetchTeamInfo,
    currentUser,
    currentTeam,
    settings: defaultSettings as Partial<LayoutSettings>,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({
  initialState,
  setInitialState,
}) => {
  return {
    actionsRender: () => [
      <Question key="doc" />,
    ],
    avatarProps: {
      src: <CrownTwoTone />,
      title: <AvatarName />,
      render: (_, avatarChildren) => {
        return <AvatarDropdown menu={true}>{avatarChildren}</AvatarDropdown>;
      },
    },
    waterMarkProps: {
      content: initialState?.currentUser?.name,
    },
    footerRender: () => <Footer />,
    onPageChange: () => {
      const { location } = history;
      const currentPath = location.pathname;

      // 如果是不需要认证的页面，不做处理
      if (noAuthPaths.some(path => currentPath.startsWith(path))) {
        return;
      }

      // 检查登录状态
      if (!AuthService.isLoggedIn()) {
        history.push(loginPath);
        return;
      }

      // 检查团队选择状态 - 使用 initialState.currentTeam
      if (!initialState?.currentTeam && ![teamSelectPath, teamCreatePath].includes(currentPath)) {
        history.push(teamSelectPath);
        return;
      }
    },
    bgLayoutImgList: [],
    links: [],
    menuHeaderRender: undefined,
    childrenRender: (children) => {
      return (
        <ErrorBoundary>
          <GlobalLoading>
            {children}
            {isDev && (
              <SettingDrawer
                disableUrlParams
                enableDarkTheme
                settings={initialState?.settings}
                onSettingChange={(settings) => {
                  setInitialState((preInitialState) => ({
                    ...preInitialState,
                    settings,
                  }));
                }}
              />
            )}
          </GlobalLoading>
        </ErrorBoundary>
      );
    },
    title: initialState?.currentTeam?.name || 'TeamAuth',
    logo: '/logo.svg',
    ...initialState?.settings,
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request: RequestConfig = {
  baseURL: '/api', // 使用代理，相对路径
  ...errorConfig,
};
